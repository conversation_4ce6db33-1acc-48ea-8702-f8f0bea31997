<template>
  <div class="bread-crumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item 
        v-for="(item, index) in items" 
        :key="index" 
        :to="item.to" 
        :class="{ 'clickable': item.to, 'non-clickable': !item.to }"
      >
        {{ item.text }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

interface BreadCrumbItem {
  text: string;
  to?: string | object;
}

const props = defineProps<{
  items: BreadCrumbItem[];
}>();
</script>

<style scoped>
.bread-crumb {
  padding: 10px 0;
  margin-bottom: 10px;
}

.clickable {
  color: #000000;
  cursor: pointer;
}

.non-clickable {
  color: #909399;
}
</style> 