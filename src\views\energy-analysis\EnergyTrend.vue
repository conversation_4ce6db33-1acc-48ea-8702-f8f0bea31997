<template>
  <div class="energy-trend" v-loading="loading" element-loading-text="正在加载趋势数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <div class="card-header">
          <h3>趋势分析控制</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="refreshTrendData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="success" size="small" @click="exportAllData">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="control-group">
            <label class="control-label">分析层级：</label>
            <el-select v-model="analysisLevel" placeholder="选择分析层级" size="small" @change="handleLevelChange">
              <el-option label="一级分项" value="level1" />
              <el-option label="二级分项" value="level2" />
              <el-option label="三级分项" value="level3" />
              <el-option label="全部层级" value="all" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="control-group">
            <label class="control-label">时间范围：</label>
            <el-select v-model="timeRange" placeholder="选择时间范围" size="small" @change="handleTimeRangeChange">
              <el-option label="最近24小时" value="24h" />
              <el-option label="最近7天" value="7d" />
              <el-option label="最近30天" value="30d" />
              <el-option label="最近90天" value="90d" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="control-group">
            <label class="control-label">对比指标：</label>
            <el-select v-model="compareMetric" placeholder="选择对比指标" size="small" @change="handleMetricChange">
              <el-option label="功率对比" value="power" />
              <el-option label="能耗对比" value="consumption" />
              <el-option label="效率对比" value="efficiency" />
              <el-option label="成本对比" value="cost" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="control-group">
            <label class="control-label">图表类型：</label>
            <el-radio-group v-model="chartType" size="small" @change="handleChartTypeChange">
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 趋势概览 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>趋势概览</h3>
          <div class="header-controls">
            <span class="update-time">最后更新: {{ lastUpdateTime }}</span>
          </div>
        </div>
      </template>
      
      <div class="trend-overview">
        <div 
          v-for="(overview, index) in trendOverview" 
          :key="index"
          class="overview-card"
        >
          <div class="overview-icon" :style="{ 
            '--icon-color': overview.color, 
            '--icon-color-dark': overview.colorDark 
          }">
            <el-icon :size="28" color="white">
              <component :is="overview.icon" />
            </el-icon>
          </div>
          <div class="overview-content">
            <div class="overview-name">{{ overview.name }}</div>
            <div class="overview-value">{{ overview.value }}</div>
            <div class="overview-unit">{{ overview.unit }}</div>
            <div class="overview-trend" :class="{ 'trend-up': overview.trend > 0, 'trend-down': overview.trend < 0 }">
              {{ overview.trend > 0 ? '↑' : '↓' }} {{ Math.abs(overview.trend) }}%
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 主趋势图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="18">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>能耗趋势分析</h3>
              <div class="header-controls">
                <el-button-group size="small">
                  <el-button @click="zoomIn" :disabled="loading">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                  <el-button @click="zoomOut" :disabled="loading">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button @click="resetZoom" :disabled="loading">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-button-group>
                <el-button type="text" size="small" @click="exportChart" :disabled="loading">
                  <el-icon><Download /></el-icon>
                  导出图表
                </el-button>
              </div>
            </div>
          </template>
          <div ref="mainChart" style="height: 500px;"></div>
        </el-card>
      </el-col>
      
      <!-- 趋势统计 -->
      <el-col :span="6">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>统计分析</h3>
            </div>
          </template>
          
          <div class="trend-stats">
            <!-- 统计摘要 -->
            <div class="stats-summary">
              <h4>数据摘要</h4>
              <div class="summary-grid">
                <div class="summary-item">
                  <div class="summary-label">平均值</div>
                  <div class="summary-value">{{ trendSummary.average }}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">最大值</div>
                  <div class="summary-value">{{ trendSummary.max }}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">最小值</div>
                  <div class="summary-value">{{ trendSummary.min }}</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">总变化</div>
                  <div class="summary-value" :class="{ 'trend-up': trendSummary.changeRate > 0, 'trend-down': trendSummary.changeRate < 0 }">
                    {{ trendSummary.changeRate > 0 ? '+' : '' }}{{ trendSummary.changeRate }}%
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 排行榜 -->
            <div class="ranking-section">
              <h4>能耗排行</h4>
              <div class="ranking-list">
                <div 
                  v-for="(item, index) in energyRanking" 
                  :key="index"
                  class="ranking-item"
                  @click="highlightSeries(item.name)"
                >
                  <div class="ranking-number" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
                  <div class="ranking-info">
                    <div class="ranking-name">{{ item.name }}</div>
                    <div class="ranking-value">{{ item.value }}{{ item.unit }}</div>
                  </div>
                  <div class="ranking-change" :class="{ 'trend-up': item.change > 0, 'trend-down': item.change < 0 }">
                    {{ item.change > 0 ? '+' : '' }}{{ item.change }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 对比分析 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>对比分析</h3>
          <div class="header-controls">
            <el-select v-model="compareType" size="small" @change="handleCompareTypeChange">
              <el-option label="同比分析" value="yoy" />
              <el-option label="环比分析" value="mom" />
              <el-option label="峰谷分析" value="peak" />
            </el-select>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div ref="compareChart" style="height: 300px;"></div>
        </el-col>
        <el-col :span="12">
          <div ref="distributionChart" style="height: 300px;"></div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { 
  Refresh, Download, ZoomIn, ZoomOut, 
  Lightning, TrendCharts, DataAnalysis, Monitor
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '能耗趋势', to: '' }
])

// 基础数据
const loading = ref(false)
const analysisLevel = ref('level1')
const timeRange = ref('7d')
const compareMetric = ref('power')
const chartType = ref('line')
const compareType = ref('yoy')
const lastUpdateTime = ref('')

// 图表引用
const mainChart = ref<HTMLElement>()
const compareChart = ref<HTMLElement>()
const distributionChart = ref<HTMLElement>()

// 趋势数据
const trendData = ref<any[]>([])
const trendOverview = ref<any[]>([])
const trendSummary = ref({
  average: '0',
  max: '0',
  min: '0',
  changeRate: 0
})
const energyRanking = ref<any[]>([])

// 初始化趋势概览数据
const initTrendOverview = () => {
  trendOverview.value = [
    {
      name: '总能耗',
      value: '2,580.5',
      unit: 'kWh',
      trend: 5.2,
      color: '#1890ff',
      colorDark: '#0050b3',
      icon: 'Lightning'
    },
    {
      name: '平均功率',
      value: '856.2',
      unit: 'kW',
      trend: 3.8,
      color: '#52c41a',
      colorDark: '#237804',
      icon: 'TrendCharts'
    },
    {
      name: '峰值功率',
      value: '1,281.5',
      unit: 'kW',
      trend: -2.1,
      color: '#fa8c16',
      colorDark: '#ad4e00',
      icon: 'DataAnalysis'
    },
    {
      name: '设备效率',
      value: '87.3',
      unit: '%',
      trend: 1.5,
      color: '#722ed1',
      colorDark: '#391085',
      icon: 'Monitor'
    }
  ]
}

// 生成趋势数据
const generateTrendData = () => {
  const nodes = [
    { name: '办公大楼A', color: '#1890ff', baseValue: 856 },
    { name: '办公大楼B', color: '#52c41a', baseValue: 642 },
    { name: '生产车间', color: '#fa8c16', baseValue: 1081 },
    { name: '辅助设施', color: '#722ed1', baseValue: 320 }
  ]

  const timePoints = generateTimePoints()

  trendData.value = nodes.map(node => ({
    name: node.name,
    type: chartType.value,
    smooth: chartType.value === 'line',
    data: timePoints.map(() => {
      const variation = (Math.random() - 0.5) * 0.3
      return Math.max(0, node.baseValue * (1 + variation))
    }),
    itemStyle: { color: node.color },
    areaStyle: chartType.value === 'line' ? {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: node.color + '40' },
          { offset: 1, color: node.color + '10' }
        ]
      }
    } : undefined
  }))

  updateTrendSummary()
  updateEnergyRanking()
}

// 生成时间点
const generateTimePoints = () => {
  const points = []
  const now = new Date()

  if (timeRange.value === '24h') {
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000)
      points.push(time.getHours().toString().padStart(2, '0') + ':00')
    }
  } else if (timeRange.value === '7d') {
    for (let i = 6; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      points.push((time.getMonth() + 1) + '/' + time.getDate())
    }
  } else if (timeRange.value === '30d') {
    for (let i = 29; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      points.push((time.getMonth() + 1) + '/' + time.getDate())
    }
  } else if (timeRange.value === '90d') {
    for (let i = 89; i >= 0; i -= 3) {
      const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
      points.push((time.getMonth() + 1) + '/' + time.getDate())
    }
  }

  return points
}

// 更新趋势统计
const updateTrendSummary = () => {
  if (trendData.value.length === 0) return

  const allValues = trendData.value.flatMap(series => series.data)
  const average = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
  const max = Math.max(...allValues)
  const min = Math.min(...allValues)

  const firstValues = trendData.value.map(series => series.data[0])
  const lastValues = trendData.value.map(series => series.data[series.data.length - 1])
  const firstAvg = firstValues.reduce((sum, val) => sum + val, 0) / firstValues.length
  const lastAvg = lastValues.reduce((sum, val) => sum + val, 0) / lastValues.length
  const changeRate = ((lastAvg - firstAvg) / firstAvg * 100)

  const unit = getMetricUnit()
  trendSummary.value = {
    average: average.toFixed(1) + unit,
    max: max.toFixed(1) + unit,
    min: min.toFixed(1) + unit,
    changeRate: parseFloat(changeRate.toFixed(1))
  }
}

// 更新能耗排名
const updateEnergyRanking = () => {
  const unit = getMetricUnit()

  energyRanking.value = trendData.value
    .map(series => {
      const avgValue = series.data.reduce((sum: number, val: number) => sum + val, 0) / series.data.length
      const change = (Math.random() - 0.5) * 20
      return {
        name: series.name,
        value: avgValue.toFixed(1),
        unit: unit,
        change: parseFloat(change.toFixed(1))
      }
    })
    .sort((a, b) => parseFloat(b.value) - parseFloat(a.value))
}

// 获取指标单位
const getMetricUnit = () => {
  switch (compareMetric.value) {
    case 'power':
      return 'kW'
    case 'consumption':
      return 'kWh'
    case 'efficiency':
      return '%'
    case 'cost':
      return '元'
    default:
      return 'kW'
  }
}

// 事件处理方法
const handleLevelChange = () => {
  generateTrendData()
  nextTick(() => {
    initMainChart()
    initCompareChart()
    initDistributionChart()
  })
}

const handleTimeRangeChange = () => {
  generateTrendData()
  nextTick(() => {
    initMainChart()
    initCompareChart()
    initDistributionChart()
  })
}

const handleMetricChange = () => {
  generateTrendData()
  nextTick(() => {
    initMainChart()
    initCompareChart()
    initDistributionChart()
  })
}

const handleChartTypeChange = () => {
  generateTrendData()
  nextTick(() => {
    initMainChart()
  })
}

const handleCompareTypeChange = () => {
  nextTick(() => {
    initCompareChart()
    initDistributionChart()
  })
}

// 刷新趋势数据
const refreshTrendData = async () => {
  try {
    loading.value = true

    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新概览数据
    initTrendOverview()

    // 重新生成趋势数据
    generateTrendData()

    // 更新最后更新时间
    lastUpdateTime.value = new Date().toLocaleTimeString()

    // 重新初始化图表
    nextTick(() => {
      initMainChart()
      initCompareChart()
      initDistributionChart()
    })

    ElMessage.success('趋势数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 图表操作方法
const zoomIn = () => {
  const chart = echarts.getInstanceByDom(mainChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'dataZoom',
      start: 10,
      end: 90
    })
  }
}

const zoomOut = () => {
  const chart = echarts.getInstanceByDom(mainChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }
}

const resetZoom = () => {
  const chart = echarts.getInstanceByDom(mainChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'restore'
    })
  }
}

// 高亮系列
const highlightSeries = (seriesName: string) => {
  const chart = echarts.getInstanceByDom(mainChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'highlight',
      seriesName: seriesName
    })
  }
}

// 导出图表
const exportChart = () => {
  try {
    if (!mainChart.value) return

    const chart = echarts.getInstanceByDom(mainChart.value)
    if (!chart) return

    const imageDataURL = chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })

    const link = document.createElement('a')
    link.download = `能耗趋势图_${timeRange.value}_${new Date().toISOString().slice(0, 10)}.png`
    link.href = imageDataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('图表导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 导出所有数据
const exportAllData = () => {
  try {
    const exportData = {
      分析层级: analysisLevel.value,
      时间范围: timeRange.value,
      对比指标: compareMetric.value,
      导出时间: new Date().toISOString(),
      趋势概览: trendOverview.value,
      趋势统计: trendSummary.value,
      能耗排名: energyRanking.value,
      趋势数据: trendData.value
    }

    const jsonData = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `能耗趋势报告_${timeRange.value}_${new Date().toISOString().slice(0, 10)}.json`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('报告导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 初始化主图表
const initMainChart = () => {
  if (!mainChart.value) return

  const chart = echarts.init(mainChart.value)

  const timePoints = generateTimePoints()
  const unit = getMetricUnit()

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e1e8ed',
      borderWidth: 1,
      textStyle: { color: '#2c3e50', fontSize: 12 },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px;'
    },
    legend: {
      top: 10,
      right: 20,
      textStyle: { fontSize: 12 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timePoints,
      axisLabel: {
        fontSize: 11,
        color: '#8c8c8c',
        rotate: timePoints.length > 20 ? 45 : 0
      },
      axisLine: {
        lineStyle: { color: '#e1e8ed' }
      }
    },
    yAxis: {
      type: 'value',
      name: `${getMetricName()}(${unit})`,
      nameTextStyle: {
        fontSize: 12,
        color: '#606266'
      },
      axisLabel: {
        formatter: `{value}${unit}`,
        fontSize: 11,
        color: '#8c8c8c'
      },
      axisLine: {
        lineStyle: { color: '#e1e8ed' }
      },
      splitLine: {
        lineStyle: { color: '#f0f0f0' }
      }
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 20,
        bottom: 10
      }
    ],
    series: trendData.value,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  chart.setOption(option, true)
}

// 获取指标名称
const getMetricName = () => {
  switch (compareMetric.value) {
    case 'power':
      return '功率'
    case 'consumption':
      return '能耗'
    case 'efficiency':
      return '效率'
    case 'cost':
      return '成本'
    default:
      return '功率'
  }
}

// 初始化对比图表
const initCompareChart = () => {
  if (!compareChart.value) return

  const chart = echarts.init(compareChart.value)

  const option = {
    title: {
      text: '同比/环比分析',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 600 }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      bottom: 10,
      textStyle: { fontSize: 11 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['办公大楼A', '办公大楼B', '生产车间', '辅助设施'],
      axisLabel: { fontSize: 10 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { fontSize: 10 }
    },
    series: [
      {
        name: '当前期',
        type: 'bar',
        data: [856, 642, 1081, 320],
        itemStyle: { color: '#1890ff' }
      },
      {
        name: '对比期',
        type: 'bar',
        data: [812, 678, 1156, 298],
        itemStyle: { color: '#52c41a' }
      }
    ]
  }

  chart.setOption(option, true)
}

// 初始化分布图表
const initDistributionChart = () => {
  if (!distributionChart.value) return

  const chart = echarts.init(distributionChart.value)

  const option = {
    title: {
      text: '能耗分布',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 600 }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: 10,
      textStyle: { fontSize: 11 }
    },
    series: [
      {
        name: '能耗分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 856, name: '办公大楼A', itemStyle: { color: '#1890ff' } },
          { value: 642, name: '办公大楼B', itemStyle: { color: '#52c41a' } },
          { value: 1081, name: '生产车间', itemStyle: { color: '#fa8c16' } },
          { value: 320, name: '辅助设施', itemStyle: { color: '#722ed1' } }
        ]
      }
    ]
  }

  chart.setOption(option, true)
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  initTrendOverview()
  generateTrendData()
  lastUpdateTime.value = new Date().toLocaleTimeString()

  // 初始化图表
  nextTick(() => {
    initMainChart()
    initCompareChart()
    initDistributionChart()
  })
})
</script>

<style scoped>
.energy-trend {
  padding: 0;
}

/* 控制面板样式 */
.control-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.update-time {
  font-size: 12px;
  color: #8c8c8c;
}

/* 趋势概览样式 */
.trend-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

.overview-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  cursor: pointer;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--icon-color), var(--icon-color-dark));
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
}

.overview-content {
  flex: 1;
  min-width: 0;
}

.overview-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.overview-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.overview-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-bottom: 4px;
}

.overview-trend {
  font-size: 11px;
  font-weight: 500;
}

.overview-trend.trend-up {
  color: #f56c6c;
}

.overview-trend.trend-down {
  color: #67c23a;
}

/* 趋势统计样式 */
.trend-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 500px;
}

.stats-summary {
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  padding: 16px;
}

.stats-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.summary-item {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.summary-label {
  font-size: 11px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-value.trend-up {
  color: #f56c6c;
}

.summary-value.trend-down {
  color: #67c23a;
}

/* 排行榜样式 */
.ranking-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  padding: 16px;
  flex: 1;
  overflow: hidden;
}

.ranking-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.ranking-item:hover {
  background: #f8f9fa;
  transform: translateX(2px);
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.ranking-number.rank-1 {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.ranking-number.rank-2 {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.ranking-number.rank-3 {
  background: linear-gradient(135deg, #45b7d1, #96c93d);
}

.ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.ranking-info {
  flex: 1;
  min-width: 0;
}

.ranking-name {
  font-size: 12px;
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ranking-value {
  font-size: 11px;
  color: #8c8c8c;
  margin-top: 2px;
}

.ranking-change {
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-change.trend-up {
  color: #f56c6c;
}

.ranking-change.trend-down {
  color: #67c23a;
}

/* 自定义滚动条 */
.ranking-list::-webkit-scrollbar {
  width: 4px;
}

.ranking-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.ranking-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trend-overview {
    grid-template-columns: 1fr;
  }

  .overview-card {
    padding: 12px 16px;
  }

  .overview-value {
    font-size: 24px;
  }
}
</style>
