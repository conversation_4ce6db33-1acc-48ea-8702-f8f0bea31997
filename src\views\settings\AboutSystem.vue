<template>
  <div class="settings-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>关于系统</h3>
        </div>
      </template>
      
      <el-descriptions :column="1" border>
        <el-descriptions-item label="系统名称">能源管理系统</el-descriptions-item>
        <el-descriptions-item label="版本">1.0.0</el-descriptions-item>
        <el-descriptions-item label="开发商">XXX科技有限公司</el-descriptions-item>
        <el-descriptions-item label="联系电话">400-123-4567</el-descriptions-item>
        <el-descriptions-item label="技术支持"><EMAIL></el-descriptions-item>
        <el-descriptions-item label="官方网站">https://www.example.com</el-descriptions-item>
        <el-descriptions-item label="版权信息">© 2025 XXX科技有限公司 版权所有</el-descriptions-item>
      </el-descriptions>
      
      <div class="about-actions">
        <el-button type="primary" @click="checkUpdate">检查更新</el-button>
        <el-button @click="viewLicense">查看许可协议</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '系统设置' },
  { text: '关于系统' }
])

// 检查更新
const checkUpdate = () => {
  ElMessage.info('当前已是最新版本')
}

// 查看许可协议
const viewLicense = () => {
  ElMessageBox.alert('本软件受版权法和国际公约保护，未经授权不得复制或分发本软件的任何部分。', '许可协议', {
    confirmButtonText: '确定'
  })
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  width: 100%;
}

.settings-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.about-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}
</style> 