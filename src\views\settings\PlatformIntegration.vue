<template>
  <div class="platform-integration" v-loading="loading" element-loading-text="正在加载平台对接数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 1. 状态总览 - 24列全宽，渐变背景 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="overview-card">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>平台对接管理中心</h2>
                <div class="status-indicator" :class="systemStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(systemStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(systemStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="overview-metrics">
            <div class="metric-item">
              <div class="metric-icon connected">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ platformStats.connected }}</div>
                <div class="metric-label">已连接平台</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon disconnected">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ platformStats.disconnected }}</div>
                <div class="metric-label">断开连接</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon syncing">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ platformStats.syncing }}</div>
                <div class="metric-label">同步中</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon total">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ platformStats.total }}</div>
                <div class="metric-label">总平台数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 2. 实时监控 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                平台连接状态监控
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedPlatformType" size="small" style="width: 120px;">
                  <el-option label="全部平台" value="" />
                  <el-option label="物联网平台" value="iot" />
                  <el-option label="云平台" value="cloud" />
                  <el-option label="数据平台" value="data" />
                  <el-option label="第三方API" value="api" />
                </el-select>
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="data-grid">
            <div class="data-card" v-for="platform in platformConnections" :key="platform.id">
              <div class="platform-header">
                <div class="platform-icon" :class="platform.status">
                  <el-icon>
                    <component :is="getPlatformIcon(platform.type)" />
                  </el-icon>
                </div>
                <div class="platform-info">
                  <div class="platform-name">{{ platform.name }}</div>
                  <div class="platform-type">{{ platform.type }}</div>
                </div>
                <div class="connection-status" :class="getConnectionStatusClass(platform.status)">
                  {{ getConnectionStatusText(platform.status) }}
                </div>
              </div>
              <div class="platform-details">
                <div class="detail-item">
                  <span class="detail-label">连接地址：</span>
                  <span class="detail-value">{{ platform.endpoint }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">数据同步：</span>
                  <span class="detail-value" :class="getSyncStatusClass(platform.syncStatus)">{{ platform.syncStatus }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">最后同步：</span>
                  <span class="detail-value">{{ platform.lastSync }}</span>
                </div>
              </div>
              <div class="platform-actions">
                <el-button type="primary" size="small" @click="testConnection(platform)">
                  测试
                </el-button>
                <el-button type="success" size="small" @click="configPlatform(platform)">
                  配置
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 3. 主要功能 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Setting /></el-icon>
                平台配置管理
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showAddPlatformDialog = true">
                  <el-icon><Plus /></el-icon>
                  新增平台
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="config-grid">
            <div class="config-card" v-for="config in platformConfigs" :key="config.id">
              <div class="config-header">
                <div class="config-icon" :class="config.category">
                  <el-icon>
                    <component :is="getConfigIcon(config.category)" />
                  </el-icon>
                </div>
                <div class="config-info">
                  <div class="config-name">{{ config.name }}</div>
                  <div class="config-description">{{ config.description }}</div>
                </div>
              </div>
              <div class="config-details">
                <div class="detail-item">
                  <span class="detail-label">协议类型：</span>
                  <span class="detail-value">{{ config.protocol }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">认证方式：</span>
                  <span class="detail-value">{{ config.authType }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">数据格式：</span>
                  <span class="detail-value">{{ config.dataFormat }}</span>
                </div>
              </div>
              <div class="config-actions">
                <el-button type="primary" size="small" @click="editConfig(config)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="deployConfig(config)">
                  部署
                </el-button>
                <el-button type="danger" size="small" @click="deleteConfig(config)">
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 4. 详细管理 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                平台对接日志
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedLogLevel" size="small" style="width: 120px;">
                  <el-option label="全部日志" value="" />
                  <el-option label="成功" value="success" />
                  <el-option label="警告" value="warning" />
                  <el-option label="错误" value="error" />
                </el-select>
                <el-button type="success" size="small" @click="exportLogs">
                  <el-icon><Operation /></el-icon>
                  导出日志
                </el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="platformLogs" border stripe style="width: 100%">
            <el-table-column prop="timestamp" label="时间" width="160" align="center" />
            <el-table-column prop="platformName" label="平台名称" min-width="150" />
            <el-table-column prop="operation" label="操作类型" width="120" align="center" />
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getLogStatusType(row.status)">
                  {{ getLogStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="日志信息" min-width="200" />
            <el-table-column prop="duration" label="耗时(ms)" width="100" align="center" />
            <el-table-column prop="dataSize" label="数据量" width="100" align="center" />
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewLogDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalLogs"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  SuccessFilled,
  CircleCloseFilled,
  Timer,
  Monitor,
  Setting,
  Operation
} from '@element-plus/icons-vue'

// 面包屑导航数据 - 更新为系统设置的子页面
const breadcrumbItems = ref([
  { text: '系统设置', to: '/settings' },
  { text: '平台对接' }
])

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const systemStatus = ref('normal')
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const selectedPlatformType = ref('')
const selectedLogLevel = ref('')
const showAddPlatformDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalLogs = ref(50)

// 平台统计数据
const platformStats = reactive({
  connected: 8,
  disconnected: 2,
  syncing: 1,
  total: 11
})

// 平台连接数据
const platformConnections = ref([
  {
    id: 'PLT001',
    name: '阿里云物联网平台',
    type: '物联网平台',
    status: 'connected',
    endpoint: 'iot.aliyun.com',
    syncStatus: '正常',
    lastSync: '2024-01-15 14:25:30'
  },
  {
    id: 'PLT002',
    name: '腾讯云API网关',
    type: '云平台',
    status: 'connected',
    endpoint: 'apigateway.tencentcloud.com',
    syncStatus: '正常',
    lastSync: '2024-01-15 14:24:15'
  },
  {
    id: 'PLT003',
    name: '华为云数据湖',
    type: '数据平台',
    status: 'syncing',
    endpoint: 'dli.huaweicloud.com',
    syncStatus: '同步中',
    lastSync: '2024-01-15 14:20:00'
  },
  {
    id: 'PLT004',
    name: '百度智能云',
    type: '第三方API',
    status: 'disconnected',
    endpoint: 'cloud.baidu.com',
    syncStatus: '连接失败',
    lastSync: '2024-01-15 13:45:20'
  }
])

// 平台配置数据
const platformConfigs = ref([
  {
    id: 'CFG001',
    name: 'MQTT物联网配置',
    description: 'MQTT协议的物联网设备接入配置',
    category: 'iot',
    protocol: 'MQTT',
    authType: 'Token认证',
    dataFormat: 'JSON'
  },
  {
    id: 'CFG002',
    name: 'REST API配置',
    description: 'RESTful API接口对接配置',
    category: 'api',
    protocol: 'HTTP/HTTPS',
    authType: 'OAuth2.0',
    dataFormat: 'JSON'
  },
  {
    id: 'CFG003',
    name: '数据库同步配置',
    description: '数据库数据同步配置',
    category: 'database',
    protocol: 'TCP/IP',
    authType: '用户名密码',
    dataFormat: 'SQL'
  },
  {
    id: 'CFG004',
    name: 'WebSocket实时配置',
    description: 'WebSocket实时数据推送配置',
    category: 'realtime',
    protocol: 'WebSocket',
    authType: 'JWT Token',
    dataFormat: 'JSON'
  }
])

// 平台日志数据
const platformLogs = ref([
  {
    id: 'LOG001',
    timestamp: '2024-01-15 14:25:30',
    platformName: '阿里云物联网平台',
    operation: '数据同步',
    status: 'success',
    message: '成功同步设备数据 125 条',
    duration: 1250,
    dataSize: '2.3KB'
  },
  {
    id: 'LOG002',
    timestamp: '2024-01-15 14:20:15',
    platformName: '腾讯云API网关',
    operation: '连接测试',
    status: 'success',
    message: '连接测试成功，响应时间正常',
    duration: 850,
    dataSize: '0.5KB'
  },
  {
    id: 'LOG003',
    timestamp: '2024-01-15 14:15:45',
    platformName: '百度智能云',
    operation: '认证验证',
    status: 'error',
    message: 'Token认证失败，请检查密钥配置',
    duration: 3200,
    dataSize: '0.2KB'
  },
  {
    id: 'LOG004',
    timestamp: '2024-01-15 14:10:20',
    platformName: '华为云数据湖',
    operation: '数据推送',
    status: 'warning',
    message: '数据推送部分成功，3条记录失败',
    duration: 2100,
    dataSize: '5.7KB'
  }
])

// 工具函数
const getStatusIcon = (status: string) => {
  const iconMap = {
    'normal': SuccessFilled,
    'warning': Timer,
    'danger': CircleCloseFilled
  }
  return iconMap[status] || SuccessFilled
}

const getStatusText = (status: string) => {
  const textMap = {
    'normal': '系统正常',
    'warning': '需要关注',
    'danger': '存在异常'
  }
  return textMap[status] || '未知状态'
}

const getPlatformIcon = (type: string) => {
  const iconMap = {
    '物联网平台': Monitor,
    '云平台': Setting,
    '数据平台': Operation,
    '第三方API': Timer
  }
  return iconMap[type] || Monitor
}

const getConnectionStatusClass = (status: string) => {
  const classMap = {
    'connected': 'status-connected',
    'disconnected': 'status-disconnected',
    'syncing': 'status-syncing'
  }
  return classMap[status] || 'status-unknown'
}

const getConnectionStatusText = (status: string) => {
  const textMap = {
    'connected': '已连接',
    'disconnected': '已断开',
    'syncing': '同步中'
  }
  return textMap[status] || '未知'
}

const getSyncStatusClass = (status: string) => {
  if (status === '正常') return 'sync-normal'
  if (status === '同步中') return 'sync-syncing'
  return 'sync-error'
}

const getConfigIcon = (category: string) => {
  const iconMap = {
    'iot': Monitor,
    'api': Setting,
    'database': Operation,
    'realtime': Timer
  }
  return iconMap[category] || Setting
}

const getLogStatusType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}

const getLogStatusText = (status: string) => {
  const textMap = {
    'success': '成功',
    'warning': '警告',
    'error': '错误'
  }
  return textMap[status] || '未知'
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  ElMessage.success(`自动刷新已${autoRefresh.value ? '开启' : '关闭'}`)
}

const testConnection = (platform: any) => {
  ElMessage.info(`正在测试连接: ${platform.name}`)
}

const configPlatform = (platform: any) => {
  ElMessage.info(`配置平台: ${platform.name}`)
}

const editConfig = (config: any) => {
  ElMessage.info(`编辑配置: ${config.name}`)
}

const deployConfig = (config: any) => {
  ElMessage.success(`部署配置: ${config.name}`)
}

const deleteConfig = (config: any) => {
  ElMessage.warning(`删除配置: ${config.name}`)
}

const exportLogs = () => {
  ElMessage.success('日志导出成功')
}

const viewLogDetail = (row: any) => {
  ElMessage.info(`查看日志详情: ${row.platformName}`)
}
</script>

<style scoped>
.platform-integration {
  padding: 0;
}

/* 1. 状态总览样式 - 渐变背景 */
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-card :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.overview-card :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.status-indicator.normal .status-icon {
  color: #67c23a;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.overview-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 2px solid;
}

.metric-icon.connected {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border-color: #67c23a;
}

.metric-icon.disconnected {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border-color: #f56c6c;
}

.metric-icon.syncing {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border-color: #e6a23c;
}

.metric-icon.total {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border-color: #409eff;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

/* 2. 数据网格样式 - 自适应布局 */
.data-grid,
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.data-card,
.config-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.data-card:hover,
.config-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.platform-header,
.config-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.platform-icon,
.config-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.platform-icon.connected,
.config-icon.iot {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.platform-icon.disconnected,
.config-icon.database {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.platform-icon.syncing,
.config-icon.api {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.config-icon.realtime {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.platform-info,
.config-info {
  flex: 1;
}

.platform-name,
.config-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.platform-type {
  font-size: 12px;
  color: #909399;
}

.config-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.connection-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.connection-status.status-connected {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border: 1px solid #67c23a;
}

.connection-status.status-disconnected {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.connection-status.status-syncing {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border: 1px solid #e6a23c;
}

.platform-details,
.config-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.detail-value.sync-normal {
  color: #67c23a;
}

.detail-value.sync-syncing {
  color: #e6a23c;
}

.detail-value.sync-error {
  color: #f56c6c;
}

.platform-actions,
.config-actions {
  display: flex;
  gap: 8px;
}

.platform-actions .el-button,
.config-actions .el-button {
  flex: 1;
}

/* 通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .data-grid,
  .config-grid {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .header-controls .el-select,
  .header-controls .el-button {
    width: 100%;
  }
}
</style>
