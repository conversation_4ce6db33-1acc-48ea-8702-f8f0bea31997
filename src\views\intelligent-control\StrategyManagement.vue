<template>
  <div class="strategy-management" v-loading="loading" element-loading-text="正在加载策略数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 策略状态总览 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="strategy-overview">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>智能控制策略管理</h2>
                <div class="strategy-status normal">
                  <el-icon class="status-icon">
                    <SuccessFilled />
                  </el-icon>
                  <span class="status-text">系统正常</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="strategy-metrics">
            <div class="metric-item">
              <div class="metric-icon enabled">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ strategyStats.enabled }}</div>
                <div class="metric-label">启用策略</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon disabled">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ strategyStats.disabled }}</div>
                <div class="metric-label">禁用策略</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon testing">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ strategyStats.testing }}</div>
                <div class="metric-label">测试中</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon total">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ strategyStats.total }}</div>
                <div class="metric-label">总策略数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 策略执行监控和效果分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                策略执行监控
              </h3>
              <div class="header-controls">
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>

          <!-- 效果统计卡片 -->
          <div class="effect-stats-row">
            <div class="effect-stat-card">
              <div class="stat-icon primary">
                <el-icon><Lightning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">1,234 kWh</div>
                <div class="stat-label">今日节能量</div>
                <div class="stat-change positive">+15.2%</div>
              </div>
            </div>
            <div class="effect-stat-card">
              <div class="stat-icon success">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">¥2,468</div>
                <div class="stat-label">成本节约</div>
                <div class="stat-change positive">+12.8%</div>
              </div>
            </div>
            <div class="effect-stat-card">
              <div class="stat-icon info">
                <el-icon><Sunny /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">0.98 吨</div>
                <div class="stat-label">碳减排量</div>
                <div class="stat-change positive">+18.5%</div>
              </div>
            </div>
            <div class="effect-stat-card">
              <div class="stat-icon warning">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">今日执行次数</div>
                <div class="stat-change positive">+8.3%</div>
              </div>
            </div>
          </div>

          <!-- 执行监控网格 -->
          <div class="execution-grid">
            <div class="execution-card" v-for="execution in recentExecutions" :key="execution.id">
              <div class="execution-header">
                <div class="execution-title">{{ execution.strategyName }}</div>
                <el-tag :type="getExecutionStatusType(execution.status)" size="small">
                  {{ getExecutionStatusText(execution.status) }}
                </el-tag>
              </div>
              <div class="execution-details">
                <div class="execution-time">执行时间：{{ execution.executeTime }}</div>
                <div class="execution-result">执行结果：{{ execution.result }}</div>
                <div class="execution-effect" v-if="execution.effect">
                  节能效果：<span class="effect-value">{{ execution.effect }}</span>
                </div>
              </div>
              <div class="execution-progress" v-if="execution.status === 'running'">
                <el-progress :percentage="execution.progress" :stroke-width="6" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 策略模板库 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Folder /></el-icon>
                策略模板库
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showTemplateDialog = true">
                  <el-icon><Plus /></el-icon>
                  创建模板
                </el-button>
              </div>
            </div>
          </template>

          <div class="template-grid">
            <div class="template-card" v-for="template in strategyTemplates" :key="template.id">
              <div class="template-header">
                <div class="template-icon">
                  <el-icon>
                    <component :is="getTemplateIcon(template.category)" />
                  </el-icon>
                </div>
                <div class="template-info">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-category">{{ template.category }}</div>
                </div>
                <div class="template-actions">
                  <el-button type="primary" size="small" @click="applyTemplate(template)">
                    应用
                  </el-button>
                </div>
              </div>
              <div class="template-description">{{ template.description }}</div>
              <div class="template-stats">
                <span class="stat-item">使用次数: {{ template.usageCount }}</span>
                <span class="stat-item">成功率: {{ template.successRate }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 智能推荐 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><MagicStick /></el-icon>
                智能推荐
              </h3>
              <div class="header-controls">
                <el-button type="info" size="small" @click="refreshRecommendations">
                  <el-icon><Refresh /></el-icon>
                  刷新推荐
                </el-button>
              </div>
            </div>
          </template>

          <div class="recommendation-grid">
            <div class="recommendation-item" v-for="rec in recommendations" :key="rec.id">
              <div class="rec-header">
                <div class="rec-type" :class="rec.type">{{ rec.typeText }}</div>
                <div class="rec-priority" :class="rec.priority">{{ rec.priority }}</div>
              </div>
              <div class="rec-title">{{ rec.title }}</div>
              <div class="rec-description">{{ rec.description }}</div>
              <div class="rec-benefit">预计效果：{{ rec.benefit }}</div>
              <div class="rec-actions">
                <el-button type="primary" size="small" @click="applyRecommendation(rec)">
                  采纳建议
                </el-button>
                <el-button size="small" @click="dismissRecommendation(rec)">
                  忽略
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 策略列表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><List /></el-icon>
                策略列表
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedCategory" size="small" placeholder="策略类型" style="width: 120px;">
                  <el-option label="全部类型" value="" />
                  <el-option label="节能策略" value="energy-saving" />
                  <el-option label="安全策略" value="safety" />
                  <el-option label="优化策略" value="optimization" />
                  <el-option label="应急策略" value="emergency" />
                </el-select>
                <el-select v-model="selectedStatus" size="small" placeholder="策略状态" style="width: 120px;">
                  <el-option label="全部状态" value="" />
                  <el-option label="启用" value="enabled" />
                  <el-option label="禁用" value="disabled" />
                  <el-option label="测试中" value="testing" />
                </el-select>
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索策略"
                  size="small"
                  clearable
                  style="width: 150px;"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" size="small" @click="showCreateDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建策略
                </el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="strategies" border stripe style="width: 100%">
            <el-table-column prop="id" label="策略ID" width="80" align="center" />
            <el-table-column prop="name" label="策略名称" min-width="150" />
            <el-table-column label="策略类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag type="success">
                  节能策略
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="策略描述" min-width="200" />
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag type="success">
                  启用
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80" align="center" />
            <el-table-column prop="triggerCount" label="触发次数" width="100" align="center" />
            <el-table-column prop="lastExecuted" label="最后执行" width="160" align="center" />
            <el-table-column prop="creator" label="创建人" width="100" align="center" />
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small">查看</el-button>
                <el-button type="success" size="small">编辑</el-button>
                <el-button type="danger" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalStrategies"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 创建策略对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建策略"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form label-width="100px">
        <el-form-item label="策略名称">
          <el-input placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="策略类型">
          <el-select placeholder="请选择策略类型" style="width: 100%;">
            <el-option label="节能策略" value="energy-saving" />
            <el-option label="安全策略" value="safety" />
            <el-option label="优化策略" value="optimization" />
            <el-option label="应急策略" value="emergency" />
          </el-select>
        </el-form-item>
        <el-form-item label="策略描述">
          <el-input type="textarea" :rows="3" placeholder="请输入策略描述" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  Search,
  CircleCheckFilled,
  CircleCloseFilled,
  Clock,
  DataAnalysis,
  List,
  SuccessFilled,
  Monitor,
  Timer,
  TrendCharts,
  Folder,
  Lightning,
  Setting,
  Lock,
  Money,
  Sunny,
  MagicStick
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '智能控制中心', to: '/intelligent-control' },
  { text: '策略管理' }
])

// 响应式数据
const loading = ref(false)
const selectedCategory = ref('')
const selectedStatus = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalStrategies = ref(50)
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const showCreateDialog = ref(false)
const showTemplateDialog = ref(false)
const autoRefresh = ref(false)
const effectChart = ref()

// 策略统计数据
const strategyStats = reactive({
  enabled: 12,
  disabled: 5,
  testing: 3,
  total: 20
})

// 策略执行监控数据
const recentExecutions = ref([
  {
    id: 'EXE001',
    strategyName: '夜间节能策略',
    status: 'success',
    executeTime: '2024-01-15 22:00:00',
    result: '成功关闭非必要照明',
    effect: '节能15%',
    progress: 100
  },
  {
    id: 'EXE002',
    strategyName: '温度控制策略',
    status: 'running',
    executeTime: '2024-01-15 14:25:00',
    result: '正在调节空调温度',
    effect: '预计节能8%',
    progress: 65
  },
  {
    id: 'EXE003',
    strategyName: '照明优化策略',
    status: 'success',
    executeTime: '2024-01-15 12:00:00',
    result: '调节照明亮度完成',
    effect: '节能12%',
    progress: 100
  }
])

// 策略模板数据
const strategyTemplates = ref([
  {
    id: 'TPL001',
    name: '办公楼节能模板',
    category: '节能策略',
    description: '适用于办公楼的综合节能策略，包含照明、空调、设备管理',
    usageCount: 45,
    successRate: 92
  },
  {
    id: 'TPL002',
    name: '工厂安全监控模板',
    category: '安全策略',
    description: '工厂环境的安全监控策略，包含温度、湿度、气体监测',
    usageCount: 28,
    successRate: 96
  },
  {
    id: 'TPL003',
    name: '智能照明模板',
    category: '优化策略',
    description: '基于人员活动和自然光的智能照明调节策略',
    usageCount: 67,
    successRate: 89
  },
  {
    id: 'TPL004',
    name: '商场节能模板',
    category: '节能策略',
    description: '商场营业时间的智能节能策略，包含扶梯、照明、空调联动',
    usageCount: 32,
    successRate: 88
  },
  {
    id: 'TPL005',
    name: '数据中心温控模板',
    category: '安全策略',
    description: '数据中心精密温控策略，确保设备稳定运行',
    usageCount: 19,
    successRate: 98
  },
  {
    id: 'TPL006',
    name: '学校智能管理模板',
    category: '优化策略',
    description: '学校作息时间的智能设备管理策略',
    usageCount: 41,
    successRate: 91
  }
])

// 智能推荐数据
const recommendations = ref([
  {
    id: 'REC001',
    type: 'optimization',
    typeText: '优化建议',
    priority: '高',
    title: '照明策略优化',
    description: '建议调整照明控制策略的触发时间，可提升节能效果',
    benefit: '节能提升12%'
  },
  {
    id: 'REC002',
    type: 'new',
    typeText: '新策略推荐',
    priority: '中',
    title: '空调联动策略',
    description: '基于人员活动检测的空调智能调节策略',
    benefit: '节能8-15%'
  },
  {
    id: 'REC003',
    type: 'alert',
    typeText: '异常提醒',
    priority: '高',
    title: '策略执行异常',
    description: '温度控制策略近期执行成功率下降，建议检查传感器',
    benefit: '提升稳定性'
  }
])

// 策略列表数据
const strategies = ref([
  {
    id: 'STR001',
    name: '夜间节能策略',
    description: '夜间自动关闭非必要照明和设备',
    priority: '中',
    triggerCount: 156,
    lastExecuted: '2024-01-15 22:00:00',
    creator: '张工程师'
  },
  {
    id: 'STR002',
    name: '温度异常应急策略',
    description: '温度超过阈值时的应急处理',
    priority: '高',
    triggerCount: 23,
    lastExecuted: '2024-01-14 14:25:00',
    creator: '李技术员'
  },
  {
    id: 'STR003',
    name: '照明智能调节策略',
    description: '根据自然光强度自动调节室内照明',
    priority: '中',
    triggerCount: 89,
    lastExecuted: '2024-01-15 18:30:00',
    creator: '王工程师'
  },
  {
    id: 'STR004',
    name: '设备预防性维护策略',
    description: '基于设备运行状态的预防性维护提醒',
    priority: '低',
    triggerCount: 12,
    lastExecuted: '2024-01-13 09:15:00',
    creator: '李技术员'
  }
])

// 工具函数
const getExecutionStatusType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'running': 'warning',
    'failed': 'danger',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

const getExecutionStatusText = (status: string) => {
  const textMap = {
    'success': '执行成功',
    'running': '执行中',
    'failed': '执行失败',
    'pending': '等待执行'
  }
  return textMap[status] || '未知'
}

const getTemplateIcon = (category: string) => {
  const iconMap = {
    '节能策略': Lightning,
    '安全策略': Lock,
    '优化策略': Setting
  }
  return iconMap[category] || Setting
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  // 这里可以添加自动刷新的逻辑
}

const applyTemplate = (template: any) => {
  // 应用模板逻辑
  console.log('应用模板:', template.name)
}

const applyRecommendation = (rec: any) => {
  // 采纳推荐逻辑
  console.log('采纳推荐:', rec.title)
}

const dismissRecommendation = (rec: any) => {
  // 忽略推荐逻辑
  console.log('忽略推荐:', rec.title)
}

const refreshRecommendations = () => {
  // 刷新推荐逻辑
  console.log('刷新智能推荐')
}
</script>

<style scoped>
.strategy-management {
  padding: 0;
}

/* 策略总览样式 */
.strategy-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.strategy-overview :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.strategy-overview :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.strategy-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.strategy-status.normal .status-icon {
  color: #67c23a;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.strategy-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.metric-icon.enabled {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border: 2px solid #67c23a;
}

.metric-icon.disabled {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border: 2px solid #f56c6c;
}

.metric-icon.testing {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border: 2px solid #e6a23c;
}

.metric-icon.total {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border: 2px solid #409eff;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 效果统计卡片样式 */
.effect-stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.effect-stat-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.effect-stat-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.info {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

/* 策略执行监控样式 */
.execution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.execution-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.execution-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.execution-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.execution-details {
  margin-bottom: 12px;
}

.execution-time,
.execution-result,
.execution-effect {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.execution-effect .effect-value {
  color: #67c23a;
  font-weight: 600;
}

.execution-progress {
  margin-top: 8px;
}

/* 效果分析样式 */
.effect-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.effect-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.effect-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.effect-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
}

.effect-value.primary {
  color: #409eff;
}

.effect-value.success {
  color: #67c23a;
}

.effect-value.info {
  color: #909399;
}

.effect-change {
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.effect-change.positive {
  color: #67c23a;
}

.effect-change.negative {
  color: #f56c6c;
}

/* 模板库样式 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.template-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.template-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 18px;
}

.template-info {
  flex: 1;
}

.template-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.template-category {
  font-size: 12px;
  color: #909399;
}

.template-description {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 12px;
}

.template-stats {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #909399;
}

.stat-item {
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 4px;
}

/* 智能推荐样式 */
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.recommendation-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rec-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.rec-type.optimization {
  background: #409eff;
}

.rec-type.new {
  background: #67c23a;
}

.rec-type.alert {
  background: #f56c6c;
}

.rec-priority {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
}

.rec-priority.高 {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.rec-priority.中 {
  background: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.rec-priority.低 {
  background: #f0f9ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.rec-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.rec-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
}

.rec-benefit {
  font-size: 12px;
  color: #67c23a;
  font-weight: 600;
  margin-bottom: 12px;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

.rec-actions .el-button {
  flex: 1;
}
</style>
