<template>
  <div class="safety-monitoring" v-loading="loading" element-loading-text="正在加载安全监测数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 安全状态总览 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="safety-overview">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>安全监测中心</h2>
                <div class="safety-status" :class="overallStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(overallStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(overallStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshAll">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="safety-metrics">
            <div class="metric-item">
              <div class="metric-icon normal">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ safetyMetrics.normal }}</div>
                <div class="metric-label">正常设备</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon warning">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ safetyMetrics.warning }}</div>
                <div class="metric-label">告警设备</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon danger">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ safetyMetrics.danger }}</div>
                <div class="metric-label">危险设备</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon offline">
                <el-icon><SwitchButton /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ safetyMetrics.offline }}</div>
                <div class="metric-label">离线设备</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon info">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ safetyMetrics.todayEvents }}</div>
                <div class="metric-label">今日事件</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时监控区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 安全参数监控 -->
      <el-col :span="16">
        <el-card class="monitoring-panel">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                实时安全参数监控
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedArea" size="small" placeholder="选择区域" style="width: 150px;">
                  <el-option label="全部区域" value="" />
                  <el-option label="办公大楼A" value="building-a" />
                  <el-option label="办公大楼B" value="building-b" />
                  <el-option label="生产车间" value="workshop" />
                  <el-option label="配电房" value="power-room" />
                </el-select>
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="parameter-grid">
            <div class="parameter-card" v-for="param in safetyParameters" :key="param.id">
              <div class="param-header">
                <div class="param-title">{{ param.name }}</div>
                <el-tag :type="getParamStatusType(param.status)" size="small">
                  {{ getParamStatusText(param.status) }}
                </el-tag>
              </div>
              <div class="param-value">
                <span class="value">{{ param.currentValue }}</span>
                <span class="unit">{{ param.unit }}</span>
              </div>
              <div class="param-range">
                <span>正常范围：{{ param.minValue }} - {{ param.maxValue }} {{ param.unit }}</span>
              </div>
              <div class="param-chart">
                <div :ref="el => setParameterChartRef(el, param.id)" style="height: 100px;"></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 安全事件面板 -->
      <el-col :span="8">
        <el-card class="events-panel">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Bell /></el-icon>
                最新安全事件
              </h3>
              <el-button type="primary" size="small" @click="viewAllEvents">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="events-list">
            <div 
              class="event-item" 
              v-for="event in latestEvents" 
              :key="event.id"
              :class="getEventSeverityClass(event.severity)"
              @click="viewEventDetail(event)"
            >
              <div class="event-icon">
                <el-icon>
                  <component :is="getEventIcon(event.type)" />
                </el-icon>
              </div>
              <div class="event-content">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-location">{{ event.location }}</div>
                <div class="event-time">{{ formatTime(event.time) }}</div>
              </div>
              <div class="event-status">
                <el-tag :type="getEventStatusType(event.status)" size="small">
                  {{ getEventStatusText(event.status) }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <div class="events-footer" v-if="latestEvents.length === 0">
            <el-empty description="暂无安全事件" :image-size="60" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="安全事件" name="events">
              <template #label>
                <span><el-icon><WarningFilled /></el-icon> 安全事件</span>
              </template>
              
              <!-- 事件筛选 -->
              <div class="filter-row" style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap; margin-bottom: 20px;">
                <el-select
                  v-model="eventFilters.severity"
                  placeholder="事件级别"
                  size="small"
                  clearable
                  style="width: 100px;"
                >
                  <el-option label="全部级别" value="" />
                  <el-option label="紧急" value="critical" />
                  <el-option label="重要" value="major" />
                  <el-option label="一般" value="minor" />
                  <el-option label="提醒" value="info" />
                </el-select>

                <el-select
                  v-model="eventFilters.type"
                  placeholder="事件类型"
                  size="small"
                  clearable
                  style="width: 100px;"
                >
                  <el-option label="全部类型" value="" />
                  <el-option label="电压异常" value="voltage" />
                  <el-option label="电流异常" value="current" />
                  <el-option label="漏电检测" value="leakage" />
                  <el-option label="过载保护" value="overload" />
                  <el-option label="短路保护" value="short-circuit" />
                  <el-option label="温度异常" value="temperature" />
                </el-select>

                <el-select
                  v-model="eventFilters.status"
                  placeholder="处理状态"
                  size="small"
                  clearable
                  style="width: 100px;"
                >
                  <el-option label="全部状态" value="" />
                  <el-option label="待处理" value="pending" />
                  <el-option label="处理中" value="processing" />
                  <el-option label="已处理" value="resolved" />
                  <el-option label="已忽略" value="ignored" />
                </el-select>

                <div style="width: 220px;">
                  <el-date-picker
                    v-model="eventFilters.dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    size="small"
                    format="MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%;"
                  />
                </div>

                <el-input
                  v-model="eventFilters.keyword"
                  placeholder="搜索事件"
                  size="small"
                  clearable
                  style="width: 150px;"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>

                <el-button
                  type="primary"
                  size="small"
                  @click="searchEvents"
                  style="width: 80px;"
                >
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
              </div>
              
              <!-- 事件列表 -->
              <el-table :data="safetyEvents" border stripe style="width: 100%">
                <el-table-column prop="id" label="事件ID" width="80" align="center" />
                <el-table-column label="事件级别" width="90" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getSeverityTagType(row.severity)" size="small">
                      {{ getSeverityText(row.severity) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="事件类型" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getTypeTagType(row.type)">
                      {{ getTypeText(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="事件描述" min-width="120" />
                <el-table-column prop="location" label="发生位置" width="120" align="center" />
                <el-table-column prop="deviceName" label="相关设备" width="120" align="center" />
                <el-table-column prop="time" label="发生时间" width="140" align="center" />
                <el-table-column label="处理状态" width="90" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getEventStatusType(row.status)" size="small">
                      {{ getEventStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="handler" label="处理人" width="80" align="center" />
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template #default="{ row }">
                    <div style="white-space: nowrap;">
                      <el-button type="primary" size="small" @click="viewEventDetail(row)" style="margin-right: 5px;">
                        查看
                      </el-button>
                      <el-button 
                        type="success" 
                        size="small" 
                        @click="handleEvent(row)" 
                        :disabled="row.status === 'resolved'"
                        style="margin-right: 5px;"
                      >
                        处理
                      </el-button>
                      <el-button type="info" size="small" @click="exportEvent(row)">
                        导出
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="安全统计" name="statistics">
              <template #label>
                <span><el-icon><DataAnalysis /></el-icon> 安全统计</span>
              </template>
              
              <!-- 统计图表 -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <h4>事件类型分布</h4>
                    </template>
                    <div ref="eventTypeChart" style="height: 300px;"></div>
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <h4>事件趋势分析</h4>
                    </template>
                    <div ref="eventTrendChart" style="height: 300px;"></div>
                  </el-card>
                </el-col>
              </el-row>
              
              <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <h4>区域安全指数</h4>
                    </template>
                    <div ref="safetyIndexChart" style="height: 300px;"></div>
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card>
                    <template #header>
                      <h4>处理效率统计</h4>
                    </template>
                    <div ref="efficiencyChart" style="height: 300px;"></div>
                  </el-card>
                </el-col>
              </el-row>
            </el-tab-pane>
            
            <el-tab-pane label="设备监控" name="devices">
              <template #label>
                <span><el-icon><Monitor /></el-icon> 设备监控</span>
              </template>
              
              <!-- 设备安全状态 -->
              <el-table :data="monitoringDevices" border style="width: 100%">
                <el-table-column prop="deviceId" label="设备编号" width="120" align="center" />
                <el-table-column prop="deviceName" label="设备名称" min-width="150" />
                <el-table-column prop="location" label="安装位置" width="150" align="center" />
                <el-table-column label="安全状态" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getDeviceStatusType(row.safetyStatus)" size="small">
                      {{ getDeviceStatusText(row.safetyStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="voltage" label="电压(V)" width="100" align="center">
                  <template #default="{ row }">
                    <span :class="getValueClass(row.voltage, 380, 420)">{{ row.voltage }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="current" label="电流(A)" width="100" align="center">
                  <template #default="{ row }">
                    <span :class="getValueClass(row.current, 0, 100)">{{ row.current }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="temperature" label="温度(℃)" width="100" align="center">
                  <template #default="{ row }">
                    <span :class="getValueClass(row.temperature, 0, 60)">{{ row.temperature }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="leakageCurrent" label="漏电流(mA)" width="120" align="center">
                  <template #default="{ row }">
                    <span :class="getValueClass(row.leakageCurrent, 0, 30, true)">{{ row.leakageCurrent }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="lastCheckTime" label="最后检测" width="160" align="center" />
                <el-table-column label="操作" width="150" align="center" fixed="right">
                  <template #default="{ row }">
                    <div style="white-space: nowrap;">
                      <el-button type="primary" size="small" @click="viewDeviceDetail(row)" style="margin-right: 5px;">
                        详情
                      </el-button>
                      <el-button type="warning" size="small" @click="testDevice(row)">
                        检测
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          
          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 事件详情对话框 -->
    <el-dialog
      v-model="showEventDialog"
      title="安全事件详情"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedEvent">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件ID">{{ selectedEvent.id }}</el-descriptions-item>
          <el-descriptions-item label="事件级别">
            <el-tag :type="getSeverityTagType(selectedEvent.severity)">
              {{ getSeverityText(selectedEvent.severity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="事件类型">{{ getTypeText(selectedEvent.type) }}</el-descriptions-item>
          <el-descriptions-item label="发生时间">{{ selectedEvent.time }}</el-descriptions-item>
          <el-descriptions-item label="发生位置">{{ selectedEvent.location }}</el-descriptions-item>
          <el-descriptions-item label="相关设备">{{ selectedEvent.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getEventStatusType(selectedEvent.status)">
              {{ getEventStatusText(selectedEvent.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{ selectedEvent.handler || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="事件描述" :span="2">{{ selectedEvent.description }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;" v-if="selectedEvent.timeline">
          <h4>处理时间线</h4>
          <el-timeline>
            <el-timeline-item
              v-for="item in selectedEvent.timeline"
              :key="item.id"
              :timestamp="item.time"
              :type="item.type"
            >
              {{ item.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showEventDialog = false">关闭</el-button>
        <el-button type="primary" @click="handleEvent(selectedEvent)" v-if="selectedEvent?.status !== 'resolved'">
          处理事件
        </el-button>
      </template>
    </el-dialog>

    <!-- 事件处理对话框 -->
    <el-dialog
      v-model="showHandleDialog"
      title="处理安全事件"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="handleForm" :rules="handleRules" ref="handleFormRef" label-width="100px">
        <el-form-item label="处理方式" prop="action">
          <el-radio-group v-model="handleForm.action">
            <el-radio label="resolve">解决</el-radio>
            <el-radio label="ignore">忽略</el-radio>
            <el-radio label="escalate">升级</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="处理说明" prop="description">
          <el-input 
            v-model="handleForm.description" 
            type="textarea" 
            :rows="4" 
            placeholder="请详细描述处理过程和结果"
          />
        </el-form-item>
        
        <el-form-item label="处理人" prop="handler">
          <el-input v-model="handleForm.handler" placeholder="请输入处理人姓名" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showHandleDialog = false">取消</el-button>
        <el-button type="primary" @click="submitHandle">确认处理</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import {
  Refresh,
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  SwitchButton,
  Clock,
  Monitor,
  Timer,
  Bell,
  Search,
  DataAnalysis
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '设备资产管理', to: '/device-management' },
  { text: '安全监测' }
])

// 响应式数据
const loading = ref(false)
const selectedArea = ref('')
const activeTab = ref('events')
const autoRefresh = ref(true)
const overallStatus = ref('normal') // normal, warning, danger
const lastUpdateTime = ref('2024-01-15 14:30:25')

// 图表引用
const parameterChartRefs = new Map()
const eventTypeChart = ref()
const eventTrendChart = ref()
const safetyIndexChart = ref()
const efficiencyChart = ref()

// 安全指标
const safetyMetrics = reactive({
  normal: 135,
  warning: 8,
  danger: 2,
  offline: 3,
  todayEvents: 12
})

// 安全参数监控
const safetyParameters = ref([
  {
    id: 'voltage',
    name: '电压监测',
    currentValue: 385.2,
    unit: 'V',
    minValue: 360,
    maxValue: 420,
    status: 'normal',
    data: [382, 385, 383, 386, 385, 384, 385]
  },
  {
    id: 'current',
    name: '电流监测',
    currentValue: 125.8,
    unit: 'A',
    minValue: 0,
    maxValue: 200,
    status: 'warning',
    data: [120, 125, 128, 130, 126, 125, 126]
  },
  {
    id: 'temperature',
    name: '温度监测',
    currentValue: 45.6,
    unit: '℃',
    minValue: 0,
    maxValue: 60,
    status: 'normal',
    data: [42, 44, 45, 46, 45, 44, 46]
  },
  {
    id: 'leakage',
    name: '漏电监测',
    currentValue: 15.2,
    unit: 'mA',
    minValue: 0,
    maxValue: 30,
    status: 'normal',
    data: [12, 14, 15, 16, 15, 14, 15]
  }
])

// 最新安全事件
const latestEvents = ref([
  {
    id: 'EVT001',
    title: '会议室201电流异常',
    location: '办公大楼A-2楼',
    time: '2024-01-15 14:25:30',
    severity: 'major',
    type: 'current',
    status: 'pending'
  },
  {
    id: 'EVT002',
    title: '配电房温度过高',
    location: '地下配电房',
    time: '2024-01-15 13:45:15',
    severity: 'critical',
    type: 'temperature',
    status: 'processing'
  },
  {
    id: 'EVT003',
    title: '生产区漏电检测',
    location: '生产车间A区',
    time: '2024-01-15 12:30:45',
    severity: 'minor',
    type: 'leakage',
    status: 'resolved'
  }
])

// 安全事件列表
const safetyEvents = ref([
  {
    id: 'EVT001',
    severity: 'major',
    type: 'current',
    title: '会议室201空调控制器电流异常超标',
    location: '办公大楼A-2楼-会议室201',
    deviceName: '空调控制器-AC201',
    time: '2024-01-15 14:25:30',
    status: 'pending',
    handler: '',
    description: '检测到空调控制器电流值持续超过正常范围，可能存在设备故障风险。',
    timeline: [
      { id: 1, time: '2024-01-15 14:25:30', content: '系统自动检测到电流异常', type: 'warning' },
      { id: 2, time: '2024-01-15 14:26:00', content: '发送告警通知', type: 'info' }
    ]
  },
  {
    id: 'EVT002',
    severity: 'critical',
    type: 'temperature',
    title: '配电房环境温度严重超标',
    location: '地下配电房',
    deviceName: '温度传感器-TS001',
    time: '2024-01-15 13:45:15',
    status: 'processing',
    handler: '张工程师',
    description: '配电房环境温度达到65℃，严重超过安全阈值，需要立即处理。',
    timeline: [
      { id: 1, time: '2024-01-15 13:45:15', content: '温度传感器检测到异常高温', type: 'danger' },
      { id: 2, time: '2024-01-15 13:46:00', content: '自动启动紧急通风系统', type: 'warning' },
      { id: 3, time: '2024-01-15 13:50:00', content: '张工程师接手处理', type: 'info' }
    ]
  },
  {
    id: 'EVT003',
    severity: 'minor',
    type: 'leakage',
    title: '生产区域检测到微量漏电',
    location: '生产车间A区',
    deviceName: '漏电保护器-LP301',
    time: '2024-01-15 12:30:45',
    status: 'resolved',
    handler: '李技术员',
    description: '生产区域检测到5mA漏电流，已定位到具体设备并完成维修。',
    timeline: [
      { id: 1, time: '2024-01-15 12:30:45', content: '漏电保护器检测到漏电', type: 'warning' },
      { id: 2, time: '2024-01-15 12:35:00', content: '李技术员开始排查', type: 'info' },
      { id: 3, time: '2024-01-15 13:15:00', content: '定位到故障设备并完成维修', type: 'success' },
      { id: 4, time: '2024-01-15 13:20:00', content: '事件处理完成', type: 'success' }
    ]
  }
])

// 设备监控数据
const monitoringDevices = ref([
  {
    deviceId: 'DEV001',
    deviceName: '主配电柜电表',
    location: '地下配电房',
    safetyStatus: 'normal',
    voltage: 385.2,
    current: 125.8,
    temperature: 45.6,
    leakageCurrent: 2.1,
    lastCheckTime: '2024-01-15 14:30:00'
  },
  {
    deviceId: 'DEV002',
    deviceName: '空调控制器',
    location: '会议室201',
    safetyStatus: 'warning',
    voltage: 220.5,
    current: 185.2,
    temperature: 52.3,
    leakageCurrent: 8.5,
    lastCheckTime: '2024-01-15 14:25:00'
  },
  {
    deviceId: 'DEV003',
    deviceName: '照明控制器',
    location: '办公区A座',
    safetyStatus: 'normal',
    voltage: 220.1,
    current: 45.6,
    temperature: 38.2,
    leakageCurrent: 1.2,
    lastCheckTime: '2024-01-15 14:28:00'
  }
])

// 筛选条件
const eventFilters = reactive({
  severity: '',
  type: '',
  status: '',
  dateRange: [],
  keyword: ''
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(50)

// 对话框控制
const showEventDialog = ref(false)
const showHandleDialog = ref(false)
const selectedEvent = ref(null)

// 处理表单
const handleForm = reactive({
  action: 'resolve',
  description: '',
  handler: ''
})

const handleRules = {
  action: [{ required: true, message: '请选择处理方式', trigger: 'change' }],
  description: [{ required: true, message: '请输入处理说明', trigger: 'blur' }],
  handler: [{ required: true, message: '请输入处理人', trigger: 'blur' }]
}

const handleFormRef = ref()

// 自动刷新定时器
let refreshTimer = null

// 设置参数图表引用
const setParameterChartRef = (el, id) => {
  if (el) {
    parameterChartRefs.set(id, el)
  }
}

// 初始化参数图表
const initParameterCharts = () => {
  nextTick(() => {
    safetyParameters.value.forEach(param => {
      const chartElement = parameterChartRefs.get(param.id)
      if (chartElement) {
        const chart = echarts.init(chartElement)
        const option = {
          grid: {
            left: '3%',
            right: '3%',
            bottom: '3%',
            top: '3%'
          },
          xAxis: {
            type: 'category',
            show: false,
            data: ['1', '2', '3', '4', '5', '6', '7']
          },
          yAxis: {
            type: 'value',
            show: false,
            min: Math.min(...param.data) - 5,
            max: Math.max(...param.data) + 5
          },
          series: [
            {
              data: param.data,
              type: 'line',
              smooth: true,
              symbol: 'none',
              lineStyle: {
                color: param.status === 'normal' ? '#67c23a' : param.status === 'warning' ? '#e6a23c' : '#f56c6c',
                width: 2
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0,
                    color: param.status === 'normal' ? 'rgba(103, 194, 58, 0.3)' : param.status === 'warning' ? 'rgba(230, 162, 60, 0.3)' : 'rgba(245, 108, 108, 0.3)'
                  }, {
                    offset: 1,
                    color: 'rgba(255, 255, 255, 0)'
                  }]
                }
              }
            }
          ]
        }
        chart.setOption(option)
      }
    })
  })
}

// 初始化统计图表
const initStatisticsCharts = () => {
  nextTick(() => {
    // 事件类型分布图
    if (eventTypeChart.value) {
      const typeChart = echarts.init(eventTypeChart.value)
      const typeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '0%'
        },
        series: [
          {
            name: '事件类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 25, name: '电压异常' },
              { value: 20, name: '电流异常' },
              { value: 15, name: '温度异常' },
              { value: 12, name: '漏电检测' },
              { value: 18, name: '过载保护' },
              { value: 10, name: '短路保护' }
            ]
          }
        ]
      }
      typeChart.setOption(typeOption)
    }

    // 事件趋势分析图
    if (eventTrendChart.value) {
      const trendChart = echarts.init(eventTrendChart.value)
      const trendOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['紧急', '重要', '一般', '提醒']
        },
        xAxis: {
          type: 'category',
          data: ['01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '紧急',
            type: 'line',
            data: [2, 3, 1, 4, 2, 3, 2],
            itemStyle: { color: '#f56c6c' }
          },
          {
            name: '重要',
            type: 'line',
            data: [5, 7, 4, 8, 6, 7, 5],
            itemStyle: { color: '#e6a23c' }
          },
          {
            name: '一般',
            type: 'line',
            data: [8, 12, 9, 15, 11, 13, 10],
            itemStyle: { color: '#409eff' }
          },
          {
            name: '提醒',
            type: 'line',
            data: [15, 18, 12, 22, 16, 19, 14],
            itemStyle: { color: '#67c23a' }
          }
        ]
      }
      trendChart.setOption(trendOption)
    }

    // 区域安全指数图
    if (safetyIndexChart.value) {
      const indexChart = echarts.init(safetyIndexChart.value)
      const indexOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: ['办公大楼A', '办公大楼B', '生产车间', '配电房', '地下室']
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            name: '安全指数',
            type: 'bar',
            data: [
              { value: 95, itemStyle: { color: '#67c23a' } },
              { value: 92, itemStyle: { color: '#67c23a' } },
              { value: 88, itemStyle: { color: '#e6a23c' } },
              { value: 78, itemStyle: { color: '#f56c6c' } },
              { value: 90, itemStyle: { color: '#67c23a' } }
            ]
          }
        ]
      }
      indexChart.setOption(indexOption)
    }

    // 处理效率统计图
    if (efficiencyChart.value) {
      const effChart = echarts.init(efficiencyChart.value)
      const effOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['平均处理时间(分钟)', '处理成功率(%)']
        },
        xAxis: {
          type: 'category',
          data: ['01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15']
        },
        yAxis: [
          {
            type: 'value',
            name: '处理时间(分钟)',
            axisLabel: {
              formatter: '{value} min'
            }
          },
          {
            type: 'value',
            name: '成功率(%)',
            min: 80,
            max: 100,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '平均处理时间(分钟)',
            type: 'bar',
            data: [25, 22, 28, 20, 24, 26, 23],
            itemStyle: { color: '#409eff' }
          },
          {
            name: '处理成功率(%)',
            type: 'line',
            yAxisIndex: 1,
            data: [95, 96, 94, 98, 95, 97, 96],
            itemStyle: { color: '#67c23a' }
          }
        ]
      }
      effChart.setOption(effOption)
    }
  })
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap = {
    normal: 'CircleCheckFilled',
    warning: 'WarningFilled',
    danger: 'CircleCloseFilled'
  }
  return iconMap[status] || 'CircleCheckFilled'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    normal: '系统正常',
    warning: '存在告警',
    danger: '紧急状态'
  }
  return textMap[status] || '未知状态'
}

// 获取参数状态类型
const getParamStatusType = (status: string) => {
  const typeMap = {
    normal: 'success',
    warning: 'warning',
    danger: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取参数状态文本
const getParamStatusText = (status: string) => {
  const textMap = {
    normal: '正常',
    warning: '告警',
    danger: '危险'
  }
  return textMap[status] || '未知'
}

// 获取事件图标
const getEventIcon = (type: string) => {
  const iconMap = {
    voltage: 'Lightning',
    current: 'Lightning',
    temperature: 'Sunny',
    leakage: 'WarningFilled',
    overload: 'WarningFilled',
    'short-circuit': 'CircleCloseFilled'
  }
  return iconMap[type] || 'WarningFilled'
}

// 获取事件严重程度样式类
const getEventSeverityClass = (severity: string) => {
  return `severity-${severity}`
}

// 获取严重程度标签类型
const getSeverityTagType = (severity: string) => {
  const typeMap = {
    critical: 'danger',
    major: 'warning',
    minor: 'info',
    info: ''
  }
  return typeMap[severity] || 'info'
}

// 获取严重程度文本
const getSeverityText = (severity: string) => {
  const textMap = {
    critical: '紧急',
    major: '重要',
    minor: '一般',
    info: '提醒'
  }
  return textMap[severity] || '未知'
}

// 获取事件类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap = {
    voltage: 'warning',
    current: 'warning',
    temperature: 'danger',
    leakage: 'info',
    overload: 'warning',
    'short-circuit': 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取事件类型文本
const getTypeText = (type: string) => {
  const textMap = {
    voltage: '电压异常',
    current: '电流异常',
    temperature: '温度异常',
    leakage: '漏电检测',
    overload: '过载保护',
    'short-circuit': '短路保护'
  }
  return textMap[type] || '未知类型'
}

// 获取事件状态标签类型
const getEventStatusType = (status: string) => {
  const typeMap = {
    pending: 'danger',
    processing: 'warning',
    resolved: 'success',
    ignored: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取事件状态文本
const getEventStatusText = (status: string) => {
  const textMap = {
    pending: '待处理',
    processing: '处理中',
    resolved: '已处理',
    ignored: '已忽略'
  }
  return textMap[status] || '未知'
}

// 获取设备状态标签类型
const getDeviceStatusType = (status: string) => {
  const typeMap = {
    normal: 'success',
    warning: 'warning',
    danger: 'danger',
    offline: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取设备状态文本
const getDeviceStatusText = (status: string) => {
  const textMap = {
    normal: '正常',
    warning: '告警',
    danger: '危险',
    offline: '离线'
  }
  return textMap[status] || '未知'
}

// 获取数值样式类
const getValueClass = (value: number, min: number, max: number, reverse = false) => {
  if (reverse) {
    // 对于漏电流等，值越小越好
    if (value > max) return 'value-danger'
    if (value > max * 0.7) return 'value-warning'
    return 'value-normal'
  } else {
    // 对于电压电流等，在范围内为正常
    if (value < min || value > max) return 'value-danger'
    if (value < min * 1.1 || value > max * 0.9) return 'value-warning'
    return 'value-normal'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  const now = new Date()
  const eventTime = new Date(time)
  const diff = Math.floor((now.getTime() - eventTime.getTime()) / 1000 / 60) // 分钟差

  if (diff < 1) return '刚刚'
  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  return time.split(' ')[1].slice(0, 5) // 显示时分
}

// 事件处理函数
const handleTabClick = (tab) => {
  if (tab.props.name === 'statistics') {
    setTimeout(() => {
      initStatisticsCharts()
    }, 100)
  }
}

const refreshAll = () => {
  loading.value = true
  lastUpdateTime.value = new Date().toLocaleString('zh-CN')
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    startAutoRefresh()
    ElMessage.success('已开启自动刷新')
  } else {
    stopAutoRefresh()
    ElMessage.info('已停止自动刷新')
  }
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    // 模拟数据更新
    safetyParameters.value.forEach(param => {
      const variation = (Math.random() - 0.5) * 2
      param.currentValue = Math.max(param.minValue, Math.min(param.maxValue, param.currentValue + variation))
      param.data.shift()
      param.data.push(param.currentValue)
    })
    initParameterCharts()
  }, 5000) // 5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

const viewAllEvents = () => {
  activeTab.value = 'events'
}

const viewEventDetail = (event) => {
  selectedEvent.value = event
  showEventDialog.value = true
}

const handleEvent = (event) => {
  selectedEvent.value = event
  Object.assign(handleForm, {
    action: 'resolve',
    description: '',
    handler: '当前用户'
  })
  showHandleDialog.value = true
}

const submitHandle = () => {
  handleFormRef.value?.validate((valid) => {
    if (valid) {
      // 更新事件状态
      if (selectedEvent.value) {
        selectedEvent.value.status = handleForm.action === 'resolve' ? 'resolved' : 
                                   handleForm.action === 'ignore' ? 'ignored' : 'processing'
        selectedEvent.value.handler = handleForm.handler
      }
      
      ElMessage.success('事件处理成功')
      showHandleDialog.value = false
      showEventDialog.value = false
    }
  })
}

const exportEvent = (event) => {
  ElMessage.success(`事件 ${event.id} 导出成功`)
}

const searchEvents = () => {
  ElMessage.info('搜索功能开发中')
}

const viewDeviceDetail = (device) => {
  ElMessage.info(`查看设备 ${device.deviceName} 详情`)
}

const testDevice = (device) => {
  ElMessage.success(`设备 ${device.deviceName} 检测完成`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 组件挂载和卸载
onMounted(() => {
  initParameterCharts()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.safety-monitoring {
  padding: 0;
}

.safety-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.safety-overview :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.safety-overview :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.safety-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.safety-status.normal .status-icon {
  color: #67c23a;
}

.safety-status.warning .status-icon {
  color: #e6a23c;
}

.safety-status.danger .status-icon {
  color: #f56c6c;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.safety-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.metric-icon.normal {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border: 2px solid #67c23a;
}

.metric-icon.warning {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border: 2px solid #e6a23c;
}

.metric-icon.danger {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border: 2px solid #f56c6c;
}

.metric-icon.offline {
  background: rgba(144, 147, 153, 0.2);
  color: #909399;
  border: 2px solid #909399;
}

.metric-icon.info {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border: 2px solid #409eff;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  opacity: 0.9;
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

.monitoring-panel, .events-panel {
  height: 500px;
}

.monitoring-panel :deep(.el-card__body),
.events-panel :deep(.el-card__body) {
  padding: 20px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.parameter-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  height: 100%;
}

.parameter-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.param-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.param-value {
  display: flex;
  align-items: baseline;
  gap: 5px;
  margin-bottom: 8px;
}

.param-value .value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
}

.param-value .unit {
  font-size: 12px;
  color: #909399;
}

.param-range {
  font-size: 12px;
  color: #606266;
  margin-bottom: 10px;
}

.events-list {
  height: 100%;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s;
}

.event-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.event-item.severity-critical {
  border-left: 4px solid #f56c6c;
  background: rgba(245, 108, 108, 0.05);
}

.event-item.severity-major {
  border-left: 4px solid #e6a23c;
  background: rgba(230, 162, 60, 0.05);
}

.event-item.severity-minor {
  border-left: 4px solid #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.event-item.severity-info {
  border-left: 4px solid #67c23a;
  background: rgba(103, 194, 58, 0.05);
}

.event-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 18px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.event-location {
  font-size: 12px;
  color: #606266;
  margin-bottom: 2px;
}

.event-time {
  font-size: 11px;
  color: #909399;
}

.events-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.value-normal {
  color: #67c23a;
  font-weight: 600;
}

.value-warning {
  color: #e6a23c;
  font-weight: 600;
}

.value-danger {
  color: #f56c6c;
  font-weight: 600;
}

@media (max-width: 1200px) {
  .parameter-grid {
    grid-template-columns: 1fr;
  }
}

/* 筛选区域样式优化 */
.filter-row {
  margin-bottom: 20px;
}

/* 强制日期选择器宽度 */
.filter-row .el-date-editor {
  width: 220px !important;
}

.filter-row .el-date-editor .el-input__wrapper {
  width: 220px !important;
}



/* 响应式布局优化 */
@media (max-width: 1200px) {
  .filter-row .el-date-picker {
    width: 200px !important;
  }

  .filter-row .el-input {
    width: 120px !important;
  }
}

@media (max-width: 768px) {
  .safety-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  /* 移动端筛选区域优化 */
  .filter-row {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }

  .filter-row .el-select,
  .filter-row .el-date-picker,
  .filter-row .el-input,
  .filter-row .el-button {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .filter-row {
    margin-bottom: 16px;
  }
}
</style> 