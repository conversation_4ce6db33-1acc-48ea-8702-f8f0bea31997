<template>
  <div class="safety-protection">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 保护策略总览 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span style="font-weight: 600;">安全保护总览</span>
              <el-button type="primary" size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </template>
          
          <div class="protection-overview">
            <div class="overview-item">
              <div class="item-icon active">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-label">激活策略</div>
                <div class="item-value">{{ protectionStats.activeStrategies }}</div>
              </div>
            </div>
            
            <div class="overview-divider"></div>
            
            <div class="overview-item">
              <div class="item-icon total">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-label">总策略数</div>
                <div class="item-value">{{ protectionStats.totalStrategies }}</div>
              </div>
            </div>
            
            <div class="overview-divider"></div>
            
            <div class="overview-item">
              <div class="item-icon protected">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-label">受保护设备</div>
                <div class="item-value">{{ protectionStats.protectedDevices }}</div>
              </div>
            </div>
            
            <div class="overview-divider"></div>
            
            <div class="overview-item">
              <div class="item-icon events">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-label">今日触发</div>
                <div class="item-value">{{ protectionStats.todayTriggers }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 保护管理操作栏 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="24">
        <el-card>
          <div class="operation-bar">
            <div class="operation-left">
              <el-select v-model="filters.building" placeholder="选择建筑" style="width: 150px; margin-right: 10px;">
                <el-option label="全部建筑" value="" />
                <el-option label="主楼" value="main" />
                <el-option label="副楼" value="sub" />
                <el-option label="实验楼" value="lab" />
              </el-select>
              
              <el-select v-model="filters.strategyType" placeholder="策略类型" style="width: 150px; margin-right: 10px;">
                <el-option label="全部类型" value="" />
                <el-option label="过载保护" value="overload" />
                <el-option label="欠压保护" value="undervoltage" />
                <el-option label="漏电保护" value="leakage" />
                <el-option label="温度保护" value="temperature" />
              </el-select>
              
              <el-select v-model="filters.status" placeholder="策略状态" style="width: 120px; margin-right: 10px;">
                <el-option label="全部状态" value="" />
                <el-option label="启用" value="enabled" />
                <el-option label="禁用" value="disabled" />
              </el-select>
              
              <el-input 
                v-model="filters.keyword" 
                placeholder="搜索策略名称或设备" 
                style="width: 200px; margin-right: 10px;"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              
              <el-button type="primary" @click="searchStrategies">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </div>
            
            <div class="operation-right">
              <el-button type="success" @click="addStrategy">
                <el-icon><Plus /></el-icon>
                新增策略
              </el-button>
              <el-button type="info" @click="batchOperation">
                <el-icon><Operation /></el-icon>
                批量操作
              </el-button>
              <el-button type="warning" @click="exportStrategies">
                <el-icon><Download /></el-icon>
                导出策略
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 保护策略 -->
            <el-tab-pane label="保护策略" name="strategies">
              <div class="strategies-content">
                <!-- 策略列表 -->
                <el-table 
                  :data="protectionStrategies" 
                  border 
                  @selection-change="handleStrategySelectionChange"
                  style="width: 100%"
                >
                  <el-table-column type="selection" width="55" align="center" />
                  <el-table-column prop="id" label="策略ID" width="120" align="center" />
                  <el-table-column prop="name" label="策略名称" min-width="150" align="center" />
                  <el-table-column label="策略类型" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getStrategyTypeTag(row.type)">
                        {{ getStrategyTypeText(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="threshold" label="触发阈值" width="100" align="center" />
                  <el-table-column prop="deviceCount" label="关联设备" width="100" align="center" />
                  <el-table-column label="策略状态" width="100" align="center">
                    <template #default="{ row }">
                      <el-switch 
                        v-model="row.enabled" 
                        @change="toggleStrategy(row)"
                        :disabled="loading"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="priority" label="优先级" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getPriorityTag(row.priority)" size="small">
                        {{ row.priority }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" width="140" align="center" />
                  <el-table-column prop="updateTime" label="更新时间" width="140" align="center" />
                  <el-table-column label="操作" width="200" align="center" fixed="right">
                    <template #default="{ row }">
                      <div style="white-space: nowrap;">
                        <el-button type="primary" size="small" @click="editStrategy(row)" style="margin-right: 5px;">
                          编辑
                        </el-button>
                        <el-button type="info" size="small" @click="viewStrategy(row)" style="margin-right: 5px;">
                          查看
                        </el-button>
                        <el-button type="danger" size="small" @click="deleteStrategy(row)">
                          删除
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="text-align: center; margin-top: 20px;">
                  <el-pagination
                    v-model:current-page="pagination.currentPage"
                    v-model:page-size="pagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pagination.total"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                  />
                </div>
              </div>
            </el-tab-pane>
            
            <!-- 安全配置 -->
            <el-tab-pane label="安全配置" name="config">
              <div class="config-content">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">全局安全配置</span>
                      </template>
                      
                      <el-form :model="globalConfig" label-width="120px">
                        <el-form-item label="监测频率">
                          <el-select v-model="globalConfig.monitorInterval" style="width: 100%;">
                            <el-option label="实时监测" value="realtime" />
                            <el-option label="每秒监测" value="1s" />
                            <el-option label="每5秒监测" value="5s" />
                            <el-option label="每10秒监测" value="10s" />
                          </el-select>
                        </el-form-item>
                        
                        <el-form-item label="报警延迟">
                          <el-input-number 
                            v-model="globalConfig.alarmDelay" 
                            :min="0" 
                            :max="60" 
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">秒</span>
                        </el-form-item>
                        
                        <el-form-item label="自动断电">
                          <el-switch v-model="globalConfig.autoShutdown" />
                        </el-form-item>
                        
                        <el-form-item label="紧急联系人">
                          <el-input v-model="globalConfig.emergencyContact" placeholder="输入手机号" />
                        </el-form-item>
                        
                        <el-form-item>
                          <el-button type="primary" @click="saveGlobalConfig">保存配置</el-button>
                          <el-button @click="resetGlobalConfig">重置</el-button>
                        </el-form-item>
                      </el-form>
                    </el-card>
                  </el-col>
                  
                  <el-col :span="12">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">阈值配置</span>
                      </template>
                      
                      <el-form :model="thresholdConfig" label-width="120px">
                        <el-form-item label="电压上限">
                          <el-input-number 
                            v-model="thresholdConfig.voltageMax" 
                            :precision="1"
                            :step="0.1"
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">V</span>
                        </el-form-item>
                        
                        <el-form-item label="电压下限">
                          <el-input-number 
                            v-model="thresholdConfig.voltageMin" 
                            :precision="1"
                            :step="0.1"
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">V</span>
                        </el-form-item>
                        
                        <el-form-item label="电流上限">
                          <el-input-number 
                            v-model="thresholdConfig.currentMax" 
                            :precision="1"
                            :step="0.1"
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">A</span>
                        </el-form-item>
                        
                        <el-form-item label="温度上限">
                          <el-input-number 
                            v-model="thresholdConfig.temperatureMax" 
                            :min="0" 
                            :max="100"
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">℃</span>
                        </el-form-item>
                        
                        <el-form-item label="漏电流阈值">
                          <el-input-number 
                            v-model="thresholdConfig.leakageMax" 
                            :precision="1"
                            :step="0.1"
                            style="width: 100%;"
                          />
                          <span style="margin-left: 8px; color: #909399;">mA</span>
                        </el-form-item>
                        
                        <el-form-item>
                          <el-button type="primary" @click="saveThresholdConfig">保存阈值</el-button>
                          <el-button @click="resetThresholdConfig">重置</el-button>
                        </el-form-item>
                      </el-form>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
            
            <!-- 防护措施 -->
            <el-tab-pane label="防护措施" name="protection">
              <div class="protection-content">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">过载保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status enabled">
                          <el-icon><CircleCheckFilled /></el-icon>
                          <span>已启用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">45</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">12</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">2小时前</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="primary" size="small" @click="configProtection('overload')">
                            配置
                          </el-button>
                          <el-button type="info" size="small" @click="testProtection('overload')">
                            测试
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">漏电保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status enabled">
                          <el-icon><CircleCheckFilled /></el-icon>
                          <span>已启用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">38</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">8</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">1天前</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="primary" size="small" @click="configProtection('leakage')">
                            配置
                          </el-button>
                          <el-button type="info" size="small" @click="testProtection('leakage')">
                            测试
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">温度保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status disabled">
                          <el-icon><CircleCloseFilled /></el-icon>
                          <span>已禁用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">0</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">0</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">从未</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="success" size="small" @click="enableProtection('temperature')">
                            启用
                          </el-button>
                          <el-button type="primary" size="small" @click="configProtection('temperature')">
                            配置
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20" style="margin-top: 20px;">
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">短路保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status enabled">
                          <el-icon><CircleCheckFilled /></el-icon>
                          <span>已启用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">52</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">3</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">3天前</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="primary" size="small" @click="configProtection('shortcircuit')">
                            配置
                          </el-button>
                          <el-button type="info" size="small" @click="testProtection('shortcircuit')">
                            测试
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">欠压保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status enabled">
                          <el-icon><CircleCheckFilled /></el-icon>
                          <span>已启用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">41</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">15</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">6小时前</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="primary" size="small" @click="configProtection('undervoltage')">
                            配置
                          </el-button>
                          <el-button type="info" size="small" @click="testProtection('undervoltage')">
                            测试
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                  
                  <el-col :span="8">
                    <el-card>
                      <template #header>
                        <span style="font-weight: 600;">过压保护</span>
                      </template>
                      
                      <div class="protection-item">
                        <div class="protection-status enabled">
                          <el-icon><CircleCheckFilled /></el-icon>
                          <span>已启用</span>
                        </div>
                        
                        <div class="protection-info">
                          <div class="info-item">
                            <span class="label">保护设备数：</span>
                            <span class="value">47</span>
                          </div>
                          <div class="info-item">
                            <span class="label">触发次数：</span>
                            <span class="value">7</span>
                          </div>
                          <div class="info-item">
                            <span class="label">最后触发：</span>
                            <span class="value">1天前</span>
                          </div>
                        </div>
                        
                        <div class="protection-actions">
                          <el-button type="primary" size="small" @click="configProtection('overvoltage')">
                            配置
                          </el-button>
                          <el-button type="info" size="small" @click="testProtection('overvoltage')">
                            测试
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
            
            <!-- 触发记录 -->
            <el-tab-pane label="触发记录" name="records">
              <div class="records-content">
                <!-- 记录筛选 -->
                <div class="records-filter" style="margin-bottom: 20px;">
                  <el-form :inline="true">
                    <el-form-item label="保护类型">
                      <el-select v-model="recordFilters.type" placeholder="选择类型" style="width: 150px;">
                        <el-option label="全部类型" value="" />
                        <el-option label="过载保护" value="overload" />
                        <el-option label="漏电保护" value="leakage" />
                        <el-option label="短路保护" value="shortcircuit" />
                        <el-option label="欠压保护" value="undervoltage" />
                        <el-option label="过压保护" value="overvoltage" />
                        <el-option label="温度保护" value="temperature" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="时间范围">
                      <el-date-picker
                        v-model="recordFilters.dateRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        style="width: 350px;"
                      />
                    </el-form-item>
                    
                    <el-form-item>
                      <el-button type="primary" @click="searchRecords">
                        <el-icon><Search /></el-icon>
                        查询记录
                      </el-button>
                      <el-button @click="resetRecordFilters">重置</el-button>
                    </el-form-item>
                  </el-form>
                </div>
                
                <!-- 触发记录表格 -->
                <el-table 
                  :data="triggerRecords" 
                  border 
                  @selection-change="handleRecordSelectionChange"
                  style="width: 100%"
                >
                  <el-table-column type="selection" width="55" align="center" />
                  <el-table-column prop="id" label="记录ID" width="120" align="center" />
                  <el-table-column label="保护类型" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getProtectionTypeTag(row.type)">
                        {{ getProtectionTypeText(row.type) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="deviceName" label="触发设备" min-width="150" align="center" />
                  <el-table-column prop="location" label="设备位置" min-width="120" align="center" />
                  <el-table-column prop="triggerValue" label="触发值" width="100" align="center" />
                  <el-table-column prop="thresholdValue" label="阈值" width="100" align="center" />
                  <el-table-column prop="triggerTime" label="触发时间" width="140" align="center" />
                  <el-table-column label="处理状态" width="100" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getRecordStatusTag(row.status)" size="small">
                        {{ getRecordStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="recoveryTime" label="恢复时间" width="140" align="center" />
                  <el-table-column label="操作" width="200" align="center" fixed="right">
                    <template #default="{ row }">
                      <div style="white-space: nowrap;">
                        <el-button type="primary" size="small" @click="viewRecord(row)" style="margin-right: 5px;">
                          查看
                        </el-button>
                        <el-button type="info" size="small" @click="exportRecord(row)" style="margin-right: 5px;">
                          导出
                        </el-button>
                        <el-button type="danger" size="small" @click="deleteRecord(row)">
                          删除
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                
                <!-- 分页 -->
                <div style="text-align: center; margin-top: 20px;">
                  <el-pagination
                    v-model:current-page="recordPagination.currentPage"
                    v-model:page-size="recordPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="recordPagination.total"
                    @size-change="handleRecordSizeChange"
                    @current-change="handleRecordCurrentChange"
                  />
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 策略详情对话框 -->
    <el-dialog 
      v-model="strategyDetailVisible" 
      title="策略详情" 
      width="800px"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="2" border v-if="selectedStrategy">
        <el-descriptions-item label="策略ID">{{ selectedStrategy.id }}</el-descriptions-item>
        <el-descriptions-item label="策略名称">{{ selectedStrategy.name }}</el-descriptions-item>
        <el-descriptions-item label="策略类型">
          <el-tag :type="getStrategyTypeTag(selectedStrategy.type)">
            {{ getStrategyTypeText(selectedStrategy.type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityTag(selectedStrategy.priority)" size="small">
            {{ selectedStrategy.priority }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="触发阈值">{{ selectedStrategy.threshold }}</el-descriptions-item>
        <el-descriptions-item label="关联设备">{{ selectedStrategy.deviceCount }} 台</el-descriptions-item>
        <el-descriptions-item label="策略状态">
          <el-tag :type="selectedStrategy.enabled ? 'success' : 'danger'">
            {{ selectedStrategy.enabled ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ selectedStrategy.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" :span="2">{{ selectedStrategy.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="策略描述" :span="2">{{ selectedStrategy.description }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="strategyDetailVisible = false">关闭</el-button>
          <el-button type="primary" @click="editStrategy(selectedStrategy)">编辑策略</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 添加/编辑策略对话框 -->
    <el-dialog 
      v-model="strategyFormVisible" 
      :title="isEdit ? '编辑策略' : '新增策略'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="strategyForm" :rules="strategyRules" ref="strategyFormRef" label-width="100px">
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="strategyForm.name" placeholder="请输入策略名称" />
        </el-form-item>
        
        <el-form-item label="策略类型" prop="type">
          <el-select v-model="strategyForm.type" placeholder="选择策略类型" style="width: 100%;">
            <el-option label="过载保护" value="overload" />
            <el-option label="欠压保护" value="undervoltage" />
            <el-option label="过压保护" value="overvoltage" />
            <el-option label="漏电保护" value="leakage" />
            <el-option label="短路保护" value="shortcircuit" />
            <el-option label="温度保护" value="temperature" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="触发阈值" prop="threshold">
          <el-input v-model="strategyForm.threshold" placeholder="请输入触发阈值" />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="strategyForm.priority" placeholder="选择优先级" style="width: 100%;">
            <el-option label="高" value="高" />
            <el-option label="中" value="中" />
            <el-option label="低" value="低" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="策略描述" prop="description">
          <el-input 
            v-model="strategyForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入策略描述"
          />
        </el-form-item>
        
        <el-form-item label="启用状态">
          <el-switch v-model="strategyForm.enabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="strategyFormVisible = false">取消</el-button>
          <el-button type="primary" @click="saveStrategy" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Refresh, Lock, Setting, CircleCheckFilled, WarningFilled, Search, Plus, 
  Operation, Download, CircleCloseFilled
} from '@element-plus/icons-vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '首页', to: '/' },
  { text: '设备资产管理', to: '/device-management' },
  { text: '安全保护', to: '/device-management/safety-protection' }
])

// 加载状态
const loading = ref(false)

// 保护统计数据
const protectionStats = reactive({
  activeStrategies: 15,
  totalStrategies: 18,
  protectedDevices: 142,
  todayTriggers: 23
})

// 筛选条件
const filters = reactive({
  building: '',
  strategyType: '',
  status: '',
  keyword: ''
})

// 当前活动标签
const activeTab = ref('strategies')

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 50
})

// 保护策略数据
const protectionStrategies = ref([
  {
    id: 'PS001',
    name: '主配电室过载保护',
    type: 'overload',
    threshold: '85%',
    deviceCount: 12,
    enabled: true,
    priority: '高',
    createTime: '2024-01-15 09:30:00',
    updateTime: '2024-01-20 14:22:00',
    description: '主配电室设备过载保护策略，当负载超过85%时触发保护'
  },
  {
    id: 'PS002',
    name: '实验室漏电保护',
    type: 'leakage',
    threshold: '30mA',
    deviceCount: 8,
    enabled: true,
    priority: '高',
    createTime: '2024-01-16 10:15:00',
    updateTime: '2024-01-22 16:45:00',
    description: '实验室区域漏电保护，漏电流超过30mA时断电保护'
  },
  {
    id: 'PS003',
    name: '办公区欠压保护',
    type: 'undervoltage',
    threshold: '200V',
    deviceCount: 25,
    enabled: false,
    priority: '中',
    createTime: '2024-01-17 11:20:00',
    updateTime: '2024-01-23 09:10:00',
    description: '办公区域欠压保护，电压低于200V时发出警告'
  },
  {
    id: 'PS004',
    name: '机房温度保护',
    type: 'temperature',
    threshold: '45℃',
    deviceCount: 6,
    enabled: true,
    priority: '高',
    createTime: '2024-01-18 13:45:00',
    updateTime: '2024-01-24 11:30:00',
    description: '机房设备温度保护，温度超过45℃时强制降温'
  },
  {
    id: 'PS005',
    name: '生产线短路保护',
    type: 'shortcircuit',
    threshold: '瞬时',
    deviceCount: 18,
    enabled: true,
    priority: '高',
    createTime: '2024-01-19 08:00:00',
    updateTime: '2024-01-25 15:20:00',
    description: '生产线短路保护，检测到短路时立即断电'
  }
])

// 全局配置
const globalConfig = reactive({
  monitorInterval: 'realtime',
  alarmDelay: 5,
  autoShutdown: true,
  emergencyContact: '13800138000'
})

// 阈值配置
const thresholdConfig = reactive({
  voltageMax: 400.0,
  voltageMin: 180.0,
  currentMax: 200.0,
  temperatureMax: 50,
  leakageMax: 30.0
})

// 触发记录筛选
const recordFilters = reactive({
  type: '',
  dateRange: []
})

// 触发记录分页
const recordPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 30
})

// 触发记录数据
const triggerRecords = ref([
  {
    id: 'TR001',
    type: 'overload',
    deviceName: '主配电柜A',
    location: '1楼配电室',
    triggerValue: '88%',
    thresholdValue: '85%',
    triggerTime: '2024-01-25 14:30:25',
    status: 'resolved',
    recoveryTime: '2024-01-25 14:32:10'
  },
  {
    id: 'TR002',
    type: 'leakage',
    deviceName: '实验台电源',
    location: '3楼实验室',
    triggerValue: '35mA',
    thresholdValue: '30mA',
    triggerTime: '2024-01-25 10:15:42',
    status: 'resolved',
    recoveryTime: '2024-01-25 10:18:30'
  },
  {
    id: 'TR003',
    type: 'undervoltage',
    deviceName: '办公照明回路',
    location: '2楼办公区',
    triggerValue: '195V',
    thresholdValue: '200V',
    triggerTime: '2024-01-25 09:45:18',
    status: 'processing',
    recoveryTime: '-'
  }
])

// 对话框状态
const strategyDetailVisible = ref(false)
const strategyFormVisible = ref(false)
const isEdit = ref(false)
const selectedStrategy = ref(null)

// 策略表单
const strategyForm = reactive({
  name: '',
  type: '',
  threshold: '',
  priority: '',
  description: '',
  enabled: true
})

// 表单验证规则
const strategyRules = {
  name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择策略类型', trigger: 'change' }],
  threshold: [{ required: true, message: '请输入触发阈值', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

const strategyFormRef = ref()

// 刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

// 搜索策略
const searchStrategies = () => {
  console.log('搜索策略:', filters)
  ElMessage.info('搜索功能开发中')
}

// 添加策略
const addStrategy = () => {
  isEdit.value = false
  Object.assign(strategyForm, {
    name: '',
    type: '',
    threshold: '',
    priority: '',
    description: '',
    enabled: true
  })
  strategyFormVisible.value = true
}

// 批量操作
const batchOperation = () => {
  ElMessage.info('批量操作功能开发中')
}

// 导出策略
const exportStrategies = () => {
  ElMessage.success('策略导出成功')
}

// 处理策略选择变化
const handleStrategySelectionChange = (selection: any[]) => {
  console.log('选中的策略:', selection)
}

// 处理记录选择变化
const handleRecordSelectionChange = (selection: any[]) => {
  console.log('选中的记录:', selection)
}

// 删除记录
const deleteRecord = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除记录 "${row.id}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('记录删除成功')
  })
}

// 切换策略状态
const toggleStrategy = (row: any) => {
  const action = row.enabled ? '启用' : '禁用'
  ElMessage.success(`策略${action}成功`)
}

// 查看策略详情
const viewStrategy = (row: any) => {
  selectedStrategy.value = row
  strategyDetailVisible.value = true
}

// 编辑策略
const editStrategy = (row: any) => {
  isEdit.value = true
  Object.assign(strategyForm, row)
  strategyFormVisible.value = true
}

// 测试策略
const testStrategy = (row: any) => {
  ElMessageBox.confirm(
    `确定要测试策略 "${row.name}" 吗？`,
    '测试确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('策略测试成功')
  })
}

// 删除策略
const deleteStrategy = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除策略 "${row.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('策略删除成功')
  })
}

// 保存策略
const saveStrategy = () => {
  strategyFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        const action = isEdit.value ? '更新' : '创建'
        ElMessage.success(`策略${action}成功`)
        strategyFormVisible.value = false
      }, 1000)
    }
  })
}

// 保存全局配置
const saveGlobalConfig = () => {
  ElMessage.success('全局配置保存成功')
}

// 重置全局配置
const resetGlobalConfig = () => {
  Object.assign(globalConfig, {
    monitorInterval: 'realtime',
    alarmDelay: 5,
    autoShutdown: true,
    emergencyContact: '13800138000'
  })
  ElMessage.info('全局配置已重置')
}

// 保存阈值配置
const saveThresholdConfig = () => {
  ElMessage.success('阈值配置保存成功')
}

// 重置阈值配置
const resetThresholdConfig = () => {
  Object.assign(thresholdConfig, {
    voltageMax: 400.0,
    voltageMin: 180.0,
    currentMax: 200.0,
    temperatureMax: 50,
    leakageMax: 30.0
  })
  ElMessage.info('阈值配置已重置')
}

// 配置保护
const configProtection = (type: string) => {
  ElMessage.info(`配置${getProtectionTypeText(type)}功能开发中`)
}

// 测试保护
const testProtection = (type: string) => {
  ElMessage.info(`测试${getProtectionTypeText(type)}功能开发中`)
}

// 启用保护
const enableProtection = (type: string) => {
  ElMessage.success(`${getProtectionTypeText(type)}已启用`)
}

// 搜索记录
const searchRecords = () => {
  console.log('搜索记录:', recordFilters)
  ElMessage.info('记录搜索功能开发中')
}

// 重置记录筛选
const resetRecordFilters = () => {
  recordFilters.type = ''
  recordFilters.dateRange = []
  ElMessage.info('筛选条件已重置')
}

// 查看记录详情
const viewRecord = (row: any) => {
  ElMessage.info('查看记录详情功能开发中')
}

// 导出记录
const exportRecord = (row: any) => {
  ElMessage.success('记录导出成功')
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

const handleRecordSizeChange = (size: number) => {
  recordPagination.pageSize = size
  recordPagination.currentPage = 1
}

const handleRecordCurrentChange = (page: number) => {
  recordPagination.currentPage = page
}

// 获取策略类型标签
const getStrategyTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    overload: 'danger',
    leakage: 'warning',
    undervoltage: 'info',
    overvoltage: 'info',
    shortcircuit: 'danger',
    temperature: 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取策略类型文本
const getStrategyTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    overload: '过载保护',
    leakage: '漏电保护',
    undervoltage: '欠压保护',
    overvoltage: '过压保护',
    shortcircuit: '短路保护',
    temperature: '温度保护'
  }
  return typeMap[type] || type
}

// 获取优先级标签
const getPriorityTag = (priority: string) => {
  const priorityMap: Record<string, string> = {
    高: 'danger',
    中: 'warning',
    低: 'info'
  }
  return priorityMap[priority] || 'info'
}

// 获取保护类型标签
const getProtectionTypeTag = (type: string) => {
  return getStrategyTypeTag(type)
}

// 获取保护类型文本
const getProtectionTypeText = (type: string) => {
  return getStrategyTypeText(type)
}

// 获取记录状态标签
const getRecordStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    resolved: 'success',
    processing: 'warning',
    pending: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取记录状态文本
const getRecordStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    resolved: '已处理',
    processing: '处理中',
    pending: '待处理'
  }
  return statusMap[status] || status
}

// 生命周期
onMounted(() => {
  // 初始化数据
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.safety-protection {
  padding: 10px;
}

/* 保护总览样式 */
.protection-overview {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 0;
}

.overview-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.item-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.item-icon.active {
  background-color: #67c23a;
}

.item-icon.total {
  background-color: #409eff;
}

.item-icon.protected {
  background-color: #e6a23c;
}

.item-icon.events {
  background-color: #f56c6c;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.item-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.overview-divider {
  width: 1px;
  height: 60%;
  background-color: #ebeef5;
  margin: 0 20px;
}

/* 操作栏样式 */
.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.operation-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.operation-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 保护项样式 */
.protection-item {
  padding: 20px;
}

.protection-status {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 600;
}

.protection-status.enabled {
  color: #67c23a;
}

.protection-status.disabled {
  color: #f56c6c;
}

.protection-status .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.protection-info {
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.protection-actions {
  display: flex;
  gap: 8px;
}

/* 记录筛选样式 */
.records-filter {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .protection-overview {
    flex-direction: column;
    gap: 15px;
  }
  
  .overview-divider {
    width: 90%;
    height: 1px;
    margin: 0;
  }
  
  .operation-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operation-left,
  .operation-right {
    justify-content: center;
  }
}
</style> 