version: '3.8'

services:
  # 开发环境
  energy-management-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development-stage
    container_name: energy-management-dev
    ports:
      - "3003:3003"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    restart: unless-stopped
    profiles:
      - dev

  # 生产环境
  energy-management-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production-stage
    container_name: energy-management-prod
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    profiles:
      - prod

networks:
  default:
    name: energy-management-network
