<template>
  <div class="exception-records" v-loading="loading" element-loading-text="正在加载异常记录...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 筛选查询 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>异常记录查询</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="exportRecords">
              <el-icon><Download /></el-icon>
              导出记录
            </el-button>
            <el-button type="warning" size="small" @click="clearFilters">
              <el-icon><Refresh /></el-icon>
              清空筛选
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <label>异常类型：</label>
            <el-select v-model="filters.exceptionType" placeholder="请选择异常类型" size="small" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="能耗超限" value="overconsumption" />
              <el-option label="设备故障" value="device-fault" />
              <el-option label="数据异常" value="data-abnormal" />
              <el-option label="通信中断" value="communication-lost" />
              <el-option label="环境异常" value="environment-abnormal" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>异常级别：</label>
            <el-select v-model="filters.severity" placeholder="请选择异常级别" size="small" clearable @change="handleFilterChange">
              <el-option label="全部级别" value="" />
              <el-option label="紧急" value="critical" />
              <el-option label="警告" value="warning" />
              <el-option label="提醒" value="info" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>处理状态：</label>
            <el-select v-model="filters.status" placeholder="请选择处理状态" size="small" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="未处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已解决" value="resolved" />
              <el-option label="已忽略" value="ignored" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>关键字搜索：</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="设备名称、异常描述" 
              size="small" 
              clearable
              @input="handleKeywordSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="12">
          <div class="filter-item">
            <label>时间范围：</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="filter-item">
            <label>设备分类：</label>
            <el-checkbox-group v-model="filters.deviceTypes" size="small" @change="handleFilterChange">
              <el-checkbox label="lighting">照明设备</el-checkbox>
              <el-checkbox label="hvac">空调设备</el-checkbox>
              <el-checkbox label="power">电力设备</el-checkbox>
              <el-checkbox label="monitoring">监测设备</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 异常统计概览 -->
    <el-card class="overview-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>异常统计概览</h3>
          <div class="header-controls">
            <el-select v-model="selectedBuilding" size="small" placeholder="选择建筑" style="width: 120px;" @change="handleBuildingChange">
              <el-option label="全部建筑" value="" />
              <el-option label="办公大楼A" value="building-a" />
              <el-option label="办公大楼B" value="building-b" />
              <el-option label="生产车间" value="workshop" />
              <el-option label="配套设施" value="facilities" />
            </el-select>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="exception-stats">
        <div class="stat-item" v-for="(item, index) in exceptionStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-value-container">
              <span class="stat-value">{{ item.value }}</span>
              <span class="stat-unit">{{ item.unit }}</span>
            </div>
            <div class="stat-trend" :style="{ color: item.trendColor }">{{ item.trend }}</div>
          </div>
          <div class="stat-divider" v-if="index < exceptionStats.length - 1"></div>
        </div>
      </div>
    </el-card>

    <!-- 异常趋势图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>异常趋势分析</h3>
              <div class="header-controls">
                <el-radio-group v-model="chartTimeRange" size="small" @change="handleChartTimeRangeChange">
                  <el-radio-button label="7d">近7天</el-radio-button>
                  <el-radio-button label="30d">近30天</el-radio-button>
                  <el-radio-button label="90d">近3个月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="exceptionTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>异常类型分布</h3>
            </div>
          </template>
          <div ref="exceptionTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 异常记录列表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>异常记录列表</h3>
          <div class="header-controls">
            <span class="record-count">共 {{ filteredRecords.length }} 条记录</span>
            <el-button type="danger" size="small" @click="batchProcess" :disabled="selectedRecords.length === 0">
              <el-icon><Operation /></el-icon>
              批量处理 ({{ selectedRecords.length }})
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="paginatedRecords" 
        border 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="异常ID" width="100" align="center" />
        <el-table-column prop="timestamp" label="发生时间" width="160" align="center" />
        <el-table-column prop="deviceName" label="设备名称" min-width="120" align="center" />
        <el-table-column prop="location" label="位置" min-width="120" align="center" />
        <el-table-column prop="exceptionType" label="异常类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getExceptionTypeColor(row.exceptionType)" size="small">
              {{ getExceptionTypeName(row.exceptionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="severity" label="级别" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getSeverityColor(row.severity)" size="small">
              {{ getSeverityName(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="异常描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="处理人" width="100" align="center" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)" style="margin-right: 5px;">
              详情
            </el-button>
            <el-button 
              v-if="row.status === 'pending'" 
              type="warning" 
              size="small" 
              @click="processException(row)"
              style="margin-right: 5px;"
            >
              处理
            </el-button>
            <el-button 
              v-if="row.status === 'processing'" 
              type="success" 
              size="small" 
              @click="resolveException(row)"
            >
              解决
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredRecords.length"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 异常详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="异常详情"
      width="70%"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedRecord">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="异常ID">{{ selectedRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="发生时间">{{ selectedRecord.timestamp }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ selectedRecord.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="设备位置">{{ selectedRecord.location }}</el-descriptions-item>
          <el-descriptions-item label="异常类型">
            <el-tag :type="getExceptionTypeColor(selectedRecord.exceptionType)" size="small">
              {{ getExceptionTypeName(selectedRecord.exceptionType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="异常级别">
            <el-tag :type="getSeverityColor(selectedRecord.severity)" size="small">
              {{ getSeverityName(selectedRecord.severity) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理状态" :span="3">
            <el-tag :type="getStatusColor(selectedRecord.status)" size="small">
              {{ getStatusName(selectedRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="异常描述" :span="3">{{ selectedRecord.description }}</el-descriptions-item>
          <el-descriptions-item label="异常数值" :span="3">{{ selectedRecord.exceptionValue }}</el-descriptions-item>
          <el-descriptions-item label="处理人">{{ selectedRecord.handler || '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ selectedRecord.processTime || '未处理' }}</el-descriptions-item>
          <el-descriptions-item label="解决时间">{{ selectedRecord.resolveTime || '未解决' }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;" v-if="selectedRecord.historicalData">
          <h4>历史数据趋势</h4>
          <div ref="detailChart" style="height: 300px;"></div>
        </div>
        
        <div style="margin-top: 20px;" v-if="selectedRecord.processingLog && selectedRecord.processingLog.length > 0">
          <h4>处理记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in selectedRecord.processingLog"
              :key="index"
              :timestamp="log.timestamp"
              :type="log.type"
            >
              <strong>{{ log.operator }}</strong> {{ log.action }}
              <div v-if="log.remark" style="color: #999; margin-top: 5px;">{{ log.remark }}</div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="exportDetailData">导出详情</el-button>
        <el-button 
          v-if="selectedRecord && selectedRecord.status === 'pending'" 
          type="warning" 
          @click="processException(selectedRecord)"
        >
          立即处理
        </el-button>
      </template>
    </el-dialog>

    <!-- 处理异常对话框 -->
    <el-dialog
      v-model="showProcessDialog"
      title="处理异常"
      width="50%"
    >
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="处理方式">
          <el-radio-group v-model="processForm.method">
            <el-radio label="manual">人工处理</el-radio>
            <el-radio label="auto">自动处理</el-radio>
            <el-radio label="ignore">忽略异常</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理说明">
          <el-input 
            v-model="processForm.remark" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入处理说明"
          />
        </el-form-item>
        <el-form-item label="预计完成">
          <el-date-picker
            v-model="processForm.expectedTime"
            type="datetime"
            placeholder="选择预计完成时间"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showProcessDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmProcess">确认处理</el-button>
      </template>
    </el-dialog>

    <!-- 批量处理对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      title="批量处理异常"
      width="50%"
    >
      <div style="margin-bottom: 20px;">
        <strong>已选择 {{ selectedRecords.length }} 条记录</strong>
      </div>
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="批量操作">
          <el-radio-group v-model="batchForm.action">
            <el-radio label="process">标记为处理中</el-radio>
            <el-radio label="resolve">标记为已解决</el-radio>
            <el-radio label="ignore">批量忽略</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理说明">
          <el-input 
            v-model="batchForm.remark" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入批量处理说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchProcess">确认处理</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { 
  Refresh, Download, Search, Operation,
  WarningFilled, InfoFilled, CircleCheck, Tools
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '异常记录', to: '' }
])

// 基础数据
const loading = ref(false)
const selectedBuilding = ref('')
const chartTimeRange = ref('30d')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedRecords = ref<any[]>([])
const showDetailDialog = ref(false)
const showProcessDialog = ref(false)
const showBatchDialog = ref(false)
const selectedRecord = ref<any>(null)

// 图表引用
const exceptionTrendChart = ref<HTMLElement>()
const exceptionTypeChart = ref<HTMLElement>()
const detailChart = ref<HTMLElement>()

// 筛选条件
const filters = ref({
  exceptionType: '',
  severity: '',
  status: '',
  keyword: '',
  dateRange: null as [Date, Date] | null,
  deviceTypes: ['lighting', 'hvac', 'power', 'monitoring']
})

// 处理表单
const processForm = ref({
  method: 'manual',
  remark: '',
  expectedTime: null as Date | null
})

// 批量处理表单
const batchForm = ref({
  action: 'process',
  remark: ''
})

// 异常统计数据
const exceptionStats = ref([
  {
    name: '今日异常',
    value: '23',
    unit: '条',
    trend: '↑ 12.5%',
    trendColor: '#f56c6c',
    icon: 'WarningFilled',
    color: '#f56c6c'
  },
  {
    name: '未处理',
    value: '8',
    unit: '条',
    trend: '↑ 33.3%',
    trendColor: '#f56c6c',
    icon: 'InfoFilled',
    color: '#e6a23c'
  },
  {
    name: '处理中',
    value: '12',
    unit: '条',
    trend: '↓ 8.3%',
    trendColor: '#67c23a',
    icon: 'Tools',
    color: '#409eff'
  },
  {
    name: '已解决',
    value: '156',
    unit: '条',
    trend: '↑ 5.2%',
    trendColor: '#67c23a',
    icon: 'CircleCheck',
    color: '#67c23a'
  }
])

// 异常记录数据
const exceptionRecords = ref([
  {
    id: 'EX2024011501',
    timestamp: '2024-01-15 14:30:25',
    deviceName: '空调主机-01',
    location: '办公大楼A-3F',
    exceptionType: 'overconsumption',
    severity: 'warning',
    description: '能耗超出正常范围30%，当前功率：15.2kW，正常范围：8-12kW',
    exceptionValue: '15.2kW (超限30%)',
    status: 'pending',
    handler: '',
    processTime: '',
    resolveTime: '',
    historicalData: true,
    processingLog: []
  },
  {
    id: 'EX2024011502',
    timestamp: '2024-01-15 13:45:12',
    deviceName: '照明控制器-02',
    location: '办公大楼B-2F',
    exceptionType: 'device-fault',
    severity: 'critical',
    description: '设备通信故障，连续5分钟无响应',
    exceptionValue: '通信中断 5分钟',
    status: 'processing',
    handler: '张工程师',
    processTime: '2024-01-15 14:00:00',
    resolveTime: '',
    historicalData: true,
    processingLog: [
      {
        timestamp: '2024-01-15 14:00:00',
        operator: '张工程师',
        action: '开始处理异常',
        type: 'primary',
        remark: '已派遣技术人员前往现场检查'
      }
    ]
  },
  {
    id: 'EX2024011503',
    timestamp: '2024-01-15 12:20:08',
    deviceName: '电表-A101',
    location: '生产车间-1F',
    exceptionType: 'data-abnormal',
    severity: 'info',
    description: '数据波动异常，电流值出现负数',
    exceptionValue: '电流: -2.5A',
    status: 'resolved',
    handler: '李技术员',
    processTime: '2024-01-15 12:30:00',
    resolveTime: '2024-01-15 13:15:00',
    historicalData: true,
    processingLog: [
      {
        timestamp: '2024-01-15 12:30:00',
        operator: '李技术员',
        action: '开始处理异常',
        type: 'primary',
        remark: '检查传感器连接状态'
      },
      {
        timestamp: '2024-01-15 13:15:00',
        operator: '李技术员',
        action: '异常已解决',
        type: 'success',
        remark: '传感器接线松动，已重新固定'
      }
    ]
  },
  {
    id: 'EX2024011504',
    timestamp: '2024-01-15 11:15:33',
    deviceName: '温度传感器-05',
    location: '配套设施-1F',
    exceptionType: 'environment-abnormal',
    severity: 'warning',
    description: '环境温度超过安全阈值',
    exceptionValue: '温度: 45°C (阈值: 40°C)',
    status: 'pending',
    handler: '',
    processTime: '',
    resolveTime: '',
    historicalData: true,
    processingLog: []
  },
  {
    id: 'EX2024011505',
    timestamp: '2024-01-15 10:30:15',
    deviceName: '变压器-主01',
    location: '办公大楼A-B1F',
    exceptionType: 'communication-lost',
    severity: 'critical',
    description: '变压器通信中断，无法获取运行数据',
    exceptionValue: '通信中断 15分钟',
    status: 'processing',
    handler: '王主管',
    processTime: '2024-01-15 10:45:00',
    resolveTime: '',
    historicalData: true,
    processingLog: [
      {
        timestamp: '2024-01-15 10:45:00',
        operator: '王主管',
        action: '开始处理异常',
        type: 'primary',
        remark: '联系设备厂商技术支持'
      }
    ]
  }
])

// 过滤后的记录
const filteredRecords = computed(() => {
  let records = exceptionRecords.value
  
  // 建筑筛选
  if (selectedBuilding.value) {
    records = records.filter(record => 
      record.location.includes(getBuildingName(selectedBuilding.value))
    )
  }
  
  // 异常类型筛选
  if (filters.value.exceptionType) {
    records = records.filter(record => record.exceptionType === filters.value.exceptionType)
  }
  
  // 异常级别筛选
  if (filters.value.severity) {
    records = records.filter(record => record.severity === filters.value.severity)
  }
  
  // 处理状态筛选
  if (filters.value.status) {
    records = records.filter(record => record.status === filters.value.status)
  }
  
  // 关键字搜索
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    records = records.filter(record => 
      record.deviceName.toLowerCase().includes(keyword) ||
      record.description.toLowerCase().includes(keyword) ||
      record.location.toLowerCase().includes(keyword)
    )
  }
  
  // 时间范围筛选
  if (filters.value.dateRange && filters.value.dateRange.length === 2) {
    const [startDate, endDate] = filters.value.dateRange
    records = records.filter(record => {
      const recordDate = new Date(record.timestamp)
      return recordDate >= startDate && recordDate <= endDate
    })
  }
  
  return records
})

// 分页后的记录
const paginatedRecords = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredRecords.value.slice(start, end)
})

// 辅助函数
const getBuildingName = (value: string) => {
  const names: Record<string, string> = {
    'building-a': '办公大楼A',
    'building-b': '办公大楼B',
    'workshop': '生产车间',
    'facilities': '配套设施'
  }
  return names[value] || ''
}

const getExceptionTypeName = (type: string) => {
  const names: Record<string, string> = {
    'overconsumption': '能耗超限',
    'device-fault': '设备故障',
    'data-abnormal': '数据异常',
    'communication-lost': '通信中断',
    'environment-abnormal': '环境异常'
  }
  return names[type] || type
}

const getExceptionTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'overconsumption': 'warning',
    'device-fault': 'danger',
    'data-abnormal': 'info',
    'communication-lost': 'danger',
    'environment-abnormal': 'warning'
  }
  return colors[type] || 'info'
}

const getSeverityName = (severity: string) => {
  const names: Record<string, string> = {
    'critical': '紧急',
    'warning': '警告',
    'info': '提醒'
  }
  return names[severity] || severity
}

const getSeverityColor = (severity: string) => {
  const colors: Record<string, string> = {
    'critical': 'danger',
    'warning': 'warning',
    'info': 'info'
  }
  return colors[severity] || 'info'
}

const getStatusName = (status: string) => {
  const names: Record<string, string> = {
    'pending': '未处理',
    'processing': '处理中',
    'resolved': '已解决',
    'ignored': '已忽略'
  }
  return names[status] || status
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'pending': 'danger',
    'processing': 'warning',
    'resolved': 'success',
    'ignored': 'info'
  }
  return colors[status] || 'info'
}

// 事件处理函数
const handleBuildingChange = () => {
  refreshData()
}

const handleFilterChange = () => {
  currentPage.value = 1
}

const handleKeywordSearch = () => {
  currentPage.value = 1
}

const handleDateRangeChange = () => {
  currentPage.value = 1
}

const handleChartTimeRangeChange = () => {
  nextTick(() => {
    initExceptionTrendChart()
  })
}

const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const clearFilters = () => {
  filters.value = {
    exceptionType: '',
    severity: '',
    status: '',
    keyword: '',
    dateRange: null,
    deviceTypes: ['lighting', 'hvac', 'power', 'monitoring']
  }
  currentPage.value = 1
}

const refreshData = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    nextTick(() => {
      initExceptionTrendChart()
      initExceptionTypeChart()
    })
    
    ElMessage.success('数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const exportRecords = () => {
  try {
    const exportData = filteredRecords.value.map(item => ({
      异常ID: item.id,
      发生时间: item.timestamp,
      设备名称: item.deviceName,
      位置: item.location,
      异常类型: getExceptionTypeName(item.exceptionType),
      级别: getSeverityName(item.severity),
      异常描述: item.description,
      处理状态: getStatusName(item.status),
      处理人: item.handler || '未分配'
    }))
    
    const csvContent = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).join(','))
    ].join('\n')
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `异常记录_${new Date().toISOString().slice(0, 10)}.csv`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('记录导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const viewDetail = (row: any) => {
  selectedRecord.value = row
  showDetailDialog.value = true
  nextTick(() => {
    if (row.historicalData) {
      initDetailChart()
    }
  })
}

const handleCloseDetail = () => {
  showDetailDialog.value = false
}

const exportDetailData = () => {
  if (!selectedRecord.value) return
  
  const data = {
    基本信息: {
      异常ID: selectedRecord.value.id,
      发生时间: selectedRecord.value.timestamp,
      设备名称: selectedRecord.value.deviceName,
      设备位置: selectedRecord.value.location
    },
    异常详情: {
      异常类型: getExceptionTypeName(selectedRecord.value.exceptionType),
      异常级别: getSeverityName(selectedRecord.value.severity),
      异常描述: selectedRecord.value.description,
      异常数值: selectedRecord.value.exceptionValue
    },
    处理信息: {
      处理状态: getStatusName(selectedRecord.value.status),
      处理人: selectedRecord.value.handler || '未分配',
      处理时间: selectedRecord.value.processTime || '未处理',
      解决时间: selectedRecord.value.resolveTime || '未解决'
    }
  }
  
  const jsonData = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.download = `异常详情_${selectedRecord.value.id}.json`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('详情导出成功！')
}

const processException = (row: any) => {
  selectedRecord.value = row
  processForm.value = {
    method: 'manual',
    remark: '',
    expectedTime: null
  }
  showProcessDialog.value = true
}

const confirmProcess = () => {
  if (!selectedRecord.value) return
  
  // 更新记录状态
  const record = exceptionRecords.value.find(r => r.id === selectedRecord.value.id)
  if (record) {
    record.status = processForm.value.method === 'ignore' ? 'ignored' : 'processing'
    record.handler = '当前用户'
    record.processTime = new Date().toLocaleString()
    
    // 添加处理记录
    record.processingLog.push({
      timestamp: new Date().toLocaleString(),
      operator: '当前用户',
      action: processForm.value.method === 'ignore' ? '忽略异常' : '开始处理异常',
      type: processForm.value.method === 'ignore' ? 'info' : 'primary',
      remark: processForm.value.remark
    })
  }
  
  showProcessDialog.value = false
  ElMessage.success('异常处理状态更新成功！')
}

const resolveException = (row: any) => {
  ElMessageBox.prompt('请输入解决方案说明', '解决异常', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请描述解决方案和处理结果'
  }).then(({ value }) => {
    // 更新记录状态
    const record = exceptionRecords.value.find(r => r.id === row.id)
    if (record) {
      record.status = 'resolved'
      record.resolveTime = new Date().toLocaleString()
      
      // 添加解决记录
      record.processingLog.push({
        timestamp: new Date().toLocaleString(),
        operator: '当前用户',
        action: '异常已解决',
        type: 'success',
        remark: value
      })
    }
    
    ElMessage.success('异常已标记为解决！')
  }).catch(() => {
    // 取消操作
  })
}

const batchProcess = () => {
  batchForm.value = {
    action: 'process',
    remark: ''
  }
  showBatchDialog.value = true
}

const confirmBatchProcess = () => {
  selectedRecords.value.forEach(row => {
    const record = exceptionRecords.value.find(r => r.id === row.id)
    if (record) {
      switch (batchForm.value.action) {
        case 'process':
          record.status = 'processing'
          record.handler = '当前用户'
          record.processTime = new Date().toLocaleString()
          break
        case 'resolve':
          record.status = 'resolved'
          record.handler = record.handler || '当前用户'
          record.processTime = record.processTime || new Date().toLocaleString()
          record.resolveTime = new Date().toLocaleString()
          break
        case 'ignore':
          record.status = 'ignored'
          record.handler = '当前用户'
          record.processTime = new Date().toLocaleString()
          break
      }
      
      // 添加批量处理记录
      record.processingLog.push({
        timestamp: new Date().toLocaleString(),
        operator: '当前用户',
        action: `批量${batchForm.value.action === 'process' ? '处理' : batchForm.value.action === 'resolve' ? '解决' : '忽略'}`,
        type: batchForm.value.action === 'resolve' ? 'success' : 'primary',
        remark: batchForm.value.remark
      })
    }
  })
  
  showBatchDialog.value = false
  selectedRecords.value = []
  ElMessage.success(`批量处理完成！共处理 ${selectedRecords.value.length} 条记录`)
}

// 初始化异常趋势图表
const initExceptionTrendChart = () => {
  if (!exceptionTrendChart.value) return
  
  const chart = echarts.init(exceptionTrendChart.value)
  
  const days = []
  const criticalData = []
  const warningData = []
  const infoData = []
  
  // 生成示例数据
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.getMonth() + 1 + '/' + date.getDate())
    
    criticalData.push(Math.floor(Math.random() * 5) + 1)
    warningData.push(Math.floor(Math.random() * 8) + 3)
    infoData.push(Math.floor(Math.random() * 10) + 5)
  }
  
  const option = {
    title: {
      text: '异常趋势统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['紧急', '警告', '提醒'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: days
    },
    yAxis: {
      type: 'value',
      name: '异常数量',
      axisLabel: { formatter: '{value}条' }
    },
    series: [
      {
        name: '紧急',
        type: 'line',
        smooth: true,
        data: criticalData,
        itemStyle: { color: '#f56c6c' },
        areaStyle: { color: 'rgba(245, 108, 108, 0.1)' }
      },
      {
        name: '警告',
        type: 'line',
        smooth: true,
        data: warningData,
        itemStyle: { color: '#e6a23c' },
        areaStyle: { color: 'rgba(230, 162, 60, 0.1)' }
      },
      {
        name: '提醒',
        type: 'line',
        smooth: true,
        data: infoData,
        itemStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化异常类型分布图表
const initExceptionTypeChart = () => {
  if (!exceptionTypeChart.value) return
  
  const chart = echarts.init(exceptionTypeChart.value)
  
  const option = {
    title: {
      text: '异常类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 60
    },
    series: [
      {
        name: '异常类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45, name: '能耗超限', itemStyle: { color: '#e6a23c' } },
          { value: 32, name: '设备故障', itemStyle: { color: '#f56c6c' } },
          { value: 28, name: '数据异常', itemStyle: { color: '#409eff' } },
          { value: 18, name: '通信中断', itemStyle: { color: '#f56c6c' } },
          { value: 12, name: '环境异常', itemStyle: { color: '#e6a23c' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化详情图表
const initDetailChart = () => {
  if (!detailChart.value) return
  
  const chart = echarts.init(detailChart.value)
  
  const option = {
    title: {
      text: '历史数据趋势',
      left: 'center'
    },
    tooltip: { trigger: 'axis' },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00']
    },
    yAxis: { type: 'value', name: '功率(kW)' },
    series: [
      {
        name: '实际功率',
        type: 'line',
        smooth: true,
        data: [10.2, 11.5, 12.8, 13.5, 14.2, 15.2, 14.8],
        itemStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' },
        markLine: {
          data: [
            { yAxis: 12, name: '正常上限', lineStyle: { color: '#67c23a', type: 'dashed' } }
          ]
        }
      }
    ]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    refreshData()
  })
})
</script>

<style scoped>
.exception-records {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.exception-stats {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 30px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-trend {
  font-size: 11px;
  font-weight: 500;
}

.stat-divider {
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: #ebeef5;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.record-count {
  font-size: 14px;
  color: #909399;
  margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .exception-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .stat-item {
    min-width: auto;
    padding: 0;
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .stat-divider {
    display: none;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .filter-item {
    margin-bottom: 15px;
  }
}
</style> 