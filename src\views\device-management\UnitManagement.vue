<template>
  <div class="unit-management" v-loading="loading" element-loading-text="正在加载单元数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 单元管理操作 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>单元管理</h3>
          <div class="header-controls">
            <el-select v-model="selectedUnitType" size="small" placeholder="单元类型" style="width: 120px;" @change="handleUnitTypeChange">
              <el-option label="全部类型" value="" />
              <el-option label="建筑" value="building" />
              <el-option label="楼层" value="floor" />
              <el-option label="区域" value="area" />
              <el-option label="房间" value="room" />
            </el-select>
            <el-button type="primary" size="small" @click="addUnit">
              <el-icon><Plus /></el-icon>
              新建单元
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="info" size="small" @click="toggleView">
              <el-icon><Switch /></el-icon>
              {{ viewMode === 'tree' ? '列表视图' : '树形视图' }}
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <label>单元状态：</label>
            <el-select v-model="filters.status" placeholder="请选择单元状态" size="small" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="异常" value="abnormal" />
              <el-option label="维护中" value="maintenance" />
              <el-option label="停用" value="disabled" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>能源类型：</label>
            <el-select v-model="filters.energyType" placeholder="请选择能源类型" size="small" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="电力" value="electricity" />
              <el-option label="水" value="water" />
              <el-option label="燃气" value="gas" />
              <el-option label="热力" value="heat" />
              <el-option label="冷气" value="cooling" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>创建时间：</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>关键字搜索：</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="单元名称、编码" 
              size="small" 
              clearable
              @input="handleKeywordSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 单元统计概览 -->
    <el-card class="overview-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>单元统计概览</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="exportUnits">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="warning" size="small" @click="importUnits">
              <el-icon><Upload /></el-icon>
              导入数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="stats-overview">
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(64, 158, 255, 0.2); border: 2px solid #409eff;">
            <el-icon style="color: #409eff; font-size: 28px;"><OfficeBuilding /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">总单元数</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ unitStats.total }}</span>
              <span class="stats-unit">个</span>
            </div>
            <div class="stats-trend trend-up">较上月 +5</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(103, 194, 58, 0.2); border: 2px solid #67c23a;">
            <el-icon style="color: #67c23a; font-size: 28px;"><CircleCheckFilled /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">正常单元</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ unitStats.normal }}</span>
              <span class="stats-unit">个</span>
            </div>
            <div class="stats-trend trend-down">较上月 -2</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(230, 162, 60, 0.2); border: 2px solid #e6a23c;">
            <el-icon style="color: #e6a23c; font-size: 28px;"><Box /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">关联设备</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ unitStats.devices }}</span>
              <span class="stats-unit">台</span>
            </div>
            <div class="stats-trend trend-up">较上月 +12</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(245, 108, 108, 0.2); border: 2px solid #f56c6c;">
            <el-icon style="color: #f56c6c; font-size: 28px;"><Lightning /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">能耗监测点</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ unitStats.monitors }}</span>
              <span class="stats-unit">个</span>
            </div>
            <div class="stats-trend trend-up">较上月 +8</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 数据可视化 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>单元类型分布</h4>
          </template>
          <div ref="unitTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>能耗分布统计</h4>
          </template>
          <div ref="energyDistributionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 左侧单元树形结构 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h4>单元层级结构</h4>
              <div class="header-controls">
                <el-dropdown @command="handleExpandCommand">
                  <el-button type="primary" size="small">
                    <el-icon><Plus /></el-icon>
                    展开操作
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="expandAll">
                        <el-icon><Plus /></el-icon>
                        展开全部
                      </el-dropdown-item>
                      <el-dropdown-item command="expandLevel1">
                        <el-icon><OfficeBuilding /></el-icon>
                        展开到建筑级
                      </el-dropdown-item>
                      <el-dropdown-item command="expandLevel2">
                        <el-icon><List /></el-icon>
                        展开到楼层级
                      </el-dropdown-item>
                      <el-dropdown-item command="expandAbnormal">
                        <el-icon><WarningFilled /></el-icon>
                        展开异常单元
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button type="info" size="small" @click="collapseAll" style="margin-left: 8px;">
                  <el-icon><Minus /></el-icon>
                  收起全部
                </el-button>
                <el-button type="success" size="small" @click="refreshTreeData" style="margin-left: 8px;">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <el-tree
            ref="unitTreeRef"
            :data="unitTreeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            show-checkbox
            @node-click="handleNodeClick"
            @check-change="handleCheckChange"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon class="node-icon" :style="{ color: getUnitTypeColor(data.type) }">
                  <component :is="getUnitTypeIcon(data.type)" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
                <el-tag v-if="data.deviceCount > 0" size="small" type="info">
                  {{ data.deviceCount }}台设备
                </el-tag>
                <el-tag v-if="data.status === 'abnormal'" size="small" type="danger">异常</el-tag>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      
      <!-- 右侧详情和列表 -->
      <el-col :span="16">
        <el-card>
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="单元列表" name="list">
              <template #label>
                <span><el-icon><List /></el-icon> 单元列表</span>
              </template>
              
              <div class="tab-header">
                <div class="tab-title">单元信息列表</div>
                <div class="tab-controls">
                  <el-button type="danger" size="small" @click="batchDeleteUnits" :disabled="selectedUnits.length === 0">
                    <el-icon><Delete /></el-icon>
                    批量删除 ({{ selectedUnits.length }})
                  </el-button>
                </div>
              </div>
              
              <el-table 
                :data="unitList" 
                border 
                @selection-change="handleUnitSelectionChange"
                style="width: 100%"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column prop="unitCode" label="单元编码" width="120" align="center" />
                <el-table-column prop="unitName" label="单元名称" min-width="150" align="center" />
                <el-table-column label="单元类型" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getUnitTypeTagType(row.type)">
                      {{ getUnitTypeName(row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="parentUnit" label="上级单元" min-width="120" align="center" />
                <el-table-column prop="area" label="面积(㎡)" width="100" align="center" />
                <el-table-column prop="deviceCount" label="设备数量" width="100" align="center" />
                <el-table-column label="单元状态" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">
                      {{ getStatusName(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="manager" label="负责人" width="100" align="center" />
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template #default="{ row }">
                    <div style="white-space: nowrap;">
                      <el-button type="primary" size="small" @click="editUnit(row)" style="margin-right: 5px;">
                        编辑
                      </el-button>
                      <el-button type="info" size="small" @click="viewUnit(row)" style="margin-right: 5px;">
                        查看
                      </el-button>
                      <el-button type="danger" size="small" @click="deleteUnit(row)">
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="设备关联" name="devices">
              <template #label>
                <span><el-icon><Connection /></el-icon> 设备关联</span>
              </template>
              
              <div class="tab-header">
                <div class="tab-title">设备关联管理</div>
                <div class="tab-controls">
                  <el-button type="primary" size="small" @click="addDeviceAssociation">
                    <el-icon><Plus /></el-icon>
                    关联设备
                  </el-button>
                </div>
              </div>
              
              <el-table 
                :data="deviceAssociations" 
                border 
                style="width: 100%"
              >
                <el-table-column prop="unitName" label="单元名称" min-width="150" align="center" />
                <el-table-column prop="deviceId" label="设备编号" width="120" align="center" />
                <el-table-column prop="deviceName" label="设备名称" min-width="150" align="center" />
                <el-table-column prop="deviceType" label="设备类型" width="120" align="center" />
                <el-table-column prop="associationType" label="关联类型" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getAssociationTypeTagType(row.associationType)">
                      {{ getAssociationTypeName(row.associationType) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="associationDate" label="关联时间" width="150" align="center" />
                <el-table-column label="操作" width="150" align="center" fixed="right">
                  <template #default="{ row }">
                    <div style="white-space: nowrap;">
                      <el-button type="warning" size="small" @click="editAssociation(row)" style="margin-right: 5px;">
                        编辑
                      </el-button>
                      <el-button type="danger" size="small" @click="removeAssociation(row)">
                        移除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            
            <el-tab-pane label="单元详情" name="details">
              <template #label>
                <span><el-icon><InfoFilled /></el-icon> 单元详情</span>
              </template>
              
              <div v-if="selectedUnitDetail">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="单元编码">{{ selectedUnitDetail.unitCode }}</el-descriptions-item>
                  <el-descriptions-item label="单元名称">{{ selectedUnitDetail.unitName }}</el-descriptions-item>
                  <el-descriptions-item label="单元类型">{{ getUnitTypeName(selectedUnitDetail.type) }}</el-descriptions-item>
                  <el-descriptions-item label="上级单元">{{ selectedUnitDetail.parentUnit || '无' }}</el-descriptions-item>
                  <el-descriptions-item label="建筑面积">{{ selectedUnitDetail.area }} ㎡</el-descriptions-item>
                  <el-descriptions-item label="使用面积">{{ selectedUnitDetail.usableArea }} ㎡</el-descriptions-item>
                  <el-descriptions-item label="单元状态">
                    <el-tag :type="getStatusTagType(selectedUnitDetail.status)">
                      {{ getStatusName(selectedUnitDetail.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="负责人">{{ selectedUnitDetail.manager }}</el-descriptions-item>
                  <el-descriptions-item label="联系电话">{{ selectedUnitDetail.phone }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ selectedUnitDetail.createTime }}</el-descriptions-item>
                  <el-descriptions-item label="更新时间">{{ selectedUnitDetail.updateTime }}</el-descriptions-item>
                  <el-descriptions-item label="备注信息" :span="2">{{ selectedUnitDetail.remarks || '无' }}</el-descriptions-item>
                </el-descriptions>
                
                <div style="margin-top: 20px;">
                  <h4>关联设备</h4>
                  <el-row :gutter="20">
                    <el-col :span="6" v-for="device in selectedUnitDetail.devices" :key="device.id">
                      <el-card shadow="hover" style="margin-bottom: 10px;">
                        <div class="device-card">
                          <el-icon class="device-icon" :style="{ color: getDeviceTypeColor(device.type) }">
                            <component :is="getDeviceTypeIcon(device.type)" />
                          </el-icon>
                          <div class="device-info">
                            <div class="device-name">{{ device.name }}</div>
                            <div class="device-type">{{ device.type }}</div>
                            <el-tag size="small" :type="device.status === 'normal' ? 'success' : 'danger'">
                              {{ device.status === 'normal' ? '正常' : '异常' }}
                            </el-tag>
                          </div>
                        </div>
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
                
                <div style="margin-top: 20px;">
                  <h4>能耗统计</h4>
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <el-statistic title="本月用电" :value="selectedUnitDetail.monthlyElectricity || 0" suffix="kWh" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="本月用水" :value="selectedUnitDetail.monthlyWater || 0" suffix="m³" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="本月燃气" :value="selectedUnitDetail.monthlyGas || 0" suffix="m³" />
                    </el-col>
                    <el-col :span="6">
                      <el-statistic title="能耗成本" :value="selectedUnitDetail.monthlyCost || 0" suffix="元" />
                    </el-col>
                  </el-row>
                </div>
              </div>
              
              <el-empty v-else description="请在左侧选择单元查看详情" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 分页 -->
    <el-pagination
      v-if="activeTab === 'list'"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="totalUnits"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px; justify-content: center;"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 单元对话框 -->
    <el-dialog
      v-model="showUnitDialog"
      :title="isEditUnit ? '编辑单元' : '新建单元'"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="unitForm" :rules="unitRules" ref="unitFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单元编码" prop="unitCode">
              <el-input v-model="unitForm.unitCode" placeholder="请输入单元编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单元名称" prop="unitName">
              <el-input v-model="unitForm.unitName" placeholder="请输入单元名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单元类型" prop="type">
              <el-select v-model="unitForm.type" placeholder="请选择单元类型" style="width: 100%">
                <el-option label="建筑" value="building" />
                <el-option label="楼层" value="floor" />
                <el-option label="区域" value="area" />
                <el-option label="房间" value="room" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级单元" prop="parentId">
              <el-cascader
                v-model="unitForm.parentId"
                :options="unitTreeOptions"
                :props="cascaderProps"
                placeholder="请选择上级单元"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建筑面积" prop="area">
              <el-input-number v-model="unitForm.area" :min="0" :precision="2" style="width: 100%" />
              <span style="margin-left: 8px;">㎡</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用面积" prop="usableArea">
              <el-input-number v-model="unitForm.usableArea" :min="0" :precision="2" style="width: 100%" />
              <span style="margin-left: 8px;">㎡</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input v-model="unitForm.manager" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="unitForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注信息">
          <el-input 
            v-model="unitForm.remarks" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showUnitDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUnit">保存</el-button>
      </template>
    </el-dialog>

    <!-- 设备关联对话框 -->
    <el-dialog
      v-model="showAssociationDialog"
      title="设备关联"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="associationForm" :rules="associationRules" ref="associationFormRef" label-width="100px">
        <el-form-item label="选择单元" prop="unitId">
          <el-cascader
            v-model="associationForm.unitId"
            :options="unitTreeOptions"
            :props="cascaderProps"
            placeholder="请选择单元"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="选择设备" prop="deviceId">
          <el-select v-model="associationForm.deviceId" placeholder="请选择设备" style="width: 100%">
            <el-option label="主配电柜电表 (DEV001)" value="DEV001" />
            <el-option label="空调控制器 (DEV002)" value="DEV002" />
            <el-option label="温湿度传感器 (DEV003)" value="DEV003" />
            <el-option label="光伏逆变器 (DEV004)" value="DEV004" />
            <el-option label="储能电池管理系统 (DEV005)" value="DEV005" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关联类型" prop="associationType">
          <el-select v-model="associationForm.associationType" placeholder="请选择关联类型" style="width: 100%">
            <el-option label="计量设备" value="meter" />
            <el-option label="控制设备" value="controller" />
            <el-option label="监测设备" value="monitor" />
            <el-option label="安全设备" value="safety" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关联说明">
          <el-input 
            v-model="associationForm.description" 
            type="textarea" 
            :rows="2" 
            placeholder="请输入关联说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAssociationDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAssociation">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import {
  Plus,
  Refresh,
  Switch,
  Search,
  Download,
  Upload,
  Delete,
  List,
  Connection,
  InfoFilled,
  Minus,
  OfficeBuilding,
  CircleCheckFilled,
  Box,
  Lightning,
  ArrowDown,
  WarningFilled,
  Grid,
  House,
  Monitor,
  Setting,
  Cpu
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '设备资产管理', to: '/device-management' },
  { text: '单元管理' }
])

// 响应式数据
const loading = ref(false)
const selectedUnitType = ref('')
const viewMode = ref('tree')
const activeTab = ref('list')
const unitTreeRef = ref()
const unitTypeChart = ref()
const energyDistributionChart = ref()

// 筛选条件
const filters = reactive({
  status: '',
  energyType: '',
  dateRange: [],
  keyword: ''
})

// 单元统计数据
const unitStats = reactive({
  total: 68,
  normal: 62,
  devices: 142,
  monitors: 89
})

// 树形数据属性
const treeProps = {
  children: 'children',
  label: 'name'
}

// 级联选择器属性
const cascaderProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true
}

// 单元树形数据
const unitTreeData = ref([
  {
    id: 'building-1',
    name: '办公大楼A',
    type: 'building',
    deviceCount: 45,
    status: 'normal',
    children: [
      {
        id: 'floor-1-1',
        name: '1楼',
        type: 'floor',
        deviceCount: 12,
        status: 'normal',
        children: [
          {
            id: 'area-1-1-1',
            name: '大厅',
            type: 'area',
            deviceCount: 5,
            status: 'normal'
          },
          {
            id: 'area-1-1-2',
            name: '会议室',
            type: 'area',
            deviceCount: 7,
            status: 'abnormal'
          }
        ]
      },
      {
        id: 'floor-1-2',
        name: '2楼',
        type: 'floor',
        deviceCount: 18,
        status: 'normal',
        children: [
          {
            id: 'room-1-2-1',
            name: '会议室201',
            type: 'room',
            deviceCount: 3,
            status: 'normal'
          },
          {
            id: 'room-1-2-2',
            name: '办公区202',
            type: 'room',
            deviceCount: 8,
            status: 'normal'
          }
        ]
      }
    ]
  },
  {
    id: 'building-2',
    name: '生产车间',
    type: 'building',
    deviceCount: 23,
    status: 'normal',
    children: [
      {
        id: 'area-2-1',
        name: '生产区A',
        type: 'area',
        deviceCount: 15,
        status: 'normal'
      },
      {
        id: 'area-2-2',
        name: '生产区B',
        type: 'area',
        deviceCount: 8,
        status: 'normal'
      }
    ]
  }
])

// 单元树形选项（用于级联选择器）
const unitTreeOptions = ref([
  {
    id: 'building-1',
    name: '办公大楼A',
    children: [
      {
        id: 'floor-1-1',
        name: '1楼',
        children: [
          { id: 'area-1-1-1', name: '大厅' },
          { id: 'area-1-1-2', name: '会议室' }
        ]
      },
      {
        id: 'floor-1-2',
        name: '2楼',
        children: [
          { id: 'room-1-2-1', name: '会议室201' },
          { id: 'room-1-2-2', name: '办公区202' }
        ]
      }
    ]
  },
  {
    id: 'building-2',
    name: '生产车间',
    children: [
      { id: 'area-2-1', name: '生产区A' },
      { id: 'area-2-2', name: '生产区B' }
    ]
  }
])

// 单元列表数据
const unitList = ref([
  {
    unitCode: 'UNIT001',
    unitName: '办公大楼A',
    type: 'building',
    parentUnit: '无',
    area: 5000.00,
    deviceCount: 45,
    status: 'normal',
    manager: '张经理'
  },
  {
    unitCode: 'UNIT002',
    unitName: '1楼大厅',
    type: 'area',
    parentUnit: '办公大楼A',
    area: 200.00,
    deviceCount: 5,
    status: 'normal',
    manager: '李主管'
  },
  {
    unitCode: 'UNIT003',
    unitName: '会议室201',
    type: 'room',
    parentUnit: '办公大楼A-2楼',
    area: 50.00,
    deviceCount: 3,
    status: 'abnormal',
    manager: '王负责人'
  },
  {
    unitCode: 'UNIT004',
    unitName: '生产车间',
    type: 'building',
    parentUnit: '无',
    area: 3000.00,
    deviceCount: 23,
    status: 'normal',
    manager: '刘厂长'
  },
  {
    unitCode: 'UNIT005',
    unitName: '生产区A',
    type: 'area',
    parentUnit: '生产车间',
    area: 1500.00,
    deviceCount: 15,
    status: 'normal',
    manager: '陈主任'
  }
])

// 设备关联数据
const deviceAssociations = ref([
  {
    unitName: '办公大楼A-1楼大厅',
    deviceId: 'DEV001',
    deviceName: '主配电柜电表',
    deviceType: '计量设备',
    associationType: 'meter',
    associationDate: '2024-01-10 09:30:00'
  },
  {
    unitName: '会议室201',
    deviceId: 'DEV002',
    deviceName: '空调控制器',
    deviceType: '控制设备',
    associationType: 'controller',
    associationDate: '2024-01-08 14:20:00'
  },
  {
    unitName: '会议室201',
    deviceId: 'DEV003',
    deviceName: '温湿度传感器',
    deviceType: '传感器',
    associationType: 'monitor',
    associationDate: '2024-01-05 11:15:00'
  }
])

// 选中的单元详情
const selectedUnitDetail = ref({
  unitCode: 'UNIT003',
  unitName: '会议室201',
  type: 'room',
  parentUnit: '办公大楼A-2楼',
  area: 50.00,
  usableArea: 45.00,
  status: 'abnormal',
  manager: '王负责人',
  phone: '***********',
  createTime: '2024-01-01 09:00:00',
  updateTime: '2024-01-15 14:30:00',
  remarks: '主要用于小型会议和培训',
  devices: [
    { id: 'DEV002', name: '空调控制器', type: '控制设备', status: 'normal' },
    { id: 'DEV003', name: '温湿度传感器', type: '传感器', status: 'abnormal' },
    { id: 'DEV006', name: '照明控制器', type: '控制设备', status: 'normal' }
  ],
  monthlyElectricity: 1250.5,
  monthlyWater: 12.8,
  monthlyGas: 0,
  monthlyCost: 865.3
})

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalUnits = ref(68)

// 选中数据
const selectedUnits = ref<any[]>([])

// 对话框控制
const showUnitDialog = ref(false)
const showAssociationDialog = ref(false)
const isEditUnit = ref(false)

// 表单数据
const unitForm = reactive({
  unitCode: '',
  unitName: '',
  type: '',
  parentId: [],
  area: 0,
  usableArea: 0,
  manager: '',
  phone: '',
  remarks: ''
})

const associationForm = reactive({
  unitId: [],
  deviceId: '',
  associationType: '',
  description: ''
})

// 表单验证规则
const unitRules = {
  unitCode: [{ required: true, message: '请输入单元编码', trigger: 'blur' }],
  unitName: [{ required: true, message: '请输入单元名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择单元类型', trigger: 'change' }],
  area: [{ required: true, message: '请输入建筑面积', trigger: 'change' }],
  manager: [{ required: true, message: '请输入负责人', trigger: 'blur' }]
}

const associationRules = {
  unitId: [{ required: true, message: '请选择单元', trigger: 'change' }],
  deviceId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  associationType: [{ required: true, message: '请选择关联类型', trigger: 'change' }]
}

const unitFormRef = ref()
const associationFormRef = ref()

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 单元类型分布图
    if (unitTypeChart.value) {
      const typeChart = echarts.init(unitTypeChart.value)
      const typeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '0%'
        },
        series: [
          {
            name: '单元类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 8, name: '建筑' },
              { value: 15, name: '楼层' },
              { value: 25, name: '区域' },
              { value: 20, name: '房间' }
            ]
          }
        ]
      }
      typeChart.setOption(typeOption)
    }

    // 能耗分布统计图
    if (energyDistributionChart.value) {
      const energyChart = echarts.init(energyDistributionChart.value)
      const energyOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['电力', '水', '燃气', '热力']
        },
        xAxis: {
          type: 'category',
          data: ['办公大楼A', '办公大楼B', '生产车间', '配套设施']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '电力',
            type: 'bar',
            data: [120, 132, 101, 134]
          },
          {
            name: '水',
            type: 'bar',
            data: [220, 182, 191, 234]
          },
          {
            name: '燃气',
            type: 'bar',
            data: [150, 232, 201, 154]
          },
          {
            name: '热力',
            type: 'bar',
            data: [98, 77, 101, 99]
          }
        ]
      }
      energyChart.setOption(energyOption)
    }
  })
}

// 获取单元类型图标
const getUnitTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    building: 'OfficeBuilding',
    floor: 'Box',
    area: 'Grid',
    room: 'House'
  }
  return iconMap[type] || 'Box'
}

// 获取单元类型颜色
const getUnitTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    building: '#409eff',
    floor: '#67c23a',
    area: '#e6a23c',
    room: '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

// 获取设备类型图标
const getDeviceTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    '计量设备': 'Monitor',
    '控制设备': 'Setting',
    '传感器': 'Cpu',
    '执行器': 'Lightning'
  }
  return iconMap[type] || 'Box'
}

// 获取设备类型颜色
const getDeviceTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '计量设备': '#409eff',
    '控制设备': '#67c23a',
    '传感器': '#e6a23c',
    '执行器': '#f56c6c'
  }
  return colorMap[type] || '#909399'
}

// 获取单元类型标签类型
const getUnitTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    building: 'primary',
    floor: 'success',
    area: 'warning',
    room: 'danger'
  }
  return typeMap[type] || ''
}

// 获取单元类型名称
const getUnitTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    building: '建筑',
    floor: '楼层',
    area: '区域',
    room: '房间'
  }
  return nameMap[type] || type
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: 'success',
    abnormal: 'danger',
    maintenance: 'warning',
    disabled: 'info'
  }
  return statusMap[status] || ''
}

// 获取状态名称
const getStatusName = (status: string) => {
  const nameMap: Record<string, string> = {
    normal: '正常',
    abnormal: '异常',
    maintenance: '维护中',
    disabled: '停用'
  }
  return nameMap[status] || status
}

// 获取关联类型标签类型
const getAssociationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    meter: 'primary',
    controller: 'success',
    monitor: 'warning',
    safety: 'danger'
  }
  return typeMap[type] || ''
}

// 获取关联类型名称
const getAssociationTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    meter: '计量设备',
    controller: '控制设备',
    monitor: '监测设备',
    safety: '安全设备'
  }
  return nameMap[type] || type
}

// 事件处理函数
const handleUnitTypeChange = () => {
  console.log('单元类型筛选变更:', selectedUnitType.value)
}

const handleFilterChange = () => {
  console.log('筛选条件变更:', filters)
}

const handleDateRangeChange = () => {
  console.log('时间范围变更:', filters.dateRange)
}

const handleKeywordSearch = () => {
  console.log('关键字搜索:', filters.keyword)
}

const handleTabClick = (tab: any) => {
  console.log('切换标签页:', tab.props.name)
}

const handleNodeClick = (data: any) => {
  console.log('点击树节点:', data)
  selectedUnitDetail.value = {
    ...selectedUnitDetail.value,
    unitCode: data.id,
    unitName: data.name,
    type: data.type
  }
}

const handleCheckChange = (data: any, checked: boolean) => {
  console.log('树节点选中状态变更:', data, checked)
}

const handleUnitSelectionChange = (selection: any[]) => {
  selectedUnits.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'tree' ? 'list' : 'tree'
}

// 递归获取所有节点的key
const getAllNodeKeys = (nodes: any[]): string[] => {
  const keys: string[] = []
  
  const traverse = (nodeList: any[]) => {
    nodeList.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(nodes)
  return keys
}

// 展开全部节点
const expandAllNodes = () => {
  nextTick(() => {
    try {
      // 先收起所有节点
      collapseAllNodesInternal();
      
      // 然后递归展开所有节点
      const expandNode = (el: Element | null) => {
        if (!el) return;
        
        // 找到当前节点的展开图标
        const expandIcon = el.querySelector(':scope > .el-tree-node__content > .el-tree-node__expand-icon');
        if (expandIcon && !expandIcon.classList.contains('is-leaf') && !expandIcon.classList.contains('expanded')) {
          (expandIcon as HTMLElement).click();
        }
        
        // 处理子节点
        const childNodes = el.querySelectorAll(':scope > .el-tree-node__children > .el-tree-node');
        childNodes.forEach(childNode => {
          expandNode(childNode);
        });
      };
      
      // 从根节点开始展开
      const rootNodes = document.querySelectorAll('.el-tree > .el-tree-node');
      rootNodes.forEach(rootNode => {
        expandNode(rootNode);
      });
    } catch (e) {
      console.error('展开所有节点失败:', e);
      ElMessage.error('展开所有节点失败');
    }
  });
};

// 展开到指定层级
const expandToLevel = (level: number) => {
  nextTick(() => {
    try {
      // 先收起所有节点
      collapseAllNodesInternal();
      
      // 然后展开到指定层级
      const expandNodeToLevel = (el: Element | null, currentLevel: number) => {
        if (!el || currentLevel > level) return;
        
        // 找到当前节点的展开图标
        const expandIcon = el.querySelector(':scope > .el-tree-node__content > .el-tree-node__expand-icon');
        if (expandIcon && !expandIcon.classList.contains('is-leaf') && !expandIcon.classList.contains('expanded')) {
          (expandIcon as HTMLElement).click();
        }
        
        // 处理子节点
        if (currentLevel < level) {
          const childNodes = el.querySelectorAll(':scope > .el-tree-node__children > .el-tree-node');
          childNodes.forEach(childNode => {
            expandNodeToLevel(childNode, currentLevel + 1);
          });
        }
      };
      
      // 从根节点开始展开
      const rootNodes = document.querySelectorAll('.el-tree > .el-tree-node');
      rootNodes.forEach(rootNode => {
        expandNodeToLevel(rootNode, 1);
      });
    } catch (e) {
      console.error(`展开到层级 ${level} 失败:`, e);
      ElMessage.error(`展开到层级 ${level} 失败`);
    }
  });
};

// 展开异常节点
const expandAbnormalNodes = () => {
  nextTick(() => {
    try {
      // 先收起所有节点
      collapseAllNodesInternal();
      
      // 找到所有异常节点
      const abnormalTags = document.querySelectorAll('.el-tree .el-tag--danger');
      
      // 对于每个异常节点，展开其路径
      abnormalTags.forEach(tag => {
        // 获取包含异常标签的树节点
        let treeNode = tag.closest('.el-tree-node');
        
        // 展开该节点的所有父节点
        while (treeNode) {
          // 找到父节点
          const parentElement = treeNode.parentElement;
          const parentTreeNode = parentElement ? parentElement.closest('.el-tree-node') : null;
          
          if (parentTreeNode) {
            // 展开父节点
            const expandIcon = parentTreeNode.querySelector(':scope > .el-tree-node__content > .el-tree-node__expand-icon');
            if (expandIcon && !expandIcon.classList.contains('is-leaf') && !expandIcon.classList.contains('expanded')) {
              (expandIcon as HTMLElement).click();
            }
          }
          
          // 移动到父节点继续处理
          treeNode = parentTreeNode as Element;
        }
      });
    } catch (e) {
      console.error('展开异常节点失败:', e);
      ElMessage.error('展开异常节点失败');
    }
  });
};

// 内部方法：收起所有节点
const collapseAllNodesInternal = () => {
  const treeNodes = document.querySelectorAll('.el-tree-node.is-expanded');
  treeNodes.forEach((node) => {
    const expandIcon = node.querySelector('.el-tree-node__expand-icon');
    if (expandIcon && !expandIcon.classList.contains('is-leaf')) {
      (expandIcon as HTMLElement).click();
    }
  });
};

// 收起全部节点
const collapseAll = () => {
  nextTick(() => {
    try {
      collapseAllNodesInternal();
      ElMessage.success('已收起所有节点');
    } catch (e) {
      console.error('收起节点操作失败:', e);
      ElMessage.error('收起节点操作失败');
    }
  });
};

// 处理展开命令
const handleExpandCommand = (command: string) => {
  let message = '';
  
  switch (command) {
    case 'expandAll':
      expandAllNodes();
      message = '已展开所有节点';
      break;
    case 'expandLevel1':
      expandToLevel(1);
      message = '已展开到建筑级';
      break;
    case 'expandLevel2':
      expandToLevel(2);
      message = '已展开到楼层级';
      break;
    case 'expandAbnormal':
      expandAbnormalNodes();
      message = '已展开异常单元路径';
      break;
    default:
      message = '未知操作';
  }
  
  ElMessage.success(message);
};

// 刷新树形数据
const refreshTreeData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('树形数据刷新成功')
  }, 1000)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const addUnit = () => {
  isEditUnit.value = false
  Object.assign(unitForm, {
    unitCode: '',
    unitName: '',
    type: '',
    parentId: [],
    area: 0,
    usableArea: 0,
    manager: '',
    phone: '',
    remarks: ''
  })
  showUnitDialog.value = true
}

const editUnit = (unit: any) => {
  isEditUnit.value = true
  Object.assign(unitForm, unit)
  showUnitDialog.value = true
}

const viewUnit = (unit: any) => {
  selectedUnitDetail.value = unit
  activeTab.value = 'details'
}

const deleteUnit = (unit: any) => {
  ElMessageBox.confirm(
    `确定要删除单元 "${unit.unitName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('单元删除成功')
  }).catch(() => {
    // 取消删除
  })
}

const saveUnit = () => {
  unitFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success(isEditUnit.value ? '单元更新成功' : '单元创建成功')
      showUnitDialog.value = false
    }
  })
}

const batchDeleteUnits = () => {
  if (selectedUnits.value.length === 0) {
    ElMessage.warning('请选择要删除的单元')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedUnits.value.length} 个单元吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success(`成功删除 ${selectedUnits.value.length} 个单元`)
    selectedUnits.value = []
  }).catch(() => {
    // 取消删除
  })
}

const addDeviceAssociation = () => {
  Object.assign(associationForm, {
    unitId: [],
    deviceId: '',
    associationType: '',
    description: ''
  })
  showAssociationDialog.value = true
}

const editAssociation = (association: any) => {
  ElMessage.info('编辑设备关联功能开发中')
}

const removeAssociation = (association: any) => {
  ElMessageBox.confirm(
    `确定要移除设备 "${association.deviceName}" 与单元的关联吗？`,
    '确认移除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('设备关联移除成功')
  }).catch(() => {
    // 取消移除
  })
}

const saveAssociation = () => {
  associationFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      ElMessage.success('设备关联保存成功')
      showAssociationDialog.value = false
    }
  })
}

const exportUnits = () => {
  ElMessage.success('单元数据导出成功')
}

const importUnits = () => {
  ElMessage.info('单元数据导入功能开发中')
}

// 组件挂载
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.unit-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3, .card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.filter-item label {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  min-width: 80px;
}

.stats-overview {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 30px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  align-items: center;
  min-width: 180px;
  padding: 0 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stats-info {
  flex: 1;
}

.stats-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stats-value-row {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stats-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stats-trend {
  font-size: 11px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.stats-divider {
  width: 1px;
  height: 60%;
  background-color: #ebeef5;
  margin: 0 15px;
}

.overview-card :deep(.el-card__body) {
  padding: 20px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.node-icon {
  font-size: 16px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.device-card {
  display: flex;
  align-items: center;
  gap: 10px;
}

.device-icon {
  font-size: 20px;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.device-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

@media (max-width: 768px) {
  .stats-overview {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-divider {
    display: none;
  }
  
  .stats-item {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
  }
}
</style> 