<template>
  <div class="login-container">
    <!-- 左侧区域 -->
    <div class="login-left">
      <div class="left-content">
        <h1>能耗管理系统产品原型</h1>
        <p>高效、智能的能耗管理解决方案</p>
      </div>
    </div>
    
    <!-- 右侧区域 -->
    <div class="login-right">
      <div class="login-card">
        <div class="login-header">
          <h1 class="main-title">能耗管理系统产品原型</h1>
          <p class="sub-title">欢迎使用，请登录</p>
        </div>
        
        <!-- 提示区域 -->
        <el-alert
          title="示例账号：admin / 密码：123456"
          type="info"
          :closable="false"
          class="login-alert"
        />
        
        <el-form ref="loginForm$" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="验证码"
                class="captcha-input"
              >
                <template #prefix>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                {{ captchaCode }}
              </div>
            </div>
          </el-form-item>
          
          <div class="login-options">
            <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
            <el-link type="primary" :underline="false" class="forgot-password">忘记密码?</el-link>
          </div>
          
          <el-form-item>
            <el-button type="primary" :loading="loading" class="login-button" @click="handleLogin">
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { ElMessage } from 'element-plus'
import { User, Lock, Key } from '@element-plus/icons-vue'

// 路由和认证
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const loading = ref(false)

// 验证码
const captchaCode = ref('')

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 5, max: 10, message: '长度在 5 到 10 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 5, max: 10, message: '长度在 5 到 10 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

// 表单引用
const loginForm$ = ref()

// 生成验证码
const generateCaptcha = () => {
  const code = Math.floor(1000 + Math.random() * 9000).toString()
  captchaCode.value = code
  localStorage.setItem('captchaCode', code)
  return code
}

// 刷新验证码
const refreshCaptcha = () => {
  generateCaptcha()
}

// 初始化
onMounted(() => {
  generateCaptcha()
})

// 登录处理
const handleLogin = async () => {
  if (!loginForm$.value) return
  
  await loginForm$.value.validate(async (valid: boolean) => {
    if (valid) {
      // 验证验证码
      const storedCaptcha = localStorage.getItem('captchaCode')
      if (loginForm.captcha !== storedCaptcha) {
        ElMessage.error('验证码错误')
        refreshCaptcha()
        return
      }
      
      loading.value = true
      
      try {
        // 模拟登录API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 验证用户名和密码
        if (loginForm.username === 'admin' && loginForm.password === '123456') {
          // 登录成功
          const mockUser = {
            id: '1',
            username: loginForm.username,
            roles: ['admin']
          }
          
          const mockToken = 'mock-token-' + Date.now()
          
          // 存储用户信息和token
          authStore.setUser(mockUser)
          authStore.setToken(mockToken)
          
          // 如果选择记住我，可以设置更长的token过期时间
          if (loginForm.remember) {
            localStorage.setItem('rememberUser', 'true')
          }
          
          // 显示成功消息
          ElMessage.success('登录成功')
          
          // 重定向到来源页面或能源总览
          const redirectPath = route.query.redirect as string || '/energy-overview'
          router.push(redirectPath)
        } else {
          throw new Error('用户名或密码错误')
        }
      } catch (error) {
        ElMessage.error('登录失败，请检查用户名和密码')
        console.error('Login error:', error)
        refreshCaptcha()
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  width: 100%;
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 40px;
}

.left-content {
  max-width: 500px;
}

.left-content h1 {
  font-size: 36px;
  margin-bottom: 20px;
}

.left-content p {
  font-size: 18px;
  opacity: 0.8;
}

.login-right {
  flex: 1;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-card {
  width: 420px;
  padding: 40px;
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
}

.main-title {
  font-size: 28px;
  color: #303133;
  margin-bottom: 10px;
}

.sub-title {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.login-alert {
  margin-bottom: 20px;
}

.login-form {
  margin-top: 20px;
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 40px;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  letter-spacing: 4px;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.login-button {
  width: 100%;
  padding: 12px 0;
  height: 40px;
  border-radius: 4px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forgot-password {
  margin-left: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-left {
    display: none;
  }
  
  .login-right {
    flex: 1;
  }
  
  .login-card {
    width: 90%;
  }
}
</style> 