// 用户信息类型
export interface UserInfo {
  id: string;
  username: string;
  avatar?: string;
  roles: string[];
}

// 路由元信息类型
export interface RouteMeta {
  title: string;
  icon?: string;
  requiresAuth?: boolean;
  requiresLayout?: boolean;
  [key: string]: any; // 添加字符串索引签名
  [key: symbol]: any; // 添加symbol索引签名
}

// 菜单项类型
export interface MenuItem {
  title: string;
  path: string;
  icon?: string;
  children?: MenuItem[];
} 