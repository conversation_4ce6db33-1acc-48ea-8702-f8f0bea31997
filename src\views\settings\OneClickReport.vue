<template>
  <div class="one-click-report" v-loading="loading" element-loading-text="正在处理报送数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 1. 状态总览 - 24列全宽，渐变背景 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="overview-card">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>一键报送管理中心</h2>
                <div class="status-indicator" :class="systemStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(systemStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(systemStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="overview-metrics">
            <div class="metric-item">
              <div class="metric-icon success">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportStats.successCount }}</div>
                <div class="metric-label">成功报送</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon pending">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportStats.pendingCount }}</div>
                <div class="metric-label">待报送</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon failed">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportStats.failedCount }}</div>
                <div class="metric-label">报送失败</div>
              </div>
            </div>
            
            <div class="metric-divider"></div>
            
            <div class="metric-item">
              <div class="metric-icon total">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ reportStats.totalCount }}</div>
                <div class="metric-label">总报送数</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 2. 实时监控 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                报送任务监控
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedReportType" size="small" style="width: 120px;">
                  <el-option label="全部类型" value="" />
                  <el-option label="能耗数据" value="energy" />
                  <el-option label="环境数据" value="environment" />
                  <el-option label="设备数据" value="device" />
                  <el-option label="告警数据" value="alarm" />
                </el-select>
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="data-grid">
            <div class="data-card" v-for="task in reportTasks" :key="task.id">
              <div class="task-header">
                <div class="task-icon" :class="task.status">
                  <el-icon>
                    <component :is="getTaskIcon(task.type)" />
                  </el-icon>
                </div>
                <div class="task-info">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-type">{{ task.type }}</div>
                </div>
                <div class="task-status" :class="getTaskStatusClass(task.status)">
                  {{ getTaskStatusText(task.status) }}
                </div>
              </div>
              <div class="task-details">
                <div class="detail-item">
                  <span class="detail-label">目标平台：</span>
                  <span class="detail-value">{{ task.targetPlatform }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">数据量：</span>
                  <span class="detail-value">{{ task.dataCount }}条</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">最后执行：</span>
                  <span class="detail-value">{{ task.lastExecution }}</span>
                </div>
              </div>
              <div class="task-actions">
                <el-button type="primary" size="small" @click="executeTask(task)">
                  执行
                </el-button>
                <el-button type="success" size="small" @click="configTask(task)">
                  配置
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 3. 主要功能 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                报送模板管理
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showCreateTemplateDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建模板
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="template-grid">
            <div class="template-card" v-for="template in reportTemplates" :key="template.id">
              <div class="template-header">
                <div class="template-icon" :class="template.category">
                  <el-icon>
                    <component :is="getTemplateIcon(template.category)" />
                  </el-icon>
                </div>
                <div class="template-info">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-description">{{ template.description }}</div>
                </div>
              </div>
              <div class="template-details">
                <div class="detail-item">
                  <span class="detail-label">数据源：</span>
                  <span class="detail-value">{{ template.dataSource }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">报送频率：</span>
                  <span class="detail-value">{{ template.frequency }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">格式类型：</span>
                  <span class="detail-value">{{ template.format }}</span>
                </div>
              </div>
              <div class="template-actions">
                <el-button type="primary" size="small" @click="editTemplate(template)">
                  编辑
                </el-button>
                <el-button type="success" size="small" @click="useTemplate(template)">
                  使用
                </el-button>
                <el-button type="danger" size="small" @click="deleteTemplate(template)">
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 4. 详细管理 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                报送历史记录
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedStatus" size="small" style="width: 120px;">
                  <el-option label="全部状态" value="" />
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="failed" />
                  <el-option label="进行中" value="running" />
                </el-select>
                <el-button type="success" size="small" @click="exportHistory">
                  <el-icon><Operation /></el-icon>
                  导出记录
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="reportHistory" border stripe style="width: 100%">
            <el-table-column prop="timestamp" label="执行时间" width="160" align="center" />
            <el-table-column prop="taskName" label="任务名称" min-width="150" />
            <el-table-column prop="targetPlatform" label="目标平台" width="150" align="center" />
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getHistoryStatusType(row.status)">
                  {{ getHistoryStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dataCount" label="数据量" width="100" align="center" />
            <el-table-column prop="duration" label="耗时(s)" width="100" align="center" />
            <el-table-column prop="message" label="执行结果" min-width="200" />
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewHistoryDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalHistory"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  SuccessFilled,
  CircleCloseFilled,
  Timer,
  Monitor,
  Setting,
  Operation
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '系统设置', to: '/settings' },
  { text: '一键报送' }
])

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const systemStatus = ref('normal')
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const selectedReportType = ref('')
const selectedStatus = ref('')
const showCreateTemplateDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalHistory = ref(50)

// 报送统计数据
const reportStats = reactive({
  successCount: 156,
  pendingCount: 8,
  failedCount: 3,
  totalCount: 167
})

// 报送任务数据
const reportTasks = ref([
  {
    id: 'TASK001',
    name: '能耗数据日报',
    type: '能耗数据',
    status: 'success',
    targetPlatform: '国家能耗监测平台',
    dataCount: 1250,
    lastExecution: '2024-01-15 09:00:00'
  },
  {
    id: 'TASK002',
    name: '环境监测月报',
    type: '环境数据',
    status: 'pending',
    targetPlatform: '环保部门平台',
    dataCount: 850,
    lastExecution: '2024-01-01 10:00:00'
  },
  {
    id: 'TASK003',
    name: '设备运行状态',
    type: '设备数据',
    status: 'running',
    targetPlatform: '设备管理平台',
    dataCount: 320,
    lastExecution: '2024-01-15 14:20:00'
  },
  {
    id: 'TASK004',
    name: '告警信息推送',
    type: '告警数据',
    status: 'failed',
    targetPlatform: '应急管理平台',
    dataCount: 45,
    lastExecution: '2024-01-15 13:45:00'
  }
])

// 报送模板数据
const reportTemplates = ref([
  {
    id: 'TPL001',
    name: '能耗数据标准模板',
    description: '符合国家标准的能耗数据报送模板',
    category: 'energy',
    dataSource: '能耗监测系统',
    frequency: '每日',
    format: 'XML'
  },
  {
    id: 'TPL002',
    name: '环境数据模板',
    description: '环境监测数据标准报送格式',
    category: 'environment',
    dataSource: '环境监测系统',
    frequency: '每月',
    format: 'JSON'
  },
  {
    id: 'TPL003',
    name: '设备状态模板',
    description: '设备运行状态数据报送模板',
    category: 'device',
    dataSource: '设备管理系统',
    frequency: '实时',
    format: 'CSV'
  },
  {
    id: 'TPL004',
    name: '告警数据模板',
    description: '系统告警信息推送模板',
    category: 'alarm',
    dataSource: '告警管理系统',
    frequency: '即时',
    format: 'JSON'
  }
])

// 报送历史记录数据
const reportHistory = ref([
  {
    id: 'HIS001',
    timestamp: '2024-01-15 09:00:00',
    taskName: '能耗数据日报',
    targetPlatform: '国家能耗监测平台',
    status: 'success',
    dataCount: 1250,
    duration: 45,
    message: '数据报送成功，已确认接收'
  },
  {
    id: 'HIS002',
    timestamp: '2024-01-15 08:30:00',
    taskName: '环境监测月报',
    targetPlatform: '环保部门平台',
    status: 'failed',
    dataCount: 0,
    duration: 120,
    message: '连接超时，报送失败'
  },
  {
    id: 'HIS003',
    timestamp: '2024-01-15 08:00:00',
    taskName: '设备运行状态',
    targetPlatform: '设备管理平台',
    status: 'success',
    dataCount: 320,
    duration: 28,
    message: '设备状态数据报送完成'
  },
  {
    id: 'HIS004',
    timestamp: '2024-01-15 07:45:00',
    taskName: '告警信息推送',
    targetPlatform: '应急管理平台',
    status: 'running',
    dataCount: 45,
    duration: 0,
    message: '正在执行中...'
  }
])

// 工具函数
const getStatusIcon = (status: string) => {
  const iconMap = {
    'normal': SuccessFilled,
    'warning': Timer,
    'danger': CircleCloseFilled
  }
  return iconMap[status] || SuccessFilled
}

const getStatusText = (status: string) => {
  const textMap = {
    'normal': '系统正常',
    'warning': '需要关注',
    'danger': '存在异常'
  }
  return textMap[status] || '未知状态'
}

const getTaskIcon = (type: string) => {
  const iconMap = {
    '能耗数据': Monitor,
    '环境数据': Setting,
    '设备数据': Operation,
    '告警数据': Timer
  }
  return iconMap[type] || Monitor
}

const getTaskStatusClass = (status: string) => {
  const classMap = {
    'success': 'status-success',
    'pending': 'status-pending',
    'running': 'status-running',
    'failed': 'status-failed'
  }
  return classMap[status] || 'status-unknown'
}

const getTaskStatusText = (status: string) => {
  const textMap = {
    'success': '已完成',
    'pending': '待执行',
    'running': '执行中',
    'failed': '执行失败'
  }
  return textMap[status] || '未知'
}

const getTemplateIcon = (category: string) => {
  const iconMap = {
    'energy': Monitor,
    'environment': Setting,
    'device': Operation,
    'alarm': Timer
  }
  return iconMap[category] || Setting
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  ElMessage.success(`自动刷新已${autoRefresh.value ? '开启' : '关闭'}`)
}

const executeTask = (task: any) => {
  ElMessage.info(`正在执行任务: ${task.name}`)
  task.status = 'running'
  setTimeout(() => {
    task.status = 'success'
    task.lastExecution = new Date().toLocaleString('zh-CN')
    ElMessage.success(`任务执行成功: ${task.name}`)
  }, 2000)
}

const configTask = (task: any) => {
  ElMessage.info(`配置任务: ${task.name}`)
}

const editTemplate = (template: any) => {
  ElMessage.info(`编辑模板: ${template.name}`)
}

const useTemplate = (template: any) => {
  ElMessage.success(`使用模板: ${template.name}`)
}

const deleteTemplate = (template: any) => {
  ElMessage.warning(`删除模板: ${template.name}`)
}

const getHistoryStatusType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning'
  }
  return typeMap[status] || 'info'
}

const getHistoryStatusText = (status: string) => {
  const textMap = {
    'success': '成功',
    'failed': '失败',
    'running': '进行中'
  }
  return textMap[status] || '未知'
}

const exportHistory = () => {
  ElMessage.success('历史记录导出成功')
}

const viewHistoryDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.taskName}`)
}
</script>

<style scoped>
.one-click-report {
  padding: 0;
}

/* 1. 状态总览样式 - 渐变背景 */
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-card :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.overview-card :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.status-indicator.normal .status-icon {
  color: #67c23a;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.overview-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 2px solid;
}

.metric-icon.success {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border-color: #67c23a;
}

.metric-icon.pending {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border-color: #e6a23c;
}

.metric-icon.failed {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border-color: #f56c6c;
}

.metric-icon.total {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border-color: #409eff;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

/* 2. 数据网格样式 - 自适应布局 */
.data-grid,
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.data-card,
.template-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.data-card:hover,
.template-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-header,
.template-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.task-icon,
.template-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.task-icon.success,
.template-icon.energy {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.task-icon.pending,
.template-icon.environment {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.task-icon.running,
.template-icon.device {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.task-icon.failed,
.template-icon.alarm {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.task-info,
.template-info {
  flex: 1;
}

.task-name,
.template-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.task-type {
  font-size: 12px;
  color: #909399;
}

.template-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.task-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.task-status.status-success {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border: 1px solid #67c23a;
}

.task-status.status-pending {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border: 1px solid #e6a23c;
}

.task-status.status-running {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border: 1px solid #409eff;
}

.task-status.status-failed {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.task-details,
.template-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.task-actions,
.template-actions {
  display: flex;
  gap: 8px;
}

.task-actions .el-button,
.template-actions .el-button {
  flex: 1;
}

/* 通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .data-grid,
  .template-grid {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .header-controls .el-select,
  .header-controls .el-button {
    width: 100%;
  }
}
</style>
