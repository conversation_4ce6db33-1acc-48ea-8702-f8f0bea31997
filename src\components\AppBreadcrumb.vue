<template>
  <div class="app-breadcrumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumbs" 
        :key="index"
        :to="item.path && index !== breadcrumbs.length - 1 ? { path: item.path } : null"
      >
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, RouteLocationNormalizedLoaded } from 'vue-router'

interface Breadcrumb {
  title: string;
  path?: string;
}

const route = useRoute()
const breadcrumbs = ref<Breadcrumb[]>([])

// 根据路由生成面包屑
const generateBreadcrumbs = (route: RouteLocationNormalizedLoaded) => {
  const paths = route.path.split('/').filter(Boolean)
  const result: Breadcrumb[] = []
  
  let currentPath = ''
  
  paths.forEach((path) => {
    currentPath += `/${path}`
    const matched = route.matched.find(r => r.path === currentPath)
    
    if (matched && matched.meta.title) {
      result.push({
        title: matched.meta.title as string,
        path: currentPath
      })
    }
  })
  
  return result
}

// 监听路由变化更新面包屑
watch(() => route.path, () => {
  breadcrumbs.value = generateBreadcrumbs(route)
}, { immediate: true })
</script>

<style scoped>
.app-breadcrumb {
  font-size: 14px;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  font-weight: bold;
  color: var(--text-color);
}
</style> 