<template>
  <div class="device-management">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="page-card">
          <template #header>
            <div class="card-header">
              <h3>设备资产管理</h3>
            </div>
          </template>
          <div class="page-content">
            <el-empty description="功能开发中，敬请期待">
              <template #image>
                <el-icon size="60"><Box /></el-icon>
              </template>
              <div class="empty-text">
                <p>设备资产管理将整合以下功能：</p>
                <ul>
                  <li>设备管理</li>
                  <li>用电安全</li>
                </ul>
                <p>聚焦设备全生命周期管理</p>
              </div>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
import { Box } from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '设备资产管理' }
])
</script>

<style scoped>
.device-management {
  padding: 0;
}

.page-card {
  min-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-text {
  margin-top: 20px;
  text-align: left;
}

.empty-text p {
  margin: 10px 0;
  color: #606266;
}

.empty-text ul {
  margin: 15px 0;
  padding-left: 20px;
}

.empty-text li {
  margin: 5px 0;
  color: #909399;
}
</style> 