<template>
  <div class="energy-comparison" v-loading="loading" element-loading-text="正在加载对比数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 对比设置 -->
    <el-card class="comparison-settings">
      <template #header>
        <div class="card-header">
          <h3>对比设置</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="executeComparison" :loading="loading">
              <el-icon><TrendCharts /></el-icon>
              执行对比
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="setting-group">
            <label class="setting-label">对比维度：</label>
            <el-select v-model="comparisonDimension" placeholder="请选择对比维度" size="small" @change="handleDimensionChange">
              <el-option label="时间对比" value="time" />
              <el-option label="建筑对比" value="building" />
              <el-option label="能源类型对比" value="energy-type" />
              <el-option label="部门对比" value="department" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="setting-group">
            <label class="setting-label">对比周期：</label>
            <el-select v-model="comparisonPeriod" placeholder="请选择对比周期" size="small" @change="handlePeriodChange">
              <el-option label="日对比" value="daily" />
              <el-option label="周对比" value="weekly" />
              <el-option label="月对比" value="monthly" />
              <el-option label="年对比" value="yearly" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="setting-group">
            <label class="setting-label">时间范围：</label>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="12">
          <div class="setting-group">
            <label class="setting-label">对比对象：</label>
            <el-select 
              v-model="comparisonObjects" 
              multiple 
              placeholder="请选择对比对象" 
              size="small" 
              collapse-tags
              collapse-tags-tooltip
              max-collapse-tags="3"
              style="width: 100%"
              @change="handleObjectsChange"
            >
              <el-option 
                v-for="option in availableObjects" 
                :key="option.value" 
                :label="option.label" 
                :value="option.value" 
              />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="setting-group">
            <label class="setting-label">能源类型：</label>
            <el-checkbox-group v-model="selectedEnergyTypes" size="small" @change="handleEnergyTypesChange">
              <el-checkbox label="electric">用电</el-checkbox>
              <el-checkbox label="water">用水</el-checkbox>
              <el-checkbox label="gas">燃气</el-checkbox>
              <el-checkbox label="heating">供暖</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 对比结果 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 趋势对比图表 -->
      <el-col :span="14">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>趋势对比分析</h3>
              <div class="header-controls">
                <el-button type="text" size="small" @click="exportChart('trend')">
                  <el-icon><Download /></el-icon>
                  导出图表
                </el-button>
              </div>
            </div>
          </template>
          <div ref="trendComparisonChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
      
      <!-- 占比对比图表 -->
      <el-col :span="10">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>占比对比分析</h3>
              <div class="header-controls">
                <el-button type="text" size="small" @click="exportChart('pie')">
                  <el-icon><Download /></el-icon>
                  导出图表
                </el-button>
              </div>
            </div>
          </template>
          <div ref="pieComparisonChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数值对比和排行榜 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 数值对比 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>数值对比</h3>
              <div class="header-controls">
                <el-button type="text" size="small" @click="exportChart('bar')">
                  <el-icon><Download /></el-icon>
                  导出图表
                </el-button>
              </div>
            </div>
          </template>
          <div ref="barComparisonChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
      
      <!-- 排行榜 -->
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>能耗排行榜</h3>
              <div class="header-controls">
                <el-radio-group v-model="rankingType" size="small" @change="handleRankingTypeChange">
                  <el-radio-button label="total">总能耗</el-radio-button>
                  <el-radio-button label="growth">增长率</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <div class="ranking-list" style="height: 350px;">
            <div 
              v-for="(item, index) in rankingData" 
              :key="index"
              class="ranking-item"
              :class="{ 'top-three': index < 3 }"
            >
              <div class="ranking-number">
                <el-icon v-if="index === 0" color="#FFD700"><Trophy /></el-icon>
                <el-icon v-else-if="index === 1" color="#C0C0C0"><Medal /></el-icon>
                <el-icon v-else-if="index === 2" color="#CD7F32"><Medal /></el-icon>
                <span v-else class="number">{{ index + 1 }}</span>
              </div>
              <div class="ranking-info">
                <div class="name">{{ item.name }}</div>
                <div class="value">{{ item.value }}</div>
              </div>
              <div class="ranking-trend">
                <el-tag :type="item.trend === 'up' ? 'danger' : 'success'" size="small">
                  {{ item.trend === 'up' ? '↑' : '↓' }} {{ item.change }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细对比数据表格 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>详细对比数据</h3>
          <div class="header-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索对比数据"
              size="small"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredTableData" 
        border 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        height="400"
      >
        <el-table-column prop="name" label="对比对象" min-width="120" align="center" />
        <el-table-column prop="period" label="时间周期" min-width="120" align="center" />
        <el-table-column prop="electric" label="用电量(kWh)" min-width="120" align="center">
          <template #default="{ row }">
            <span :style="{ color: '#409eff', fontWeight: 'bold' }">{{ row.electric.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="water" label="用水量(t)" min-width="100" align="center">
          <template #default="{ row }">
            <span :style="{ color: '#67c23a', fontWeight: 'bold' }">{{ row.water.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="gas" label="燃气量(m³)" min-width="120" align="center">
          <template #default="{ row }">
            <span :style="{ color: '#e6a23c', fontWeight: 'bold' }">{{ row.gas.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="heating" label="供暖量(GJ)" min-width="120" align="center">
          <template #default="{ row }">
            <span :style="{ color: '#f56c6c', fontWeight: 'bold' }">{{ row.heating.toLocaleString() }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total" label="总能耗(标煤)" min-width="130" align="center">
          <template #default="{ row }">
            <el-tag type="primary" size="small">{{ row.total.toFixed(2) }}t</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="growth" label="增长率" min-width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.growth >= 0 ? 'danger' : 'success'" size="small">
              {{ row.growth >= 0 ? '+' : '' }}{{ row.growth.toFixed(1) }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)" style="margin-right: 5px;">
              详情
            </el-button>
            <el-button type="success" size="small" @click="exportSingleRecord(row)">
              导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="对比详情"
      width="70%"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedRecord">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="对比对象">{{ selectedRecord.name }}</el-descriptions-item>
          <el-descriptions-item label="时间周期">{{ selectedRecord.period }}</el-descriptions-item>
          <el-descriptions-item label="总能耗">{{ selectedRecord.total.toFixed(2) }}t标煤</el-descriptions-item>
          <el-descriptions-item label="用电量">{{ selectedRecord.electric.toLocaleString() }}kWh</el-descriptions-item>
          <el-descriptions-item label="用水量">{{ selectedRecord.water.toLocaleString() }}t</el-descriptions-item>
          <el-descriptions-item label="燃气量">{{ selectedRecord.gas.toLocaleString() }}m³</el-descriptions-item>
          <el-descriptions-item label="供暖量">{{ selectedRecord.heating.toLocaleString() }}GJ</el-descriptions-item>
          <el-descriptions-item label="增长率">
            <el-tag :type="selectedRecord.growth >= 0 ? 'danger' : 'success'">
              {{ selectedRecord.growth >= 0 ? '+' : '' }}{{ selectedRecord.growth.toFixed(1) }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="能效等级">
            <el-rate v-model="selectedRecord.efficiency" disabled show-score />
          </el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>历史趋势</h4>
          <div ref="detailChart" style="height: 300px;"></div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="exportDetailData">导出详情</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { 
  Refresh, Download, Search, TrendCharts, 
  Trophy, Medal
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '能耗对比', to: '' }
])

// 基础数据
const loading = ref(false)
const comparisonDimension = ref('time')
const comparisonPeriod = ref('monthly')
const dateRange = ref<[Date, Date]>()
const comparisonObjects = ref(['building-a', 'building-b'])
const selectedEnergyTypes = ref(['electric', 'water', 'gas', 'heating'])
const rankingType = ref('total')
const searchKeyword = ref('')
const showDetailDialog = ref(false)
const selectedRecord = ref<any>(null)

// 图表引用
const trendComparisonChart = ref<HTMLElement>()
const pieComparisonChart = ref<HTMLElement>()
const barComparisonChart = ref<HTMLElement>()
const detailChart = ref<HTMLElement>()

// 可选对比对象
const availableObjects = computed(() => {
  switch (comparisonDimension.value) {
    case 'time':
      return [
        { label: '本年', value: 'current-year' },
        { label: '去年', value: 'last-year' },
        { label: '前年', value: 'year-before-last' }
      ]
    case 'building':
      return [
        { label: '办公大楼A', value: 'building-a' },
        { label: '办公大楼B', value: 'building-b' },
        { label: '生产车间', value: 'workshop' },
        { label: '配套设施', value: 'facilities' }
      ]
    case 'energy-type':
      return [
        { label: '用电', value: 'electric' },
        { label: '用水', value: 'water' },
        { label: '燃气', value: 'gas' },
        { label: '供暖', value: 'heating' }
      ]
    case 'department':
      return [
        { label: '研发部', value: 'rd-dept' },
        { label: '生产部', value: 'production-dept' },
        { label: '行政部', value: 'admin-dept' },
        { label: '销售部', value: 'sales-dept' }
      ]
    default:
      return []
  }
})

// 排行榜数据
const rankingData = computed(() => {
  if (rankingType.value === 'total') {
    return [
      { name: '办公大楼A', value: '2,456.8t标煤', trend: 'up', change: '5.2%' },
      { name: '生产车间', value: '1,832.6t标煤', trend: 'down', change: '2.1%' },
      { name: '办公大楼B', value: '1,245.3t标煤', trend: 'up', change: '3.8%' },
      { name: '配套设施', value: '896.4t标煤', trend: 'down', change: '1.5%' }
    ]
  } else {
    return [
      { name: '配套设施', value: '+8.5%', trend: 'up', change: '2.3%' },
      { name: '办公大楼A', value: '+5.2%', trend: 'up', change: '1.1%' },
      { name: '办公大楼B', value: '+3.8%', trend: 'up', change: '0.9%' },
      { name: '生产车间', value: '-2.1%', trend: 'down', change: '1.2%' }
    ]
  }
})

// 表格数据
const tableData = ref([
  { 
    name: '办公大楼A', period: '2024年1月', electric: 186420, water: 520, gas: 1250, heating: 890, 
    total: 42.6, growth: 5.2, efficiency: 4 
  },
  { 
    name: '办公大楼B', period: '2024年1月', electric: 142680, water: 395, gas: 980, heating: 650, 
    total: 32.8, growth: 3.8, efficiency: 3 
  },
  { 
    name: '生产车间', period: '2024年1月', electric: 298560, water: 680, gas: 1850, heating: 1200, 
    total: 68.4, growth: -2.1, efficiency: 4 
  },
  { 
    name: '配套设施', period: '2024年1月', electric: 89420, water: 245, gas: 580, heating: 420, 
    total: 18.9, growth: 8.5, efficiency: 3 
  },
  { 
    name: '办公大楼A', period: '2023年12月', electric: 178950, water: 485, gas: 1180, heating: 856, 
    total: 40.2, growth: 2.8, efficiency: 4 
  },
  { 
    name: '办公大楼B', period: '2023年12月', electric: 135420, water: 368, gas: 925, heating: 615, 
    total: 31.1, growth: 1.9, efficiency: 3 
  }
])

// 过滤后的表格数据
const filteredTableData = computed(() => {
  if (!searchKeyword.value) {
    return tableData.value
  }
  
  return tableData.value.filter(item => 
    item.name.includes(searchKeyword.value) ||
    item.period.includes(searchKeyword.value)
  )
})

// 事件处理
const handleDimensionChange = () => {
  comparisonObjects.value = []
  nextTick(() => {
    if (availableObjects.value.length > 0) {
      comparisonObjects.value = [availableObjects.value[0].value, availableObjects.value[1]?.value].filter(Boolean)
    }
  })
}

const handlePeriodChange = () => {
  executeComparison()
}

const handleDateRangeChange = () => {
  executeComparison()
}

const handleObjectsChange = () => {
  executeComparison()
}

const handleEnergyTypesChange = () => {
  executeComparison()
}

const handleRankingTypeChange = () => {
  // 排行榜类型变化已通过computed自动处理
}

const handleSearch = () => {
  // 搜索功能通过computed实现
}

// 执行对比
const executeComparison = async () => {
  if (comparisonObjects.value.length < 2) {
    ElMessage.warning('请至少选择2个对比对象')
    return
  }
  
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    nextTick(() => {
      initTrendComparisonChart()
      initPieComparisonChart()
      initBarComparisonChart()
    })
    
    ElMessage.success('对比分析完成！')
  } catch (error) {
    ElMessage.error('对比分析失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 800))
    
    nextTick(() => {
      initTrendComparisonChart()
      initPieComparisonChart()
      initBarComparisonChart()
    })
    
    ElMessage.success('数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 导出图表
const exportChart = async (type: string) => {
  try {
    let chartRef: HTMLElement | undefined
    let filename: string
    
    switch (type) {
      case 'trend':
        chartRef = trendComparisonChart.value
        filename = '趋势对比图'
        break
      case 'pie':
        chartRef = pieComparisonChart.value
        filename = '占比对比图'
        break
      case 'bar':
        chartRef = barComparisonChart.value
        filename = '数值对比图'
        break
      default:
        return
    }
    
    if (!chartRef) return
    
    const chart = echarts.getInstanceByDom(chartRef)
    if (!chart) return
    
    const imageDataURL = chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${filename}_${new Date().toISOString().slice(0, 10)}.png`
    link.href = imageDataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('图表导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 导出数据
const exportData = () => {
  try {
    const exportData = filteredTableData.value.map(item => ({
      对比对象: item.name,
      时间周期: item.period,
      用电量kWh: item.electric,
      用水量t: item.water,
      燃气量m3: item.gas,
      供暖量GJ: item.heating,
      总能耗t标煤: item.total,
      增长率: item.growth + '%'
    }))
    
    const csvContent = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).join(','))
    ].join('\n')
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `能耗对比数据_${new Date().toISOString().slice(0, 10)}.csv`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 查看详情
const viewDetail = (row: any) => {
  selectedRecord.value = row
  showDetailDialog.value = true
  nextTick(() => {
    initDetailChart()
  })
}

// 关闭详情
const handleCloseDetail = () => {
  showDetailDialog.value = false
}

// 导出单条记录
const exportSingleRecord = (row: any) => {
  selectedRecord.value = row
  exportDetailData()
}

// 导出详情数据
const exportDetailData = () => {
  if (!selectedRecord.value) return
  
  const data = {
    基本信息: {
      对比对象: selectedRecord.value.name,
      时间周期: selectedRecord.value.period,
      总能耗: selectedRecord.value.total + 't标煤'
    },
    详细数据: {
      用电量: selectedRecord.value.electric + 'kWh',
      用水量: selectedRecord.value.water + 't',
      燃气量: selectedRecord.value.gas + 'm³',
      供暖量: selectedRecord.value.heating + 'GJ'
    },
    分析指标: {
      增长率: selectedRecord.value.growth + '%',
      能效等级: selectedRecord.value.efficiency + '星'
    }
  }
  
  const jsonData = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.download = `对比详情_${selectedRecord.value.name}_${selectedRecord.value.period}.json`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('详情导出成功！')
}

// 初始化趋势对比图表
const initTrendComparisonChart = () => {
  if (!trendComparisonChart.value) return
  
  const chart = echarts.init(trendComparisonChart.value)
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  
  const option = {
    title: {
      text: '能耗趋势对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['办公大楼A', '办公大楼B', '生产车间'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      name: '能耗(t标煤)',
      axisLabel: { formatter: '{value}' }
    },
    series: [
      {
        name: '办公大楼A',
        type: 'line',
        smooth: true,
        data: [42.6, 38.9, 45.2, 41.8, 48.3, 44.7, 46.9, 43.5, 47.8, 45.1, 42.3, 40.6],
        itemStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
      },
      {
        name: '办公大楼B',
        type: 'line',
        smooth: true,
        data: [32.8, 29.5, 35.1, 31.2, 36.8, 33.4, 35.7, 32.9, 36.2, 34.1, 31.8, 30.5],
        itemStyle: { color: '#67c23a' },
        areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
      },
      {
        name: '生产车间',
        type: 'line',
        smooth: true,
        data: [68.4, 72.1, 65.8, 69.7, 71.3, 67.9, 70.2, 68.8, 66.5, 69.1, 67.3, 65.2],
        itemStyle: { color: '#e6a23c' },
        areaStyle: { color: 'rgba(230, 162, 60, 0.1)' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化占比对比图表
const initPieComparisonChart = () => {
  if (!pieComparisonChart.value) return
  
  const chart = echarts.init(pieComparisonChart.value)
  
  const option = {
    title: {
      text: '能耗占比对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 60
    },
    series: [
      {
        name: '能耗占比',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 42.6, name: '办公大楼A', itemStyle: { color: '#409eff' } },
          { value: 32.8, name: '办公大楼B', itemStyle: { color: '#67c23a' } },
          { value: 68.4, name: '生产车间', itemStyle: { color: '#e6a23c' } },
          { value: 18.9, name: '配套设施', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化柱状对比图表
const initBarComparisonChart = () => {
  if (!barComparisonChart.value) return
  
  const chart = echarts.init(barComparisonChart.value)
  
  const option = {
    title: {
      text: '数值对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      data: ['用电量', '用水量', '燃气量'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['办公大楼A', '办公大楼B', '生产车间', '配套设施']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用电量',
        type: 'bar',
        data: [186.4, 142.7, 298.6, 89.4],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '用水量',
        type: 'bar',
        data: [520, 395, 680, 245],
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '燃气量',
        type: 'bar',
        data: [1250, 980, 1850, 580],
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化详情图表
const initDetailChart = () => {
  if (!detailChart.value) return
  
  const chart = echarts.init(detailChart.value)
  
  const option = {
    tooltip: { trigger: 'axis' },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '10%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: { type: 'value', name: '能耗(t标煤)' },
    series: [{
      name: '历史趋势',
      type: 'line',
      smooth: true,
      data: [42.6, 38.9, 45.2, 41.8, 48.3, 44.7],
      itemStyle: { color: '#409eff' },
      areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
    }]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  // 设置默认日期范围
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  dateRange.value = [lastMonth, now]
  
  nextTick(() => {
    executeComparison()
  })
})
</script>

<style scoped>
.energy-comparison {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.comparison-settings {
  margin-bottom: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.ranking-list {
  height: 350px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-item.top-three {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), transparent);
}

.ranking-number {
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.ranking-number .number {
  font-size: 18px;
  font-weight: bold;
  color: #909399;
}

.ranking-info {
  flex: 1;
  margin-left: 15px;
}

.ranking-info .name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.ranking-info .value {
  font-size: 16px;
  color: #409eff;
  font-weight: bold;
  margin-top: 2px;
}

.ranking-trend {
  width: 80px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .setting-group {
    margin-bottom: 15px;
  }
}
</style> 