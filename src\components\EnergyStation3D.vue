<template>
  <div ref="container" class="energy-station-3d"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

interface DeviceData {
  id: string
  name: string
  type: 'generator' | 'transformer' | 'solar' | 'wind' | 'battery' | 'building' | 'charging_station'
  position: { x: number; y: number; z: number }
  status: 'normal' | 'warning' | 'error' | 'offline' | 'charging' | 'idle'
  power: number
  efficiency: number
}

const props = defineProps<{
  devices?: DeviceData[]
  autoRotate?: boolean
}>()

const container = ref<HTMLElement>()
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let animationId: number
let deviceMeshes: Map<string, THREE.Mesh> = new Map()
let particleSystems: Map<string, THREE.Points> = new Map()
let compass: THREE.Group
let compassNeedle: THREE.Mesh

// 默认设备数据
const defaultDevices: DeviceData[] = [
  { id: '1', name: '主变压器', type: 'transformer', position: { x: 0, y: 0, z: 0 }, status: 'normal', power: 85, efficiency: 95 },
  { id: '2', name: '发电机组1', type: 'generator', position: { x: -15, y: 0, z: -10 }, status: 'normal', power: 78, efficiency: 88 },
  { id: '3', name: '发电机组2', type: 'generator', position: { x: -15, y: 0, z: 10 }, status: 'warning', power: 65, efficiency: 82 },
  { id: '4', name: '太阳能板阵列', type: 'solar', position: { x: 15, y: 0, z: -15 }, status: 'normal', power: 45, efficiency: 92 },
  { id: '5', name: '风力发电机', type: 'wind', position: { x: 15, y: 0, z: 15 }, status: 'normal', power: 38, efficiency: 89 },
  { id: '6', name: '储能电池', type: 'battery', position: { x: 0, y: 0, z: -20 }, status: 'normal', power: 55, efficiency: 94 },
  { id: '7', name: '办公楼A', type: 'building', position: { x: -25, y: 0, z: 0 }, status: 'normal', power: 25, efficiency: 85 },
  { id: '8', name: '办公楼B', type: 'building', position: { x: 25, y: 0, z: 0 }, status: 'normal', power: 30, efficiency: 87 },
  // 充电桩区域
  { id: '9', name: '充电桩1', type: 'charging_station', position: { x: -8, y: 0, z: 25 }, status: 'charging', power: 80, efficiency: 95 },
  { id: '10', name: '充电桩2', type: 'charging_station', position: { x: -4, y: 0, z: 25 }, status: 'idle', power: 0, efficiency: 95 },
  { id: '11', name: '充电桩3', type: 'charging_station', position: { x: 0, y: 0, z: 25 }, status: 'charging', power: 75, efficiency: 93 },
  { id: '12', name: '充电桩4', type: 'charging_station', position: { x: 4, y: 0, z: 25 }, status: 'idle', power: 0, efficiency: 95 },
  { id: '13', name: '充电桩5', type: 'charging_station', position: { x: 8, y: 0, z: 25 }, status: 'error', power: 0, efficiency: 0 },
  { id: '14', name: '充电桩6', type: 'charging_station', position: { x: 12, y: 0, z: 25 }, status: 'charging', power: 90, efficiency: 96 }
]

const devices = ref<DeviceData[]>(props.devices || defaultDevices)

// 状态颜色映射
const statusColors = {
  normal: 0x00ff00,
  warning: 0xffaa00,
  error: 0xff0000,
  offline: 0x666666,
  charging: 0x00bfff,  // 充电中 - 深天蓝色
  idle: 0x90ee90       // 空闲 - 浅绿色
}

// 设备类型颜色
const deviceTypeColors = {
  generator: 0x4169e1,
  transformer: 0x8a2be2,
  solar: 0xffd700,
  wind: 0x00ced1,
  battery: 0x32cd32,
  building: 0x708090,
  charging_station: 0xff6347  // 充电桩 - 番茄红色
}

const initThreeJS = () => {
  if (!container.value) return

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x87CEEB) // 天空蓝色背景
  scene.fog = new THREE.Fog(0x87CEEB, 50, 200)

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    container.value.clientWidth / container.value.clientHeight,
    0.1,
    1000
  )
  camera.position.set(50, 30, 50)
  camera.lookAt(0, 0, 0)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: true, 
    alpha: true 
  })
  renderer.setSize(container.value.clientWidth, container.value.clientHeight)
  renderer.setPixelRatio(window.devicePixelRatio)
  
  // 启用阴影
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.shadowMap.autoUpdate = true
  
  // 启用色调映射以获得更好的视觉效果
  renderer.toneMapping = THREE.ACESFilmicToneMapping
  renderer.toneMappingExposure = 1.2
  
  renderer.setClearColor(0x000000, 0)
  container.value.appendChild(renderer.domElement)

  // 创建轨道控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true // 启用阻尼（惯性）
  controls.dampingFactor = 0.05 // 阻尼系数
  controls.screenSpacePanning = false // 禁用屏幕空间平移
  controls.enableZoom = true
  controls.enablePan = true
  controls.minDistance = 20 // 最小缩放距离
  controls.maxDistance = 200 // 最大缩放距离
  controls.maxPolarAngle = Math.PI / 2.2 // 限制垂直旋转角度
  controls.autoRotate = props.autoRotate || false // 自动旋转
  controls.autoRotateSpeed = 2.0 // 自动旋转速度

  // 添加光源
  addLights()

  // 创建地面
  createGround()

  // 创建设备
  createDevices()
  
  // 创建指南针
  createCompass()
  
  // 添加环境效果
  addEnvironmentEffects()
  
  // 确保动画循环启动
  if (!animationId) {
    animate()
  }
}

const addLights = () => {
  // 环境光 - 柔和的全局照明
  const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
  scene.add(ambientLight)

  // 主光源 - 太阳光
  const sunLight = new THREE.DirectionalLight(0xffffff, 1.2)
  sunLight.position.set(50, 80, 30)
  sunLight.castShadow = true
  sunLight.shadow.mapSize.width = 4096
  sunLight.shadow.mapSize.height = 4096
  sunLight.shadow.camera.near = 0.5
  sunLight.shadow.camera.far = 200
  sunLight.shadow.camera.left = -100
  sunLight.shadow.camera.right = 100
  sunLight.shadow.camera.top = 100
  sunLight.shadow.camera.bottom = -100
  sunLight.shadow.bias = -0.0001
  scene.add(sunLight)

  // 天空光 - 模拟天空散射
  const skyLight = new THREE.HemisphereLight(0x87CEEB, 0x362d1d, 0.6)
  skyLight.position.set(0, 50, 0)
  scene.add(skyLight)

  // 补充光源 - 填充阴影
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.3)
  fillLight.position.set(-30, 20, -30)
  scene.add(fillLight)

  // 设备状态光源（动态）
  devices.value.forEach(device => {
    if (device.status === 'normal' || device.status === 'warning' || device.status === 'charging') {
      const pointLight = new THREE.PointLight(statusColors[device.status], 0.8, 15)
      pointLight.position.set(device.position.x, device.position.y + 8, device.position.z)
      pointLight.castShadow = true
      pointLight.shadow.mapSize.width = 512
      pointLight.shadow.mapSize.height = 512
      pointLight.userData.isDeviceLight = true
      pointLight.userData.deviceId = device.id
      scene.add(pointLight)
    }
  })
}

const createGround = () => {
  // 创建主地面（更真实的材质）
  const groundGeometry = new THREE.PlaneGeometry(200, 200, 50, 50)
  const groundMaterial = new THREE.MeshLambertMaterial({ 
    color: 0x3a5f3a,
    transparent: true,
    opacity: 0.8
  })
  
  // 添加地面纹理变化
  const vertices = groundGeometry.attributes.position.array
  for (let i = 0; i < vertices.length; i += 3) {
    vertices[i + 2] += (Math.random() - 0.5) * 0.5 // 轻微的高度变化
  }
  groundGeometry.attributes.position.needsUpdate = true
  groundGeometry.computeVertexNormals()
  
  const ground = new THREE.Mesh(groundGeometry, groundMaterial)
  ground.rotation.x = -Math.PI / 2
  ground.receiveShadow = true
  scene.add(ground)

  // 创建网格线（更精细）
  const gridHelper = new THREE.GridHelper(200, 40, 0x666666, 0x888888)
  gridHelper.material.opacity = 0.3
  gridHelper.material.transparent = true
  scene.add(gridHelper)

  // 创建充电区域标识（更明显）
  const chargingAreaGeometry = new THREE.PlaneGeometry(60, 20)
  const chargingAreaMaterial = new THREE.MeshBasicMaterial({
    color: 0x00ff00,
    transparent: true,
    opacity: 0.15,
    side: THREE.DoubleSide
  })
  const chargingArea = new THREE.Mesh(chargingAreaGeometry, chargingAreaMaterial)
  chargingArea.rotation.x = -Math.PI / 2
  chargingArea.position.set(0, 0.01, 25)
  scene.add(chargingArea)

  // 添加充电区域边框
  const borderGeometry = new THREE.EdgesGeometry(chargingAreaGeometry)
  const borderMaterial = new THREE.LineBasicMaterial({ 
    color: 0x00ff00, 
    linewidth: 2,
    transparent: true,
    opacity: 0.8
  })
  const chargingBorder = new THREE.LineSegments(borderGeometry, borderMaterial)
  chargingBorder.rotation.x = -Math.PI / 2
  chargingBorder.position.set(0, 0.02, 25)
  scene.add(chargingBorder)

  // 添加充电桩停车位标识
  for (let i = 0; i < 6; i++) {
    const parkingSpotGeometry = new THREE.PlaneGeometry(4, 8)
    const parkingSpotMaterial = new THREE.MeshBasicMaterial({
      color: 0x0066ff,
      transparent: true,
      opacity: 0.1,
      side: THREE.DoubleSide
    })
    const parkingSpot = new THREE.Mesh(parkingSpotGeometry, parkingSpotMaterial)
    parkingSpot.rotation.x = -Math.PI / 2
    parkingSpot.position.set(
      (i % 3 - 1) * 10,
      0.005,
      25 + Math.floor(i / 3) * 10 - 5
    )
    scene.add(parkingSpot)

    // 停车位边框
    const spotBorderGeometry = new THREE.EdgesGeometry(parkingSpotGeometry)
    const spotBorderMaterial = new THREE.LineBasicMaterial({ 
      color: 0x0066ff, 
      linewidth: 1,
      transparent: true,
      opacity: 0.6
    })
    const spotBorder = new THREE.LineSegments(spotBorderGeometry, spotBorderMaterial)
    spotBorder.rotation.x = -Math.PI / 2
    spotBorder.position.set(
      (i % 3 - 1) * 10,
      0.006,
      25 + Math.floor(i / 3) * 10 - 5
    )
    scene.add(spotBorder)
  }
}

// 创建设备
const createDevices = () => {
  if (!devices.value || devices.value.length === 0) {
    console.warn('没有设备数据')
    return
  }

  devices.value.forEach(device => {
    const deviceMesh = createDeviceMesh(device)
    if (deviceMesh) {
      deviceMesh.position.set(device.position.x, device.position.y, device.position.z)
      deviceMesh.userData.isDevice = true
      deviceMesh.userData.deviceType = device.type
      deviceMesh.userData.deviceStatus = device.status
      deviceMesh.userData.deviceInfo = device
      scene.add(deviceMesh)
      
      // 添加设备标签
      createDeviceLabel(device)
    }
  })
}

// 创建方向指南针
const createCompass = () => {
  compass = new THREE.Group()
  
  // 指南针底座
  const baseGeometry = new THREE.CylinderGeometry(3, 3, 0.5, 32)
  const baseMaterial = new THREE.MeshPhongMaterial({ 
    color: 0x2c2c2c,
    shininess: 50
  })
  const base = new THREE.Mesh(baseGeometry, baseMaterial)
  base.position.y = 0.25
  base.castShadow = true
  base.receiveShadow = true
  compass.add(base)

  // 指南针表盘
  const dialGeometry = new THREE.CylinderGeometry(2.8, 2.8, 0.1, 32)
  const dialMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xf8f8f8,
    shininess: 80
  })
  const dial = new THREE.Mesh(dialGeometry, dialMaterial)
  dial.position.y = 0.55
  dial.castShadow = true
  compass.add(dial)

  // 方向标记 (N, E, S, W)
  const directions = [
    { text: 'N', angle: 0, color: 0xff0000 },      // 北 - 红色
    { text: 'E', angle: Math.PI / 2, color: 0x000000 },   // 东 - 黑色
    { text: 'S', angle: Math.PI, color: 0x000000 },       // 南 - 黑色
    { text: 'W', angle: -Math.PI / 2, color: 0x000000 }   // 西 - 黑色
  ]

  directions.forEach(dir => {
    // 方向标记线
    const markGeometry = new THREE.BoxGeometry(0.05, 0.8, 0.02)
    const markMaterial = new THREE.MeshPhongMaterial({ color: dir.color })
    const mark = new THREE.Mesh(markGeometry, markMaterial)
    mark.position.set(
      Math.sin(dir.angle) * 2.2,
      0.6,
      Math.cos(dir.angle) * 2.2
    )
    mark.rotation.y = dir.angle
    compass.add(mark)

    // 方向字母（使用简单的几何体表示）
    const letterGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.05)
    const letterMaterial = new THREE.MeshPhongMaterial({ 
      color: dir.color,
      emissive: dir.color,
      emissiveIntensity: dir.text === 'N' ? 0.3 : 0.1
    })
    const letter = new THREE.Mesh(letterGeometry, letterMaterial)
    letter.position.set(
      Math.sin(dir.angle) * 2.6,
      0.8,
      Math.cos(dir.angle) * 2.6
    )
    compass.add(letter)
  })

  // 指南针指针（指向北方）
  const needleGroup = new THREE.Group()
  
  // 指针主体 - 北端（红色）
  const northNeedleGeometry = new THREE.ConeGeometry(0.1, 1.5, 8)
  const northNeedleMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xff0000,
    emissive: 0xff0000,
    emissiveIntensity: 0.3
  })
  const northNeedle = new THREE.Mesh(northNeedleGeometry, northNeedleMaterial)
  northNeedle.position.set(0, 0.75, 0.75)
  northNeedle.rotation.x = Math.PI
  needleGroup.add(northNeedle)

  // 指针主体 - 南端（白色）
  const southNeedleGeometry = new THREE.ConeGeometry(0.08, 1.2, 8)
  const southNeedleMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xffffff,
    shininess: 100
  })
  const southNeedle = new THREE.Mesh(southNeedleGeometry, southNeedleMaterial)
  southNeedle.position.set(0, 0.6, -0.6)
  needleGroup.add(southNeedle)

  // 指针中心轴
  const axisGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.3, 16)
  const axisMaterial = new THREE.MeshPhongMaterial({ 
    color: 0x333333,
    shininess: 80
  })
  const axis = new THREE.Mesh(axisGeometry, axisMaterial)
  axis.position.y = 0.75
  needleGroup.add(axis)

  // 中心装饰球
  const centerBallGeometry = new THREE.SphereGeometry(0.08, 16, 16)
  const centerBallMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xffd700,
    shininess: 100,
    emissive: 0x332200,
    emissiveIntensity: 0.2
  })
  const centerBall = new THREE.Mesh(centerBallGeometry, centerBallMaterial)
  centerBall.position.y = 0.75
  needleGroup.add(centerBall)

  needleGroup.position.y = 0
  compass.add(needleGroup)
  compassNeedle = needleGroup

  // 外圈装饰
  const rimGeometry = new THREE.TorusGeometry(2.9, 0.1, 8, 32)
  const rimMaterial = new THREE.MeshPhongMaterial({ 
    color: 0x8b7355,
    shininess: 60
  })
  const rim = new THREE.Mesh(rimGeometry, rimMaterial)
  rim.position.y = 0.55
  rim.rotation.x = Math.PI / 2
  compass.add(rim)

  // 刻度线
  for (let i = 0; i < 36; i++) {
    const angle = (i * Math.PI * 2) / 36
    const isMainTick = i % 9 === 0 // 每90度一个主刻度
    const tickLength = isMainTick ? 0.3 : 0.15
    const tickWidth = isMainTick ? 0.03 : 0.02
    
    const tickGeometry = new THREE.BoxGeometry(tickWidth, 0.02, tickLength)
    const tickMaterial = new THREE.MeshPhongMaterial({ 
      color: isMainTick ? 0x000000 : 0x666666
    })
    const tick = new THREE.Mesh(tickGeometry, tickMaterial)
    tick.position.set(
      Math.sin(angle) * (2.5 + tickLength / 2),
      0.61,
      Math.cos(angle) * (2.5 + tickLength / 2)
    )
    tick.rotation.y = angle
    compass.add(tick)
  }

  // 设置指南针位置（右下角）
  compass.position.set(70, 1, 70)
  compass.scale.set(1.5, 1.5, 1.5)
  
  scene.add(compass)
}

// 创建简单的风力发电机
const createSimpleWindTurbine = (device: DeviceData) => {
  const group = new THREE.Group()
  
  // 塔架
  const towerGeometry = new THREE.CylinderGeometry(0.3, 0.8, 18, 16)
  const towerMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xf0f0f0,
    shininess: 50
  })
  const tower = new THREE.Mesh(towerGeometry, towerMaterial)
  tower.position.y = 9
  tower.castShadow = true
  tower.receiveShadow = true
  group.add(tower)

  // 机舱
  const nacelleGeometry = new THREE.BoxGeometry(3, 1.5, 1.5)
  const nacelleMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xe0e0e0,
    shininess: 60
  })
  const nacelle = new THREE.Mesh(nacelleGeometry, nacelleMaterial)
  nacelle.position.set(0, 18, 0)
  nacelle.castShadow = true
  group.add(nacelle)

  // 轮毂
  const hubGeometry = new THREE.SphereGeometry(0.4, 16, 16)
  const hubMaterial = new THREE.MeshPhongMaterial({ 
    color: 0xd0d0d0,
    shininess: 70
  })
  const hub = new THREE.Mesh(hubGeometry, hubMaterial)
  hub.position.set(1.5, 18, 0)
  hub.castShadow = true
  hub.userData.isHub = true
  group.add(hub)

  // 叶片 - 三片叶子，简单的椭圆形状
  for (let i = 0; i < 3; i++) {
    const bladeGeometry = new THREE.BoxGeometry(0.1, 6, 0.8)
    const bladeMaterial = new THREE.MeshPhongMaterial({ 
      color: 0xfafafa,
      shininess: 80
    })
    const blade = new THREE.Mesh(bladeGeometry, bladeMaterial)
    
    // 叶片位置和旋转
    const angle = (i * Math.PI * 2) / 3
    blade.position.set(
      1.5 + Math.cos(angle) * 0.1,
      18 + Math.sin(angle) * 3,
      Math.cos(angle + Math.PI / 2) * 3
    )
    
    // 叶片朝向轮毂中心
    blade.lookAt(1.5, 18, 0)
    blade.rotateZ(Math.PI / 2)
    
    blade.castShadow = true
    blade.userData.isBlade = true
    blade.userData.bladeIndex = i
    group.add(blade)
  }

  // 状态指示灯
  const statusLightGeometry = new THREE.SphereGeometry(0.1, 12, 12)
  const statusLightMaterial = new THREE.MeshPhongMaterial({
    color: statusColors[device.status],
    emissive: statusColors[device.status],
    emissiveIntensity: 0.6
  })
  const statusLight = new THREE.Mesh(statusLightGeometry, statusLightMaterial)
  statusLight.position.set(0, 19, 0)
  statusLight.userData.isStatusLight = true
  group.add(statusLight)

  // 基础
  const foundationGeometry = new THREE.CylinderGeometry(2, 2.5, 1, 16)
  const foundationMaterial = new THREE.MeshPhongMaterial({ 
    color: 0x888888
  })
  const foundation = new THREE.Mesh(foundationGeometry, foundationMaterial)
  foundation.position.y = 0.5
  foundation.receiveShadow = true
  group.add(foundation)

  return group
}

// 创建设备网格
const createDeviceMesh = (device: DeviceData) => {
  const group = new THREE.Group()
  
  switch (device.type) {
    case 'transformer':
      // 变压器 - 更真实的设计
      // 主体
      const transformerBody = new THREE.Mesh(
        new THREE.BoxGeometry(4, 5, 3),
        new THREE.MeshPhongMaterial({ 
          color: 0x4a4a4a,
          shininess: 30,
          specular: 0x222222
        })
      )
      transformerBody.position.y = 2.5
      transformerBody.castShadow = true
      transformerBody.receiveShadow = true
      group.add(transformerBody)

      // 散热器
      for (let i = 0; i < 3; i++) {
        const radiator = new THREE.Mesh(
          new THREE.BoxGeometry(4.2, 0.3, 3.2),
          new THREE.MeshPhongMaterial({ color: 0x666666 })
        )
        radiator.position.set(0, 1 + i * 1.5, 0)
        radiator.castShadow = true
        group.add(radiator)
      }

      // 绝缘子
      for (let i = 0; i < 6; i++) {
        const insulator = new THREE.Mesh(
          new THREE.CylinderGeometry(0.1, 0.15, 1.5, 8),
          new THREE.MeshPhongMaterial({ color: 0x8B4513 })
        )
        insulator.position.set(
          (i % 3 - 1) * 1.5,
          6,
          Math.floor(i / 3) * 2 - 1
        )
        insulator.castShadow = true
        group.add(insulator)
      }

      // 状态指示灯
      const statusLight = new THREE.Mesh(
        new THREE.SphereGeometry(0.15, 8, 8),
        new THREE.MeshPhongMaterial({
          color: statusColors[device.status],
          emissive: statusColors[device.status],
          emissiveIntensity: 0.5
        })
      )
      statusLight.position.set(0, 5.5, 1.6)
      statusLight.userData.isStatusLight = true
      group.add(statusLight)
      break

    case 'generator':
      // 发电机 - 更详细的设计
      // 基座
      const genBase = new THREE.Mesh(
        new THREE.BoxGeometry(3, 0.5, 2),
        new THREE.MeshPhongMaterial({ color: 0x333333 })
      )
      genBase.position.y = 0.25
      genBase.castShadow = true
      genBase.receiveShadow = true
      group.add(genBase)

      // 主体
      const genBody = new THREE.Mesh(
        new THREE.CylinderGeometry(1.5, 1.5, 3, 16),
        new THREE.MeshPhongMaterial({ 
          color: 0x2E8B57,
          shininess: 50
        })
      )
      genBody.position.y = 2
      genBody.castShadow = true
      group.add(genBody)

      // 散热格栅
      for (let i = 0; i < 8; i++) {
        const fin = new THREE.Mesh(
          new THREE.BoxGeometry(0.05, 2.5, 0.1),
          new THREE.MeshPhongMaterial({ color: 0x1a5c3a })
        )
        const angle = (i / 8) * Math.PI * 2
        fin.position.set(
          Math.cos(angle) * 1.6,
          2,
          Math.sin(angle) * 1.6
        )
        fin.rotation.y = angle
        group.add(fin)
      }

      // 排气管
      const exhaust = new THREE.Mesh(
        new THREE.CylinderGeometry(0.2, 0.2, 1.5, 8),
        new THREE.MeshPhongMaterial({ color: 0x444444 })
      )
      exhaust.position.set(0, 4.25, 0)
      exhaust.castShadow = true
      group.add(exhaust)

      // 运行指示灯
      const runLight = new THREE.Mesh(
        new THREE.SphereGeometry(0.1, 8, 8),
        new THREE.MeshPhongMaterial({
          color: statusColors[device.status],
          emissive: statusColors[device.status],
          emissiveIntensity: 0.8
        })
      )
      runLight.position.set(0, 3.5, 1.6)
      runLight.userData.isStatusLight = true
      group.add(runLight)

      // 如果运行中，添加热气效果
      if (device.status === 'normal') {
        const heatEffect = createHeatEffect()
        heatEffect.position.set(0, 5, 0)
        heatEffect.userData.isHeatEffect = true
        group.add(heatEffect)
      }
      break

    case 'solar':
      // 太阳能板 - 更真实的设计
      // 支撑结构
      const supportFrame = new THREE.Mesh(
        new THREE.BoxGeometry(8.5, 0.2, 6.5),
        new THREE.MeshPhongMaterial({ color: 0x666666 })
      )
      supportFrame.position.y = 1.8
      supportFrame.castShadow = true
      group.add(supportFrame)

      // 太阳能电池板（多个小板组成）
      for (let x = 0; x < 4; x++) {
        for (let z = 0; z < 3; z++) {
          const panel = new THREE.Mesh(
            new THREE.BoxGeometry(1.8, 0.05, 1.8),
            new THREE.MeshPhongMaterial({ 
              color: 0x1a1a2e,
              shininess: 100,
              specular: 0x4444ff
            })
          )
          panel.position.set(
            (x - 1.5) * 2,
            2,
            (z - 1) * 2
          )
          panel.castShadow = true
          group.add(panel)

          // 电池格栅
          for (let gx = 0; gx < 6; gx++) {
            for (let gz = 0; gz < 6; gz++) {
              const cell = new THREE.Mesh(
                new THREE.BoxGeometry(0.25, 0.02, 0.25),
                new THREE.MeshPhongMaterial({ 
                  color: 0x0f0f23,
                  transparent: true,
                  opacity: 0.9
                })
              )
              cell.position.set(
                (x - 1.5) * 2 + (gx - 2.5) * 0.3,
                2.05,
                (z - 1) * 2 + (gz - 2.5) * 0.3
              )
              group.add(cell)
            }
          }
        }
      }

      // 支撑柱（更真实）
      for (let i = 0; i < 4; i++) {
        const support = new THREE.Mesh(
          new THREE.CylinderGeometry(0.15, 0.2, 3.6, 8),
          new THREE.MeshPhongMaterial({ color: 0x888888 })
        )
        support.position.set(
          (i % 2) * 7 - 3.5,
          1.8,
          Math.floor(i / 2) * 5 - 2.5
        )
        support.castShadow = true
        group.add(support)
      }

      // 如果正常工作，添加发光效果
      if (device.status === 'normal') {
        const glowEffect = createSolarGlowEffect()
        glowEffect.position.set(0, 2.1, 0)
        group.add(glowEffect)
      }
      break

    case 'wind':
      // 使用简单的风力发电机
      return createSimpleWindTurbine(device)

    case 'battery':
      // 储能电池 - 更真实的设计
      // 主容器
      const batteryContainer = new THREE.Mesh(
        new THREE.BoxGeometry(8, 4, 5),
        new THREE.MeshPhongMaterial({ 
          color: 0x2c5aa0,
          shininess: 40
        })
      )
      batteryContainer.position.y = 2
      batteryContainer.castShadow = true
      batteryContainer.receiveShadow = true
      group.add(batteryContainer)

      // 电池模块（多个小模块）
      for (let x = 0; x < 4; x++) {
        for (let z = 0; z < 2; z++) {
          const module = new THREE.Mesh(
            new THREE.BoxGeometry(1.8, 3.5, 2.2),
            new THREE.MeshPhongMaterial({ 
              color: 0x1e3d6f,
              transparent: true,
              opacity: 0.9
            })
          )
          module.position.set(
            (x - 1.5) * 2,
            2,
            (z - 0.5) * 2.4
          )
          module.castShadow = true
          group.add(module)
        }
      }

      // 冷却系统
      const coolingUnit = new THREE.Mesh(
        new THREE.BoxGeometry(8.5, 1, 5.5),
        new THREE.MeshPhongMaterial({ color: 0x666666 })
      )
      coolingUnit.position.y = 4.5
      coolingUnit.castShadow = true
      group.add(coolingUnit)

      // 散热风扇
      for (let i = 0; i < 3; i++) {
        const fan = new THREE.Mesh(
          new THREE.CylinderGeometry(0.4, 0.4, 0.1, 8),
          new THREE.MeshPhongMaterial({ color: 0x333333 })
        )
        fan.position.set((i - 1) * 2.5, 5.1, 0)
        fan.userData.isFan = true
        group.add(fan)

        // 风扇叶片
        for (let j = 0; j < 4; j++) {
          const fanBlade = new THREE.Mesh(
            new THREE.BoxGeometry(0.6, 0.05, 0.1),
            new THREE.MeshPhongMaterial({ color: 0x222222 })
          )
          fanBlade.position.set((i - 1) * 2.5, 5.15, 0)
          fanBlade.rotation.y = (j * Math.PI) / 2
          fanBlade.userData.isFanBlade = true
          fanBlade.userData.fanIndex = i
          group.add(fanBlade)
        }
      }

      // 电量指示器
      const powerIndicator = new THREE.Mesh(
        new THREE.BoxGeometry(1, 0.3, 0.1),
        new THREE.MeshPhongMaterial({
          color: statusColors[device.status],
          emissive: statusColors[device.status],
          emissiveIntensity: 0.3
        })
      )
      powerIndicator.position.set(0, 3, 2.6)
      powerIndicator.userData.isStatusLight = true
      group.add(powerIndicator)

      // 电池充放电效果
      if (device.power > 50) {
        const energyEffect = createEnergyFlowEffect()
        energyEffect.position.set(0, 2, 0)
        group.add(energyEffect)
      }
      break

    case 'building':
      // 建筑物 - 更真实的设计
      // 主体结构
      const buildingMain = new THREE.Mesh(
        new THREE.BoxGeometry(10, 18, 10),
        new THREE.MeshPhongMaterial({ 
          color: 0x8B7355,
          shininess: 10
        })
      )
      buildingMain.position.y = 9
      buildingMain.castShadow = true
      buildingMain.receiveShadow = true
      group.add(buildingMain)

      // 窗户
      for (let floor = 0; floor < 6; floor++) {
        for (let x = 0; x < 4; x++) {
          for (let z = 0; z < 4; z++) {
            if (x === 1 || x === 2 || z === 1 || z === 2) continue // 跳过中心
            
            const window = new THREE.Mesh(
              new THREE.BoxGeometry(1.2, 1.5, 0.1),
              new THREE.MeshPhongMaterial({ 
                color: Math.random() > 0.3 ? 0x87CEEB : 0x1a1a1a,
                transparent: true,
                opacity: 0.8,
                emissive: Math.random() > 0.7 ? 0xffffaa : 0x000000,
                emissiveIntensity: Math.random() > 0.7 ? 0.3 : 0
              })
            )
            
            let posX = (x - 1.5) * 2.2
            let posZ = (z - 1.5) * 2.2
            
            if (Math.abs(posX) > Math.abs(posZ)) {
              window.position.set(Math.sign(posX) * 5.1, 2 + floor * 3, posZ)
            } else {
              window.position.set(posX, 2 + floor * 3, Math.sign(posZ) * 5.1)
            }
            
            window.userData.isWindow = true
            group.add(window)
          }
        }
      }

      // 屋顶
      const roof = new THREE.Mesh(
        new THREE.BoxGeometry(11, 1, 11),
        new THREE.MeshPhongMaterial({ color: 0x654321 })
      )
      roof.position.y = 18.5
      roof.castShadow = true
      group.add(roof)

      // 入口
      const entrance = new THREE.Mesh(
        new THREE.BoxGeometry(2, 3, 0.2),
        new THREE.MeshPhongMaterial({ color: 0x8B4513 })
      )
      entrance.position.set(0, 1.5, 5.1)
      group.add(entrance)
      break

    case 'charging_station':
      // 充电桩 - 更真实的设计
      // 底座（更厚重）
      const chargingBase = new THREE.Mesh(
        new THREE.BoxGeometry(2.5, 0.8, 2.5),
        new THREE.MeshPhongMaterial({ 
          color: 0x2c2c2c,
          shininess: 20
        })
      )
      chargingBase.position.y = 0.4
      chargingBase.castShadow = true
      chargingBase.receiveShadow = true
      group.add(chargingBase)

      // 主体（圆角设计）
      const chargingMain = new THREE.Mesh(
        new THREE.CylinderGeometry(0.6, 0.8, 4.5, 12),
        new THREE.MeshPhongMaterial({ 
          color: 0xf0f0f0,
          shininess: 60
        })
      )
      chargingMain.position.y = 3.05
      chargingMain.castShadow = true
      group.add(chargingMain)

      // 显示屏
      const screen = new THREE.Mesh(
        new THREE.BoxGeometry(0.8, 1.2, 0.1),
        new THREE.MeshPhongMaterial({ 
          color: device.status === 'charging' ? 0x00ff00 : 
                device.status === 'error' ? 0xff0000 : 0x0066cc,
          emissive: device.status === 'charging' ? 0x003300 : 
                   device.status === 'error' ? 0x330000 : 0x001133,
          emissiveIntensity: 0.4
        })
      )
      screen.position.set(0, 3.5, 0.7)
      screen.userData.isScreen = true
      group.add(screen)

      // 充电枪支架
      const gunHolder = new THREE.Mesh(
        new THREE.BoxGeometry(0.3, 0.8, 0.4),
        new THREE.MeshPhongMaterial({ color: 0x666666 })
      )
      gunHolder.position.set(0.8, 2.5, 0)
      gunHolder.castShadow = true
      group.add(gunHolder)

      // 充电枪（更真实）
      const gunHandle = new THREE.Mesh(
        new THREE.CylinderGeometry(0.08, 0.12, 0.8, 8),
        new THREE.MeshPhongMaterial({ color: 0x1a1a1a })
      )
      gunHandle.position.set(1.2, 2.8, 0)
      gunHandle.rotation.z = Math.PI / 6
      gunHandle.castShadow = true
      group.add(gunHandle)

      // 充电线
      const cableGeometry = new THREE.CylinderGeometry(0.03, 0.03, 1.5, 8)
      const cable = new THREE.Mesh(cableGeometry, new THREE.MeshPhongMaterial({ color: 0x333333 }))
      cable.position.set(1.0, 2.2, 0)
      cable.rotation.z = Math.PI / 4
      group.add(cable)

      // 状态指示灯（顶部）
      const topLight = new THREE.Mesh(
        new THREE.SphereGeometry(0.15, 12, 12),
        new THREE.MeshPhongMaterial({
          color: device.status === 'charging' ? 0x00ff00 : 
                device.status === 'error' ? 0xff0000 : 
                device.status === 'idle' ? 0x0066ff : 0x666666,
          emissive: device.status === 'charging' ? 0x00ff00 : 
                   device.status === 'error' ? 0xff0000 : 
                   device.status === 'idle' ? 0x0066ff : 0x000000,
          emissiveIntensity: 0.6
        })
      )
      topLight.position.set(0, 5.5, 0)
      topLight.userData.isStatusLight = true
      group.add(topLight)

      // 充电效果
      if (device.status === 'charging') {
        const chargingEffect = createAdvancedChargingEffect()
        chargingEffect.position.set(0, 3, 0)
        chargingEffect.userData.isChargingEffect = true
        group.add(chargingEffect)
      }

      // 散热格栅
      for (let i = 0; i < 8; i++) {
        const vent = new THREE.Mesh(
          new THREE.BoxGeometry(0.05, 0.3, 0.1),
          new THREE.MeshPhongMaterial({ color: 0x444444 })
        )
        const angle = (i / 8) * Math.PI * 2
        vent.position.set(
          Math.cos(angle) * 0.7,
          4.5,
          Math.sin(angle) * 0.7
        )
        vent.rotation.y = angle
        group.add(vent)
      }
      break

    default:
      // 默认设备
      const defaultGeometry = new THREE.BoxGeometry(2, 2, 2)
      const defaultMaterial = new THREE.MeshPhongMaterial({ color: 0x888888 })
      const defaultMesh = new THREE.Mesh(defaultGeometry, defaultMaterial)
      defaultMesh.position.y = 1
      defaultMesh.castShadow = true
      defaultMesh.receiveShadow = true
      group.add(defaultMesh)
  }

  group.position.set(device.position.x, device.position.y, device.position.z)
  group.userData.deviceType = device.type
  group.userData.deviceStatus = device.status
  return group
}

const createParticleSystem = (device: DeviceData): THREE.Points | null => {
  const particleCount = 100
  const particles = new THREE.BufferGeometry()
  const positions = new Float32Array(particleCount * 3)
  const colors = new Float32Array(particleCount * 3)

  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = device.position.x + (Math.random() - 0.5) * 10
    positions[i * 3 + 1] = device.position.y + Math.random() * 15
    positions[i * 3 + 2] = device.position.z + (Math.random() - 0.5) * 10

    const color = new THREE.Color(statusColors[device.status])
    colors[i * 3] = color.r
    colors[i * 3 + 1] = color.g
    colors[i * 3 + 2] = color.b
  }

  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  particles.setAttribute('color', new THREE.BufferAttribute(colors, 3))

  const particleMaterial = new THREE.PointsMaterial({
    size: 0.5,
    vertexColors: true,
    transparent: true,
    opacity: 0.6,
    blending: THREE.AdditiveBlending
  })

  return new THREE.Points(particles, particleMaterial)
}

// 创建热气效果（发电机）
const createHeatEffect = () => {
  const particleCount = 50
  const particles = new THREE.BufferGeometry()
  const positions = new Float32Array(particleCount * 3)
  const velocities = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 0.5
    positions[i * 3 + 1] = 0
    positions[i * 3 + 2] = (Math.random() - 0.5) * 0.5
    
    velocities[i * 3] = (Math.random() - 0.5) * 0.02
    velocities[i * 3 + 1] = Math.random() * 0.05 + 0.02
    velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.02
  }
  
  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  particles.userData.velocities = velocities
  
  const material = new THREE.PointsMaterial({
    size: 0.05,
    color: 0xffffff,
    transparent: true,
    opacity: 0.3,
    blending: THREE.AdditiveBlending
  })
  
  const heatParticles = new THREE.Points(particles, material)
  heatParticles.userData.isHeatEffect = true
  return heatParticles
}

// 创建太阳能发光效果
const createSolarGlowEffect = () => {
  const glowGeometry = new THREE.PlaneGeometry(8, 6)
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: 0x4444ff,
    transparent: true,
    opacity: 0.1,
    blending: THREE.AdditiveBlending
  })
  
  const glow = new THREE.Mesh(glowGeometry, glowMaterial)
  glow.rotation.x = -Math.PI / 2
  glow.userData.isSolarGlow = true
  return glow
}

// 创建能量流动效果（电池）
const createEnergyFlowEffect = () => {
  const group = new THREE.Group()
  
  // 创建能量流线
  for (let i = 0; i < 6; i++) {
    const curve = new THREE.CatmullRomCurve3([
      new THREE.Vector3(-3, 0, -2),
      new THREE.Vector3(0, 2, 0),
      new THREE.Vector3(3, 0, 2)
    ])
    
    const geometry = new THREE.TubeGeometry(curve, 20, 0.02, 8, false)
    const material = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.6,
      emissive: 0x004444,
      emissiveIntensity: 0.5
    })
    
    const tube = new THREE.Mesh(geometry, material)
    tube.rotation.y = (i / 6) * Math.PI * 2
    tube.userData.isEnergyFlow = true
    group.add(tube)
  }
  
  return group
}

// 创建高级充电效果
const createAdvancedChargingEffect = () => {
  const group = new THREE.Group()
  
  // 充电粒子螺旋
  const particleCount = 80
  const particles = new THREE.BufferGeometry()
  const positions = new Float32Array(particleCount * 3)
  const colors = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount; i++) {
    const t = i / particleCount
    const angle = t * Math.PI * 8
    const radius = 0.5 + Math.sin(t * Math.PI * 4) * 0.2
    
    positions[i * 3] = Math.cos(angle) * radius
    positions[i * 3 + 1] = t * 4 - 2
    positions[i * 3 + 2] = Math.sin(angle) * radius
    
    const color = new THREE.Color().setHSL(0.3 + t * 0.4, 1, 0.5)
    colors[i * 3] = color.r
    colors[i * 3 + 1] = color.g
    colors[i * 3 + 2] = color.b
  }
  
  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  particles.setAttribute('color', new THREE.BufferAttribute(colors, 3))
  
  const particleMaterial = new THREE.PointsMaterial({
    size: 0.08,
    vertexColors: true,
    transparent: true,
    opacity: 0.8,
    blending: THREE.AdditiveBlending
  })
  
  const chargingParticles = new THREE.Points(particles, particleMaterial)
  chargingParticles.userData.isChargingParticles = true
  group.add(chargingParticles)
  
  // 充电环
  const ringGeometry = new THREE.TorusGeometry(1, 0.05, 8, 32)
  const ringMaterial = new THREE.MeshBasicMaterial({
    color: 0x00ff00,
    transparent: true,
    opacity: 0.7,
    emissive: 0x004400,
    emissiveIntensity: 0.8
  })
  
  const chargingRing = new THREE.Mesh(ringGeometry, ringMaterial)
  chargingRing.rotation.x = Math.PI / 2
  chargingRing.position.y = 1
  chargingRing.userData.isChargingRing = true
  group.add(chargingRing)
  
  return group
}

const createChargingEffect = (): THREE.Group => {
  const group = new THREE.Group()
  
  // 创建充电粒子效果
  const particleCount = 50
  const particles = new THREE.BufferGeometry()
  const positions = new Float32Array(particleCount * 3)
  const colors = new Float32Array(particleCount * 3)
  
  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 2
    positions[i * 3 + 1] = Math.random() * 4
    positions[i * 3 + 2] = (Math.random() - 0.5) * 2
    
    // 充电效果使用蓝色粒子
    const color = new THREE.Color(0x00bfff)
    colors[i * 3] = color.r
    colors[i * 3 + 1] = color.g
    colors[i * 3 + 2] = color.b
  }
  
  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  particles.setAttribute('color', new THREE.BufferAttribute(colors, 3))
  
  const particleMaterial = new THREE.PointsMaterial({
    size: 0.3,
    vertexColors: true,
    transparent: true,
    opacity: 0.8,
    blending: THREE.AdditiveBlending
  })
  
  const chargingParticles = new THREE.Points(particles, particleMaterial)
  group.add(chargingParticles)
  
  // 添加充电环效果
  const ringGeometry = new THREE.RingGeometry(0.5, 0.7, 16)
  const ringMaterial = new THREE.MeshBasicMaterial({
    color: 0x00bfff,
    transparent: true,
    opacity: 0.3,
    side: THREE.DoubleSide
  })
  const ring = new THREE.Mesh(ringGeometry, ringMaterial)
  ring.rotation.x = -Math.PI / 2
  ring.position.y = 0.1
  group.add(ring)
  
  return group
}

const createDeviceLabel = (device: DeviceData) => {
  // 创建文字纹理
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')!
  canvas.width = 256
  canvas.height = 64
  
  context.fillStyle = 'rgba(0, 0, 0, 0.8)'
  context.fillRect(0, 0, canvas.width, canvas.height)
  
  context.fillStyle = '#ffffff'
  context.font = '16px Arial'
  context.textAlign = 'center'
  context.fillText(device.name, canvas.width / 2, 25)
  
  // 为充电桩显示特殊的状态信息
  if (device.type === 'charging_station') {
    let statusText = ''
    switch (device.status) {
      case 'charging':
        statusText = `充电中 ${device.power}%`
        break
      case 'idle':
        statusText = '空闲'
        break
      case 'error':
        statusText = '故障'
        break
      case 'offline':
        statusText = '离线'
        break
      default:
        statusText = `${device.power}% | ${device.efficiency}%`
    }
    context.fillText(statusText, canvas.width / 2, 45)
  } else {
    context.fillText(`${device.power}% | ${device.efficiency}%`, canvas.width / 2, 45)
  }

  const texture = new THREE.CanvasTexture(canvas)
  const spriteMaterial = new THREE.SpriteMaterial({ map: texture })
  const sprite = new THREE.Sprite(spriteMaterial)
  
  sprite.position.set(
    device.position.x,
    device.position.y + 10,
    device.position.z
  )
  sprite.scale.set(8, 2, 1)
  
  scene.add(sprite)
}

const addEnvironmentEffects = () => {
  // 添加星空背景
  const starGeometry = new THREE.BufferGeometry()
  const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.5 })
  
  const starVertices = []
  for (let i = 0; i < 1000; i++) {
    const x = (Math.random() - 0.5) * 2000
    const y = Math.random() * 500 + 50
    const z = (Math.random() - 0.5) * 2000
    starVertices.push(x, y, z)
  }
  
  starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3))
  const stars = new THREE.Points(starGeometry, starMaterial)
  scene.add(stars)
}

const animate = () => {
  animationId = requestAnimationFrame(animate)

  // 更新控制器
  controls.update()

  const time = Date.now() * 0.001

  // 更新指南针指针方向
  if (compass && compassNeedle) {
    // 获取相机的方位角（水平旋转角度）
    const cameraDirection = new THREE.Vector3()
    camera.getWorldDirection(cameraDirection)
    const azimuth = Math.atan2(cameraDirection.x, cameraDirection.z)
    
    // 指针始终指向北方（相对于相机视角）
    compassNeedle.rotation.y = -azimuth
  }

  // 遍历所有设备进行动画
  scene.children.forEach((child) => {
    if (child.userData.deviceType) {
      // 风力发电机叶片旋转
      if (child.userData.deviceType === 'wind') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isBlade) {
            // 简单的叶片旋转 - 绕轮毂中心旋转
            const angle = time * 2 + (subChild.userData.bladeIndex * Math.PI * 2) / 3
            subChild.position.set(
              1.5 + Math.cos(angle) * 0.1,
              18 + Math.sin(angle) * 3,
              Math.cos(angle + Math.PI / 2) * 3
            )
            subChild.lookAt(1.5, 18, 0)
            subChild.rotateZ(Math.PI / 2)
          }
          if (subChild.userData.isHub) {
            // 轮毂旋转
            subChild.rotation.x += 0.02
          }
        })
      }
      
      // 电池风扇旋转
      if (child.userData.deviceType === 'battery') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isFanBlade) {
            subChild.rotation.y += 0.1 // 风扇叶片快速旋转
          }
        })
      }
      
      // 充电桩效果动画
      if (child.userData.deviceType === 'charging_station') {
        child.children.forEach((subChild) => {
          // 充电环旋转
          if (subChild.userData.isChargingRing) {
            subChild.rotation.z += 0.05
            subChild.scale.setScalar(1 + Math.sin(time * 3) * 0.1)
          }
          
          // 充电粒子螺旋动画
          if (subChild.userData.isChargingParticles && subChild.geometry && subChild.geometry.attributes && subChild.geometry.attributes.position) {
            subChild.rotation.y += 0.02
            const positions = subChild.geometry.attributes.position.array
            if (positions && positions.length > 0) {
              for (let i = 0; i < positions.length; i += 3) {
                if (positions[i + 1] !== undefined) {
                  positions[i + 1] += 0.02 // 粒子向上移动
                  if (positions[i + 1] > 2) {
                    positions[i + 1] = -2 // 重置到底部
                  }
                }
              }
              subChild.geometry.attributes.position.needsUpdate = true
            }
          }
          
          // 显示屏闪烁效果
          if (subChild.userData.isScreen && child.userData.deviceStatus === 'charging' && subChild.material) {
            const intensity = 0.4 + Math.sin(time * 4) * 0.2
            subChild.material.emissiveIntensity = intensity
          }
          
          // 状态指示灯脉冲
          if (subChild.userData.isStatusLight && subChild.material) {
            const pulse = 0.6 + Math.sin(time * 2) * 0.3
            subChild.material.emissiveIntensity = pulse
          }
        })
      }
      
      // 发电机热气效果
      if (child.userData.deviceType === 'generator') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isHeatEffect && subChild.geometry && subChild.geometry.attributes && subChild.geometry.attributes.position) {
            const positions = subChild.geometry.attributes.position.array
            const velocities = subChild.userData.velocities
            
            if (positions && velocities && positions.length > 0 && velocities.length > 0) {
              for (let i = 0; i < positions.length; i += 3) {
                if (velocities[i] !== undefined && velocities[i + 1] !== undefined && velocities[i + 2] !== undefined) {
                  positions[i] += velocities[i]
                  positions[i + 1] += velocities[i + 1]
                  positions[i + 2] += velocities[i + 2]
                  
                  // 重置粒子位置
                  if (positions[i + 1] > 3) {
                    positions[i] = (Math.random() - 0.5) * 0.5
                    positions[i + 1] = 0
                    positions[i + 2] = (Math.random() - 0.5) * 0.5
                  }
                }
              }
              subChild.geometry.attributes.position.needsUpdate = true
            }
          }
          
          // 状态指示灯脉冲
          if (subChild.userData.isStatusLight && subChild.material) {
            const pulse = 0.8 + Math.sin(time * 3) * 0.2
            subChild.material.emissiveIntensity = pulse
          }
        })
      }
      
      // 太阳能板发光效果
      if (child.userData.deviceType === 'solar') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isSolarGlow && subChild.material) {
            const glow = 0.1 + Math.sin(time * 1.5) * 0.05
            subChild.material.opacity = glow
          }
        })
      }
      
      // 电池能量流动效果
      if (child.userData.deviceType === 'battery') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isEnergyFlow && subChild.material) {
            subChild.rotation.y += 0.01
            const intensity = 0.5 + Math.sin(time * 2) * 0.3
            subChild.material.emissiveIntensity = intensity
          }
          
          // 电量指示器脉冲
          if (subChild.userData.isStatusLight && subChild.material) {
            const pulse = 0.3 + Math.sin(time * 1.5) * 0.2
            subChild.material.emissiveIntensity = pulse
          }
        })
      }
      
      // 变压器状态指示灯
      if (child.userData.deviceType === 'transformer') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isStatusLight && subChild.material) {
            const pulse = 0.5 + Math.sin(time * 2.5) * 0.3
            subChild.material.emissiveIntensity = pulse
          }
        })
      }
      
      // 建筑物窗户闪烁
      if (child.userData.deviceType === 'building') {
        child.children.forEach((subChild) => {
          if (subChild.userData.isWindow && subChild.material && Math.random() > 0.995) {
            // 随机窗户灯光变化
            const isLit = Math.random() > 0.5
            subChild.material.color.setHex(isLit ? 0xffffaa : 0x1a1a1a)
            subChild.material.emissive.setHex(isLit ? 0xffffaa : 0x000000)
            subChild.material.emissiveIntensity = isLit ? 0.3 : 0
          }
        })
      }
    }
  })

  // 动画粒子系统
  particleSystems.forEach(particles => {
    if (particles && particles.geometry && particles.geometry.attributes && particles.geometry.attributes.position) {
      const positions = particles.geometry.attributes.position.array as Float32Array
      if (positions && positions.length > 0) {
        for (let i = 1; i < positions.length; i += 3) {
          if (positions[i] !== undefined) {
            positions[i] += 0.1 // Y轴向上移动
            if (positions[i] > 20) {
              positions[i] = 0 // 重置到底部
            }
          }
        }
        particles.geometry.attributes.position.needsUpdate = true
      }
    }
  })

  renderer.render(scene, camera)
}

const handleResize = () => {
  if (!container.value) return
  
  camera.aspect = container.value.clientWidth / container.value.clientHeight
  camera.updateProjectionMatrix()
  renderer.setSize(container.value.clientWidth, container.value.clientHeight)
}

// 监听设备数据变化
watch(() => props.devices, (newDevices) => {
  if (newDevices && scene) {
    devices.value = newDevices
    
    // 清除现有设备
    const devicesToRemove = []
    scene.children.forEach(child => {
      if (child.userData.deviceType) {
        devicesToRemove.push(child)
      }
    })
    devicesToRemove.forEach(device => {
      scene.remove(device)
    })
    
    // 清除缓存
    deviceMeshes.clear()
    particleSystems.clear()
    
    // 重新创建设备
    createDevices()
  }
}, { deep: true })

// 监听自动旋转属性变化
watch(() => props.autoRotate, (newAutoRotate) => {
  if (controls) {
    controls.autoRotate = newAutoRotate || false
  }
})

// 暴露控制器方法给父组件
const resetCamera = () => {
  if (controls) {
    controls.reset()
  }
}

const toggleAutoRotate = () => {
  if (controls) {
    controls.autoRotate = !controls.autoRotate
  }
}

defineExpose({
  resetCamera,
  toggleAutoRotate
})

onMounted(() => {
  initThreeJS()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('resize', handleResize)
  if (controls) {
    controls.dispose()
  }
  if (renderer) {
    renderer.dispose()
  }
})
</script>

<style scoped>
.energy-station-3d {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.energy-station-3d canvas {
  display: block;
  width: 100%;
  height: 100%;
}
</style>