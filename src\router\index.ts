import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { RouteMeta } from '@/types'

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/homepage'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      requiresLayout: false
    } as RouteMeta
  },
  {
    path: '/homepage',
    name: 'Homepage',
    component: () => import('@/views/Homepage.vue'),
    meta: {
      title: '首页',
      icon: 'House',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/data-dashboard',
    name: 'DataDashboard',
    component: () => import('@/views/DataDashboard.vue'),
    meta: {
      title: '数据大屏',
      icon: 'Monitor',
      requiresAuth: true,
      requiresLayout: false
    } as RouteMeta
  },
  {
    path: '/energy-monitoring',
    redirect: '/energy-monitoring/overview'
  },
  {
    path: '/energy-monitoring/overview',
    name: 'EnergyOverview',
    component: () => import('@/views/energy-monitoring/EnergyOverview.vue'),
    meta: {
      title: '能源总览',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-monitoring/green-power',
    name: 'GreenPowerOverview',
    component: () => import('@/views/energy-monitoring/GreenPowerOverview.vue'),
    meta: {
      title: '绿电总览',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-monitoring/generation',
    name: 'GenerationManagement',
    component: () => import('@/views/energy-monitoring/GenerationManagement.vue'),
    meta: {
      title: '发电管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-monitoring/storage',
    name: 'StorageManagement',
    component: () => import('@/views/energy-monitoring/StorageManagement.vue'),
    meta: {
      title: '储能管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-monitoring/charging',
    name: 'ChargingManagement',
    component: () => import('@/views/energy-monitoring/ChargingManagement.vue'),
    meta: {
      title: '充电桩管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis',
    redirect: '/energy-analysis/topology'
  },
  {
    path: '/energy-analysis/topology',
    name: 'TopologyView',
    component: () => import('@/views/energy-analysis/TopologyView.vue'),
    meta: {
      title: '拓扑视图',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis/trend',
    name: 'EnergyTrend',
    component: () => import('@/views/energy-analysis/EnergyTrend.vue'),
    meta: {
      title: '能耗趋势',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis/comparison',
    name: 'EnergyComparison',
    component: () => import('@/views/energy-analysis/EnergyComparison.vue'),
    meta: {
      title: '能耗对比',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis/reports',
    name: 'EnergyReports',
    component: () => import('@/views/energy-analysis/ReportManagement.vue'),
    meta: {
      title: '报告管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis/exceptions',
    name: 'EnergyExceptions',
    component: () => import('@/views/energy-analysis/ExceptionRecords.vue'),
    meta: {
      title: '异常记录',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/energy-analysis/rules',
    name: 'EnergyRules',
    component: () => import('@/views/energy-analysis/ExceptionRules.vue'),
    meta: {
      title: '异常规则',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/device-management',
    redirect: '/device-management/list'
  },
  {
    path: '/device-management/list',
    name: 'DeviceList',
    component: () => import('@/views/device-management/DeviceList.vue'),
    meta: {
      title: '设备列表',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/device-management/maintenance',
    name: 'DeviceMaintenance',
    component: () => import('@/views/device-management/DeviceMaintenance.vue'),
    meta: {
      title: '设备维护',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/device-management/units',
    name: 'DeviceUnits',
    component: () => import('@/views/device-management/UnitManagement.vue'),
    meta: {
      title: '单元管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/device-management/safety',
    name: 'DeviceSafety',
    component: () => import('@/views/device-management/SafetyMonitoring.vue'),
    meta: {
      title: '安全监测',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/device-management/protection',
    name: 'DeviceProtection',
    component: () => import('@/views/device-management/SafetyProtection.vue'),
    meta: {
      title: '安全保护',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/intelligent-control',
    redirect: '/intelligent-control/strategy'
  },
  {
    path: '/intelligent-control/strategy',
    name: 'StrategyManagement',
    component: () => import('@/views/intelligent-control/StrategyManagement.vue'),
    meta: {
      title: '策略管理',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/intelligent-control/linkage',
    name: 'SystemLinkage',
    component: () => import('@/views/intelligent-control/SystemLinkage.vue'),
    meta: {
      title: '多系统联动',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/intelligent-control/prediction',
    name: 'LoadPrediction',
    component: () => import('@/views/intelligent-control/LoadPrediction.vue'),
    meta: {
      title: '负荷预测',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/intelligent-control/fault-prediction',
    name: 'FaultPrediction',
    component: () => import('@/views/intelligent-control/FaultPrediction.vue'),
    meta: {
      title: '故障预测',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    redirect: '/settings/basic',
    meta: {
      title: '系统设置',
      icon: 'Setting',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta,
    children: [
      {
        path: 'basic',
        name: 'BasicSettings',
        component: () => import('@/views/settings/BasicSettings.vue'),
        meta: {
          title: '基础设置',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'alert',
        name: 'AlertSettings',
        component: () => import('@/views/settings/AlertSettings.vue'),
        meta: {
          title: '告警设置',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'data',
        name: 'DataSettings',
        component: () => import('@/views/settings/DataSettings.vue'),
        meta: {
          title: '数据设置',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'user',
        name: 'UserManagement',
        component: () => import('@/views/settings/UserManagement.vue'),
        meta: {
          title: '用户管理',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'platform-integration',
        name: 'PlatformIntegration',
        component: () => import('@/views/settings/PlatformIntegration.vue'),
        meta: {
          title: '平台对接',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'one-click-report',
        name: 'OneClickReport',
        component: () => import('@/views/settings/OneClickReport.vue'),
        meta: {
          title: '一键报送',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      },
      {
        path: 'about',
        name: 'AboutSystem',
        component: () => import('@/views/settings/AboutSystem.vue'),
        meta: {
          title: '关于系统',
          requiresAuth: true,
          requiresLayout: true
        } as RouteMeta
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: {
      title: '个人信息',
      requiresAuth: true,
      requiresLayout: true
    } as RouteMeta
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '404',
      requiresLayout: false
    } as RouteMeta
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  const requiresAuth = to.meta.requiresAuth as boolean
  
  // 设置页面标题
  document.title = `${to.meta.title} - 能源管理系统`
  
  // 如果需要认证且未登录，则重定向到登录页
  if (requiresAuth && !authStore.isLoggedIn) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
  } else {
    next()
  }
})

export default router