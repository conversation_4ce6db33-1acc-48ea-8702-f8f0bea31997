#!/bin/bash

# 能源管理系统启动脚本
# 使用方法: ./start.sh [dev|prod|stop|restart|status]

APP_NAME="energy-management"
LOG_DIR="./logs"

# 创建日志目录
mkdir -p $LOG_DIR

case "$1" in
  "dev")
    echo "启动开发环境..."
    if command -v pm2 &> /dev/null; then
      pm2 start ecosystem.config.js --env development
    else
      echo "PM2 未安装，使用 nohup 启动..."
      nohup npm run dev > $LOG_DIR/app.log 2>&1 &
      echo "应用已在后台启动，日志文件: $LOG_DIR/app.log"
    fi
    ;;
  "prod")
    echo "构建并启动生产环境..."
    npm run build
    if command -v pm2 &> /dev/null; then
      pm2 start ecosystem.config.js --env production
    else
      echo "PM2 未安装，请先安装: npm install -g pm2"
      exit 1
    fi
    ;;
  "stop")
    echo "停止应用..."
    if command -v pm2 &> /dev/null; then
      pm2 stop $APP_NAME
    else
      pkill -f "npm run dev"
    fi
    ;;
  "restart")
    echo "重启应用..."
    if command -v pm2 &> /dev/null; then
      pm2 restart $APP_NAME
    else
      pkill -f "npm run dev"
      sleep 2
      nohup npm run dev > $LOG_DIR/app.log 2>&1 &
    fi
    ;;
  "status")
    echo "查看应用状态..."
    if command -v pm2 &> /dev/null; then
      pm2 status
    else
      ps aux | grep "npm run dev" | grep -v grep
    fi
    ;;
  "logs")
    echo "查看日志..."
    if command -v pm2 &> /dev/null; then
      pm2 logs $APP_NAME
    else
      tail -f $LOG_DIR/app.log
    fi
    ;;
  *)
    echo "使用方法: $0 {dev|prod|stop|restart|status|logs}"
    echo "  dev     - 启动开发环境"
    echo "  prod    - 构建并启动生产环境"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  status  - 查看应用状态"
    echo "  logs    - 查看应用日志"
    exit 1
    ;;
esac
