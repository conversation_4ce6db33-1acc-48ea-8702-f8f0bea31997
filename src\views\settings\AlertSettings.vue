<template>
  <div class="settings-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>告警设置</h3>
        </div>
      </template>
      
      <el-form :model="alertSettings" label-width="180px">
        <el-form-item label="启用告警通知">
          <el-switch v-model="alertSettings.enableNotification" />
        </el-form-item>
        
        <el-form-item label="告警通知方式">
          <el-checkbox-group v-model="alertSettings.notificationMethods">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="wechat">微信</el-checkbox>
            <el-checkbox label="app">App推送</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="温度告警阈值 (°C)">
          <el-input-number v-model="alertSettings.temperatureThreshold" :min="0" :max="100" />
        </el-form-item>
        
        <el-form-item label="功率告警阈值 (kW)">
          <el-input-number v-model="alertSettings.powerThreshold" :min="0" :precision="2" :step="0.1" />
        </el-form-item>
        
        <el-form-item label="离线设备告警延迟 (分钟)">
          <el-input-number v-model="alertSettings.offlineDelay" :min="1" :max="60" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveAlertSettings">保存设置</el-button>
          <el-button @click="resetAlertSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '系统设置' },
  { text: '告警设置' }
])

// 告警设置
const alertSettings = reactive({
  enableNotification: true,
  notificationMethods: ['email', 'app'],
  temperatureThreshold: 30,
  powerThreshold: 50.5,
  offlineDelay: 5
})

// 保存告警设置
const saveAlertSettings = () => {
  ElMessage.success('告警设置保存成功')
}

// 重置告警设置
const resetAlertSettings = () => {
  alertSettings.enableNotification = true
  alertSettings.notificationMethods = ['email', 'app']
  alertSettings.temperatureThreshold = 30
  alertSettings.powerThreshold = 50.5
  alertSettings.offlineDelay = 5
  ElMessage.info('告警设置已重置')
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  width: 100%;
}

.settings-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}
</style> 