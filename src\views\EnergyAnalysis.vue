<template>
  <div class="energy-analysis">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="page-card">
          <template #header>
            <div class="card-header">
              <h3>能耗分析平台</h3>
            </div>
          </template>
          <div class="page-content">
            <el-empty description="功能开发中，敬请期待">
              <template #image>
                <el-icon size="60"><component :is="'TrendCharts'" /></el-icon>
              </template>
              <div class="empty-text">
                <p>能耗分析平台将整合以下功能：</p>
                <ul>
                  <li>列表视图</li>
                  <li>能耗对比</li>
                  <li>报告管理</li>
                  <li>异常记录</li>
                  <li>异常规则</li>
                </ul>
                <p>聚焦数据分析与异常处理</p>
              </div>
            </el-empty>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
// 删除对TrendCharts的直接import
// import { TrendCharts } from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '能耗分析平台' }
])
</script>

<style scoped>
.energy-analysis {
  padding: 0;
}

.page-card {
  min-height: 500px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-text {
  margin-top: 20px;
  text-align: left;
}

.empty-text p {
  margin: 10px 0;
  color: #606266;
}

.empty-text ul {
  margin: 15px 0;
  padding-left: 20px;
}

.empty-text li {
  margin: 5px 0;
  color: #909399;
}
</style> 