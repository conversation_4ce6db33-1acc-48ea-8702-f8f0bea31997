<template>
  <div class="green-power-overview">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 绿电总览统计 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <h3>绿电总览统计</h3>
          <div class="header-controls">
          <el-date-picker
              v-model="selectedDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            size="small"
              @change="handleDateRangeChange"
          />
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="green-power-stats">
        <div class="power-item" v-for="(item, index) in greenPowerStats" :key="index">
          <div class="power-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="power-info">
            <div class="power-name">{{ item.name }}</div>
            <div class="power-value-container">
              <span class="power-value">{{ item.value }}</span>
              <span class="power-unit">{{ item.unit }}</span>
            </div>
            <div class="power-trend" :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
              {{ item.comparePeriod }}{{ item.trend === 'up' ? '增长' : '下降' }} {{ item.trend === 'up' ? '+' : '-' }}{{ item.changeRate }}%
            </div>
          </div>
          <div class="power-divider" v-if="index < greenPowerStats.length - 1"></div>
        </div>
      </div>
    </el-card>

    <!-- 绿电发电量与碳减排 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>绿电发电量趋势</h3>
          </template>
          <div class="generation-chart" ref="generationChart"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>碳减排统计</h3>
          </template>
          <div class="carbon-chart" ref="carbonChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 绿电设备状态与效率 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="16">
        <el-card>
          <template #header>
            <h3>绿电设备状态监控</h3>
          </template>
          <div class="device-status-chart" ref="deviceStatusChart"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <h3>发电效率分析</h3>
          </template>
          <div class="efficiency-chart" ref="efficiencyChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 绿电收益分析 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <h3>绿电收益分析</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="revenue-item">
            <div class="revenue-icon">
              <el-icon size="40" color="#67c23a"><Money /></el-icon>
            </div>
            <div class="revenue-info">
              <div class="revenue-title">今日收益</div>
              <div class="revenue-value">¥ 1,234.56</div>
              <div class="revenue-desc">较昨日增长 +5.2%</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="revenue-item">
            <div class="revenue-icon">
              <el-icon size="40" color="#409eff"><TrendCharts /></el-icon>
            </div>
            <div class="revenue-info">
              <div class="revenue-title">本月收益</div>
              <div class="revenue-value">¥ 35,678.90</div>
              <div class="revenue-desc">较上月增长 +8.7%</div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="8">
          <div class="revenue-item">
            <div class="revenue-icon">
              <el-icon size="40" color="#e6a23c"><Trophy /></el-icon>
            </div>
            <div class="revenue-info">
              <div class="revenue-title">年度收益</div>
              <div class="revenue-value">¥ 456,789.12</div>
              <div class="revenue-desc">预计年底达成目标</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 绿电设备详情表格 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>绿电设备详情</h3>
          <el-button type="primary" size="small" @click="refreshDeviceData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="deviceData" 
        style="width: 100%" 
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="deviceName" label="设备名称" align="center" />
        <el-table-column prop="deviceType" label="设备类型" align="center">
          <template #default="scope">
            <el-tag :type="getDeviceTypeColor(scope.row.deviceType)">
              {{ scope.row.deviceType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="currentPower" label="当前功率" align="center">
          <template #default="scope">
            <span>{{ scope.row.currentPower }} kW</span>
          </template>
        </el-table-column>
        <el-table-column prop="todayGeneration" label="今日发电量" align="center">
          <template #default="scope">
            <span>{{ scope.row.todayGeneration }} kWh</span>
          </template>
        </el-table-column>
        <el-table-column prop="monthGeneration" label="本月发电量" align="center">
          <template #default="scope">
            <span>{{ scope.row.monthGeneration }} kWh</span>
          </template>
        </el-table-column>
        <el-table-column prop="carbonReduction" label="碳减排量" align="center">
          <template #default="scope">
            <span>{{ scope.row.carbonReduction }} kg</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="运行状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="efficiency" label="发电效率" align="center">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.efficiency" 
              :color="getEfficiencyColor(scope.row.efficiency)"
              :stroke-width="8"
              style="width: 90%"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import { Download } from '@element-plus/icons-vue'
import { 
  Sunny, 
  Lightning, 
  ArrowUp,
  ArrowDown,
  Refresh,
  Money,
  TrendCharts,
  Trophy,
  Cpu
} from '@element-plus/icons-vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能源监控中心', to: '/energy-monitoring' },
  { text: '绿电总览', to: '' }
])

// 初始化日期范围为过去7天
const today = new Date()
const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
const selectedDateRange = ref<[Date, Date]>([weekAgo, today])

// 绿电统计数据
const greenPowerStats = ref([
  {
    name: '总发电量',
    value: '2,456.8',
    unit: 'kWh',
    color: '#67c23a',
    icon: Lightning,
    trend: 'up',
    changeRate: '8.5',
    comparePeriod: '较昨日'
  },
  {
    name: '光伏发电',
    value: '1,678.9',
    unit: 'kWh',
    color: '#e6a23c',
    icon: Sunny,
    trend: 'up',
    changeRate: '12.3',
    comparePeriod: '较昨日'
  },
  {
    name: '风力发电',
    value: '777.9',
    unit: 'kWh',
    color: '#409eff',
    icon: Cpu,
    trend: 'up',
    changeRate: '4.2',
    comparePeriod: '较昨日'
  },
  {
    name: '碳减排量',
    value: '1,234.5',
    unit: 'kg',
    color: '#909399',
    icon: Trophy,
    trend: 'up',
    changeRate: '6.8',
    comparePeriod: '较昨日'
  }
])

// 设备数据
const deviceData = ref([
  {
    deviceName: '光伏板组1',
    deviceType: '光伏板',
    currentPower: '125.6',
    todayGeneration: '856.7',
    monthGeneration: '25,678.9',
    carbonReduction: '428.4',
    status: '正常',
    efficiency: 88
  },
  {
    deviceName: '光伏板组2',
    deviceType: '光伏板',
    currentPower: '118.3',
    todayGeneration: '822.2',
    monthGeneration: '24,123.5',
    carbonReduction: '411.2',
    status: '正常',
    efficiency: 85
  },
  {
    deviceName: '风力发电机1',
    deviceType: '风力发电',
    currentPower: '245.8',
    todayGeneration: '388.9',
    monthGeneration: '11,567.3',
    carbonReduction: '194.8',
    status: '正常',
    efficiency: 92
  },
  {
    deviceName: '风力发电机2',
    deviceType: '风力发电',
    currentPower: '238.7',
    todayGeneration: '389.0',
    monthGeneration: '11,234.8',
    carbonReduction: '200.1',
    status: '正常',
    efficiency: 89
  }
])

const generationChart = ref()
const carbonChart = ref()
const deviceStatusChart = ref()
const efficiencyChart = ref()

// 获取设备类型颜色
const getDeviceTypeColor = (type: string) => {
  switch (type) {
    case '光伏板': return 'success'
    case '风力发电': return 'primary'
    case '水力发电': return 'info'
    default: return 'default'
  }
}

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 80) return '#67c23a'
  if (efficiency >= 60) return '#409eff'
  if (efficiency >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 初始化发电量趋势图表
const initGenerationChart = () => {
  const chart = echarts.init(generationChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['光伏发电', '风力发电', '总发电量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00']
    },
    yAxis: {
      type: 'value',
      name: '发电量(kWh)'
    },
    series: [
      {
        name: '光伏发电',
        type: 'line',
        data: [50, 120, 200, 280, 320, 250, 180],
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '风力发电',
        type: 'line',
        data: [80, 85, 90, 95, 88, 92, 85],
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#409eff' }
      },
      {
        name: '总发电量',
        type: 'line',
        data: [130, 205, 290, 375, 408, 342, 265],
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化碳减排图表
const initCarbonChart = () => {
  const chart = echarts.init(carbonChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} kg ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['光伏减排', '风力减排']
    },
    series: [
      {
        name: '碳减排来源',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 839.6, name: '光伏减排', itemStyle: { color: '#e6a23c' } },
          { value: 394.9, name: '风力减排', itemStyle: { color: '#409eff' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化设备状态图表
const initDeviceStatusChart = () => {
  const chart = echarts.init(deviceStatusChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['当前功率', '今日发电量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['光伏板组1', '光伏板组2', '风力发电机1', '风力发电机2']
    },
    yAxis: [
      {
        type: 'value',
        name: '功率(kW)',
        position: 'left'
      },
      {
        type: 'value',
        name: '发电量(kWh)',
        position: 'right'
      }
    ],
    series: [
      {
        name: '当前功率',
        type: 'bar',
        data: [125.6, 118.3, 245.8, 238.7],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '今日发电量',
        type: 'line',
        yAxisIndex: 1,
        data: [856.7, 822.2, 388.9, 389.0],
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化效率图表
const initEfficiencyChart = () => {
  const chart = echarts.init(efficiencyChart.value)
  const option = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    },
    series: [
      {
        name: '平均效率',
        type: 'gauge',
        detail: {
          formatter: '{value}%'
        },
        data: [{ value: 88.5, name: '发电效率' }],
        axisLine: {
          lineStyle: {
            width: 20,
            color: [
              [0.4, '#f56c6c'],
              [0.6, '#e6a23c'],
              [0.8, '#409eff'],
              [1, '#67c23a']
            ]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 处理日期范围变化
const handleDateRangeChange = async (dates: [Date, Date]) => {
  if (dates && dates.length === 2) {
    selectedDateRange.value = dates
    console.log('日期范围变化:', dates)
    
    // 根据日期范围重新生成数据
    await refreshAllData()
  }
}

// 根据日期范围生成数据
const generateDataByDateRange = (startDate: Date, endDate: Date) => {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const multiplier = Math.max(1, daysDiff / 7) // 基于天数差异调整数据量
  
  // 生成绿电统计数据
  const baseStats = [
    { name: '总发电量', baseValue: 2456.8, unit: 'kWh', color: '#67c23a', icon: Lightning },
    { name: '光伏发电', baseValue: 1678.9, unit: 'kWh', color: '#e6a23c', icon: Sunny },
    { name: '风力发电', baseValue: 777.9, unit: 'kWh', color: '#409eff', icon: Cpu },
    { name: '碳减排量', baseValue: 1234.5, unit: 'kg', color: '#909399', icon: Trophy }
  ]
  
  return baseStats.map(stat => ({
    ...stat,
    value: (stat.baseValue * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    trend: Math.random() > 0.5 ? 'up' : 'down',
    changeRate: (Math.random() * 15).toFixed(1),
    comparePeriod: daysDiff <= 1 ? '较昨日' : daysDiff <= 7 ? '较上周' : '较上月'
  }))
}

// 刷新所有数据
const refreshAllData = async () => {
  const [startDate, endDate] = selectedDateRange.value
  
  // 更新统计数据
  greenPowerStats.value = generateDataByDateRange(startDate, endDate)
  
  // 重新初始化所有图表
  await nextTick(() => {
    initGenerationChart()
    initCarbonChart()
    initDeviceStatusChart()
    initEfficiencyChart()
  })
  
  console.log('数据刷新完成')
}

// 刷新设备数据
const refreshDeviceData = () => {
  console.log('刷新设备数据')
  // 这里可以重新加载设备数据
}

// 导出数据
const exportData = () => {
  const [startDate, endDate] = selectedDateRange.value
  const dateRange = `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
  
  // 创建CSV内容
  let csvContent = "数据类型,数值,单位,变化趋势,变化率,对比周期\n"
  
  greenPowerStats.value.forEach(stat => {
    csvContent += `${stat.name},${stat.value},${stat.unit},${stat.trend === 'up' ? '上升' : '下降'},${stat.changeRate}%,${stat.comparePeriod}\n`
  })
  
  csvContent += "\n绿电设备数据\n"
  csvContent += "设备名称,设备类型,当前功率,今日发电量,本月发电量,碳减排量,状态,效率\n"
  
  deviceData.value.forEach(device => {
    csvContent += `${device.deviceName},${device.deviceType},${device.currentPower},${device.todayGeneration},${device.monthGeneration},${device.carbonReduction},${device.status},${device.efficiency}%\n`
  })
  
  // 创建下载链接，添加BOM头支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `绿电总览数据_${dateRange.replace(/\//g, '-')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  console.log('绿电数据导出完成')
}

onMounted(async () => {
  // 初始化数据
  await refreshAllData()
})
</script>

<style scoped>
.green-power-overview {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.overview-card {
  margin-bottom: 20px;
}

.green-power-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 10px 0;
}

.power-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.power-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: #ebeef5;
}

.power-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 50%;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;
}

.power-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.power-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.power-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 6px;
}

.power-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.power-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.power-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.power-divider {
  display: none;
}

.generation-chart,
.carbon-chart,
.device-status-chart,
.efficiency-chart {
  height: 300px;
}

.revenue-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #ffffff;
}

.revenue-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.1);
  margin-right: 20px;
}

.revenue-info {
  flex: 1;
}

.revenue-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.revenue-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.revenue-desc {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .green-power-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .power-item {
    flex: none;
    max-width: none;
    justify-content: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }
  
  .power-item:not(:last-child)::after {
    display: none;
  }
  
  .power-item:last-child {
    margin-bottom: 0;
  }
  
  .revenue-item {
    flex-direction: column;
    text-align: center;
  }
  
  .revenue-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style> 