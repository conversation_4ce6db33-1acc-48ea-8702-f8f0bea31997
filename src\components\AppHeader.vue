<template>
  <header class="app-header">
    <div class="header-left">
      <!-- Logo区域 -->
      <div class="logo-container">
        <div class="logo" style="background-color: #409EFF;"></div>
        <h1 class="system-title">能耗管理系统产品原型</h1>
      </div>
    </div>
    
    <div class="header-right">
      <!-- 日期时间显示 -->
      <div class="date-time-display">{{ currentDateTime }}</div>

      <!-- 通知图标 -->
      <el-popover
        placement="bottom"
        title="通知中心"
        :width="300"
        trigger="click"
      >
        <template #reference>
          <el-badge :value="unreadNotifications" :max="99" class="notification-badge" :hidden="unreadNotifications === 0">
            <el-icon class="header-icon"><Bell /></el-icon>
          </el-badge>
        </template>
        <div class="notification-panel">
          <div v-if="notifications.length === 0" class="no-notifications">
            暂无新通知
          </div>
          <div v-else>
            <div v-for="item in notifications" :key="item.id" class="notification-item">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-time">{{ item.time }}</div>
            </div>
          </div>
          <div class="notification-footer">
            <el-button type="primary" link size="small">查看全部</el-button>
          </div>
        </div>
      </el-popover>
      
      <!-- 用户信息 -->
      <div class="user-info" @click.stop="toggleUserMenu">
        <el-avatar :size="32" :src="userAvatar">{{ userInitials }}</el-avatar>
        <span class="username">{{ username }}</span>
        <el-icon><ArrowDown /></el-icon>
        
        <!-- 用户菜单 -->
        <div class="user-dropdown" v-if="showUserMenu" @click.stop>
          <div class="dropdown-item" @click="goToProfile">
            <el-icon><User /></el-icon>
            <span>个人信息</span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item" @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            <span>退出登录</span>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { Bell, ArrowDown, User, SwitchButton } from '@element-plus/icons-vue'

// 日期时间
const currentDateTime = ref('')
let timer: number | null = null

const updateDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentDateTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 模拟通知数据
const notifications = ref([
  { id: 1, title: '设备 #PV001 出现异常，请及时处理', time: '5分钟前' },
  { id: 2, title: '系统将在今晚23:00进行维护升级', time: '2小时前' },
  { id: 3, title: '新的月度能耗报告已生成', time: '1天前' },
])
const unreadNotifications = computed(() => notifications.value.length)

// 用户信息
const username = ref('管理员')
const userAvatar = ref('')
const userInitials = computed(() => username.value.charAt(0).toUpperCase())

// 用户菜单
const showUserMenu = ref(false)
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const userInfo = document.querySelector('.user-info')
  if (userInfo && !userInfo.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

// 使用生命周期钩子管理事件监听器
onMounted(() => {
  updateDateTime()
  timer = window.setInterval(updateDateTime, 1000)
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
  document.removeEventListener('click', handleClickOutside)
})

// 路由和认证
const router = useRouter()
const authStore = useAuthStore()

// 导航方法
const goToProfile = () => {
  router.push('/profile')
  showUserMenu.value = false
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
  showUserMenu.value = false
}
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background-color: #FFFFFF;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 10px;
}

.system-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.date-time-display {
  margin-right: 20px;
  font-size: 14px;
  color: var(--text-color-light);
}

.header-icon {
  font-size: 20px;
  color: var(--text-color-light);
  cursor: pointer;
}

.notification-badge {
  margin-right: 20px;
  cursor: pointer;
}

.notification-panel {
  display: flex;
  flex-direction: column;
}

.notification-item {
  padding: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-title {
  font-size: 14px;
  color: #303133;
}

.notification-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.no-notifications {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.notification-footer {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #EBEEF5;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}

.user-dropdown {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 160px;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  animation: dropdown 0.2s ease-in-out;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #F5F7FA;
}

.dropdown-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.dropdown-divider {
  height: 1px;
  background-color: #EBEEF5;
  margin: 4px 0;
}

@keyframes dropdown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 