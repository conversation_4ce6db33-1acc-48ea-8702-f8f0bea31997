<template>
  <div class="fault-prediction" v-loading="loading" element-loading-text="正在加载故障预测数据...">
    <BreadCrumb :items="breadcrumbItems" />

    <!-- 1. 状态总览 - 24列全宽，渐变背景 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="overview-card">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>故障预测分析中心</h2>
                <div class="status-indicator" :class="systemStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(systemStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(systemStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>

          <div class="overview-metrics">
            <div class="metric-item">
              <div class="metric-icon normal">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ faultStats.normalDevices }}</div>
                <div class="metric-label">正常设备</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon warning">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ faultStats.warningDevices }}</div>
                <div class="metric-label">预警设备</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon risk">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ faultStats.riskDevices }}</div>
                <div class="metric-label">高风险设备</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon accuracy">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ faultStats.accuracy }}</div>
                <div class="metric-label">预测准确率</div>
                <div class="metric-unit">%</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 2. 实时监控 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                设备健康状态监控
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedDeviceType" size="small" style="width: 120px;">
                  <el-option label="全部设备" value="" />
                  <el-option label="空调系统" value="hvac" />
                  <el-option label="照明系统" value="lighting" />
                  <el-option label="电梯系统" value="elevator" />
                  <el-option label="消防系统" value="fire" />
                </el-select>
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>

          <div class="data-grid">
            <div class="data-card" v-for="device in deviceHealthData" :key="device.id">
              <div class="device-header">
                <div class="device-icon" :class="device.healthStatus">
                  <el-icon>
                    <component :is="getDeviceIcon(device.type)" />
                  </el-icon>
                </div>
                <div class="device-info">
                  <div class="device-name">{{ device.name }}</div>
                  <div class="device-type">{{ device.type }}</div>
                </div>
                <div class="health-score" :class="getHealthScoreClass(device.healthScore)">
                  {{ device.healthScore }}%
                </div>
              </div>
              <div class="device-details">
                <div class="detail-item">
                  <span class="detail-label">运行时间：</span>
                  <span class="detail-value">{{ device.runningTime }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">故障风险：</span>
                  <span class="detail-value" :class="getRiskClass(device.riskLevel)">{{ device.riskLevel }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">预计维护：</span>
                  <span class="detail-value">{{ device.nextMaintenance }}</span>
                </div>
              </div>
              <div class="device-actions">
                <el-button type="primary" size="small" @click="viewDeviceDetail(device)">
                  详情
                </el-button>
                <el-button type="warning" size="small" @click="scheduleMaintenance(device)">
                  维护
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 3. 故障预警 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><WarningFilled /></el-icon>
                故障预警列表
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedRiskLevel" size="small" style="width: 120px;">
                  <el-option label="全部风险" value="" />
                  <el-option label="高风险" value="high" />
                  <el-option label="中风险" value="medium" />
                  <el-option label="低风险" value="low" />
                </el-select>
                <el-button type="primary" size="small" @click="exportWarnings">
                  <el-icon><Operation /></el-icon>
                  导出预警
                </el-button>
              </div>
            </div>
          </template>

          <div class="warning-grid">
            <div class="warning-card" v-for="warning in faultWarnings" :key="warning.id">
              <div class="warning-header">
                <div class="warning-level" :class="warning.level">
                  <el-icon>
                    <component :is="getWarningIcon(warning.level)" />
                  </el-icon>
                  <span>{{ getWarningText(warning.level) }}</span>
                </div>
                <div class="warning-time">{{ formatTime(warning.predictedTime) }}</div>
              </div>
              <div class="warning-content">
                <div class="warning-device">{{ warning.deviceName }}</div>
                <div class="warning-description">{{ warning.description }}</div>
                <div class="warning-probability">故障概率：{{ warning.probability }}%</div>
              </div>
              <div class="warning-actions">
                <el-button type="primary" size="small" @click="handleWarning(warning)">
                  处理
                </el-button>
                <el-button type="info" size="small" @click="ignoreWarning(warning)">
                  忽略
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 4. 详细管理 - 24列全宽 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                故障预测历史记录
              </h3>
              <div class="header-controls">
                <el-date-picker
                  v-model="selectedDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  style="width: 240px;"
                />
                <el-button type="success" size="small" @click="generateReport">
                  <el-icon><Setting /></el-icon>
                  生成报告
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="faultHistory" border stripe style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" align="center" />
            <el-table-column prop="deviceName" label="设备名称" min-width="150" />
            <el-table-column prop="deviceType" label="设备类型" width="120" align="center" />
            <el-table-column label="预测结果" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getPredictionResultType(row.predictionResult)">
                  {{ getPredictionResultText(row.predictionResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="actualResult" label="实际结果" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getActualResultType(row.actualResult)">
                  {{ getActualResultText(row.actualResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="准确性" width="100" align="center">
              <template #default="{ row }">
                <span :class="getAccuracyClass(row.accuracy)">
                  {{ row.accuracy ? '准确' : '误报' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="probability" label="预测概率" width="100" align="center">
              <template #default="{ row }">
                <span>{{ row.probability }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="maintenanceAction" label="维护措施" min-width="200" />
            <el-table-column prop="cost" label="维护成本" width="120" align="center" />
            <el-table-column label="操作" width="150" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewHistoryDetail(row)">
                  详情
                </el-button>
                <el-button type="info" size="small" @click="analyzeCase(row)">
                  分析
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalRecords"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="showDeviceDetailDialog"
      title="设备详细信息"
      width="800px"
      :before-close="handleCloseDeviceDetail">
      <div v-if="selectedDevice" class="device-detail-content">
        <!-- 设备基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">设备名称：</span>
                <span class="info-value">{{ selectedDevice.name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">设备类型：</span>
                <span class="info-value">{{ selectedDevice.type }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">健康评分：</span>
                <span class="info-value" :class="getHealthScoreClass(selectedDevice.healthScore)">
                  {{ selectedDevice.healthScore }}%
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">运行时间：</span>
                <span class="info-value">{{ selectedDevice.runningTime }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">风险等级：</span>
                <span class="info-value" :class="getRiskClass(selectedDevice.riskLevel)">
                  {{ selectedDevice.riskLevel }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="info-label">预计维护：</span>
                <span class="info-value">{{ selectedDevice.nextMaintenance }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 实时监控数据 -->
        <div class="detail-section">
          <h4 class="section-title">实时监控数据</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="monitor-card">
                <div class="monitor-title">温度</div>
                <div class="monitor-value">{{ deviceMonitorData.temperature }}°C</div>
                <div class="monitor-status normal">正常</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="monitor-card">
                <div class="monitor-title">振动</div>
                <div class="monitor-value">{{ deviceMonitorData.vibration }}mm/s</div>
                <div class="monitor-status" :class="deviceMonitorData.vibrationStatus">
                  {{ deviceMonitorData.vibrationStatus === 'normal' ? '正常' : '异常' }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="monitor-card">
                <div class="monitor-title">电流</div>
                <div class="monitor-value">{{ deviceMonitorData.current }}A</div>
                <div class="monitor-status normal">正常</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 历史故障记录 -->
        <div class="detail-section">
          <h4 class="section-title">历史故障记录</h4>
          <el-table :data="deviceFaultHistory" border stripe style="width: 100%" max-height="200">
            <el-table-column prop="date" label="日期" width="120" align="center" />
            <el-table-column prop="faultType" label="故障类型" width="150" />
            <el-table-column prop="description" label="故障描述" min-width="200" />
            <el-table-column prop="repairTime" label="修复时间" width="100" align="center" />
            <el-table-column prop="cost" label="维修费用" width="100" align="center" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDeviceDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="openMaintenanceDialog">安排维护</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 维护安排对话框 -->
    <el-dialog
      v-model="showMaintenanceDialog"
      title="安排设备维护"
      width="600px"
      :before-close="handleCloseMaintenanceDialog">
      <el-form
        ref="maintenanceFormRef"
        :model="maintenanceForm"
        :rules="maintenanceRules"
        label-width="120px">
        <el-form-item label="设备名称：">
          <el-input v-model="maintenanceForm.deviceName" disabled />
        </el-form-item>

        <el-form-item label="维护类型：" prop="maintenanceType">
          <el-select v-model="maintenanceForm.maintenanceType" placeholder="请选择维护类型">
            <el-option label="预防性维护" value="preventive" />
            <el-option label="纠正性维护" value="corrective" />
            <el-option label="紧急维护" value="emergency" />
            <el-option label="定期保养" value="routine" />
          </el-select>
        </el-form-item>

        <el-form-item label="维护日期：" prop="maintenanceDate">
          <el-date-picker
            v-model="maintenanceForm.maintenanceDate"
            type="datetime"
            placeholder="选择维护日期和时间"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="维护人员：" prop="technician">
          <el-select v-model="maintenanceForm.technician" placeholder="请选择维护人员">
            <el-option label="张工程师" value="zhang" />
            <el-option label="李技师" value="li" />
            <el-option label="王师傅" value="wang" />
            <el-option label="外包团队" value="outsource" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级：" prop="priority">
          <el-radio-group v-model="maintenanceForm.priority">
            <el-radio label="low">低</el-radio>
            <el-radio label="medium">中</el-radio>
            <el-radio label="high">高</el-radio>
            <el-radio label="urgent">紧急</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="预计耗时：" prop="estimatedDuration">
          <el-input-number
            v-model="maintenanceForm.estimatedDuration"
            :min="0.5"
            :max="24"
            :step="0.5"
            controls-position="right"
            style="width: 150px;" />
          <span style="margin-left: 10px;">小时</span>
        </el-form-item>

        <el-form-item label="维护内容：" prop="description">
          <el-input
            v-model="maintenanceForm.description"
            type="textarea"
            :rows="3"
            placeholder="请描述具体的维护内容和注意事项" />
        </el-form-item>

        <el-form-item label="备件需求：">
          <el-input
            v-model="maintenanceForm.spareParts"
            type="textarea"
            :rows="2"
            placeholder="列出所需的备件和材料" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMaintenanceDialog = false">取消</el-button>
          <el-button type="primary" @click="submitMaintenance" :loading="submittingMaintenance">
            确认安排
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  Monitor,
  Timer,
  Operation,
  Setting
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '智能控制中心', to: '/intelligent-control' },
  { text: '故障预测' }
])

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const systemStatus = ref('normal')
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const selectedDeviceType = ref('')
const selectedRiskLevel = ref('')
const selectedDateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(50)

// 对话框相关数据
const showDeviceDetailDialog = ref(false)
const showMaintenanceDialog = ref(false)
const selectedDevice = ref(null)
const submittingMaintenance = ref(false)
const maintenanceFormRef = ref()

// 维护表单数据
const maintenanceForm = reactive({
  deviceName: '',
  maintenanceType: '',
  maintenanceDate: '',
  technician: '',
  priority: 'medium',
  estimatedDuration: 2,
  description: '',
  spareParts: ''
})

// 表单验证规则
const maintenanceRules = {
  maintenanceType: [
    { required: true, message: '请选择维护类型', trigger: 'change' }
  ],
  maintenanceDate: [
    { required: true, message: '请选择维护日期', trigger: 'change' }
  ],
  technician: [
    { required: true, message: '请选择维护人员', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入维护内容', trigger: 'blur' },
    { min: 10, message: '维护内容至少10个字符', trigger: 'blur' }
  ]
}

// 设备监控数据
const deviceMonitorData = reactive({
  temperature: 45,
  vibration: 2.3,
  vibrationStatus: 'normal',
  current: 12.5
})

// 设备故障历史
const deviceFaultHistory = ref([
  {
    date: '2024-01-10',
    faultType: '轴承磨损',
    description: '轴承温度过高，噪音异常',
    repairTime: '4小时',
    cost: '¥2,500'
  },
  {
    date: '2023-12-15',
    faultType: '电机故障',
    description: '电机启动困难，电流异常',
    repairTime: '6小时',
    cost: '¥4,200'
  },
  {
    date: '2023-11-20',
    faultType: '传感器故障',
    description: '温度传感器读数异常',
    repairTime: '2小时',
    cost: '¥800'
  }
])

// 故障统计数据
const faultStats = reactive({
  normalDevices: 45,
  warningDevices: 8,
  riskDevices: 3,
  accuracy: 92.5
})

// 设备健康数据
const deviceHealthData = ref([
  {
    id: 'DEV001',
    name: '中央空调-1号机组',
    type: '空调系统',
    healthStatus: 'normal',
    healthScore: 95,
    runningTime: '2,340小时',
    riskLevel: '低',
    nextMaintenance: '2024-03-15'
  },
  {
    id: 'DEV002',
    name: '电梯-A座1号梯',
    type: '电梯系统',
    healthStatus: 'warning',
    healthScore: 72,
    runningTime: '8,760小时',
    riskLevel: '中',
    nextMaintenance: '2024-02-01'
  },
  {
    id: 'DEV003',
    name: '消防泵-主泵',
    type: '消防系统',
    healthStatus: 'risk',
    healthScore: 45,
    runningTime: '12,500小时',
    riskLevel: '高',
    nextMaintenance: '2024-01-20'
  },
  {
    id: 'DEV004',
    name: '照明控制器-楼层1',
    type: '照明系统',
    healthStatus: 'normal',
    healthScore: 88,
    runningTime: '5,200小时',
    riskLevel: '低',
    nextMaintenance: '2024-04-10'
  }
])

// 故障预警数据
const faultWarnings = ref([
  {
    id: 'WARN001',
    level: 'high',
    deviceName: '消防泵-主泵',
    description: '轴承温度异常升高，预计72小时内可能发生故障',
    probability: 85,
    predictedTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'WARN002',
    level: 'medium',
    deviceName: '电梯-A座1号梯',
    description: '钢丝绳磨损加剧，建议提前进行维护检查',
    probability: 65,
    predictedTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'WARN003',
    level: 'low',
    deviceName: '中央空调-2号机组',
    description: '过滤器阻塞程度增加，影响运行效率',
    probability: 35,
    predictedTime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
  }
])

// 故障历史记录
const faultHistory = ref([
  {
    date: '2024-01-15',
    deviceName: '电梯-B座2号梯',
    deviceType: '电梯系统',
    predictionResult: 'fault',
    actualResult: 'fault',
    accuracy: true,
    probability: 78,
    maintenanceAction: '更换钢丝绳，调整制动器',
    cost: '¥12,500'
  },
  {
    date: '2024-01-14',
    deviceName: '空调-3号机组',
    deviceType: '空调系统',
    predictionResult: 'normal',
    actualResult: 'normal',
    accuracy: true,
    probability: 15,
    maintenanceAction: '定期保养',
    cost: '¥800'
  },
  {
    date: '2024-01-13',
    deviceName: '消防泵-备用泵',
    deviceType: '消防系统',
    predictionResult: 'fault',
    actualResult: 'normal',
    accuracy: false,
    probability: 68,
    maintenanceAction: '误报，无需维护',
    cost: '¥0'
  },
  {
    date: '2024-01-12',
    deviceName: '照明控制器-楼层3',
    deviceType: '照明系统',
    predictionResult: 'warning',
    actualResult: 'warning',
    accuracy: true,
    probability: 45,
    maintenanceAction: '更换继电器',
    cost: '¥350'
  }
])

// 工具函数
const getStatusIcon = (status: string) => {
  const iconMap = {
    'normal': SuccessFilled,
    'warning': WarningFilled,
    'danger': CircleCloseFilled
  }
  return iconMap[status] || SuccessFilled
}

const getStatusText = (status: string) => {
  const textMap = {
    'normal': '系统正常',
    'warning': '需要关注',
    'danger': '存在风险'
  }
  return textMap[status] || '未知状态'
}

const getDeviceIcon = (type: string) => {
  const iconMap = {
    '空调系统': Setting,
    '电梯系统': Operation,
    '消防系统': WarningFilled,
    '照明系统': Monitor
  }
  return iconMap[type] || Setting
}

const getHealthScoreClass = (score: number) => {
  if (score >= 80) return 'score-good'
  if (score >= 60) return 'score-warning'
  return 'score-danger'
}

const getRiskClass = (level: string) => {
  const classMap = {
    '低': 'risk-low',
    '中': 'risk-medium',
    '高': 'risk-high'
  }
  return classMap[level] || 'risk-low'
}

const getWarningIcon = (level: string) => {
  const iconMap = {
    'high': CircleCloseFilled,
    'medium': WarningFilled,
    'low': Monitor
  }
  return iconMap[level] || Monitor
}

const getWarningText = (level: string) => {
  const textMap = {
    'high': '高风险',
    'medium': '中风险',
    'low': '低风险'
  }
  return textMap[level] || '未知'
}

const getPredictionResultType = (result: string) => {
  const typeMap = {
    'fault': 'danger',
    'warning': 'warning',
    'normal': 'success'
  }
  return typeMap[result] || 'info'
}

const getPredictionResultText = (result: string) => {
  const textMap = {
    'fault': '故障',
    'warning': '预警',
    'normal': '正常'
  }
  return textMap[result] || '未知'
}

const getActualResultType = (result: string) => {
  const typeMap = {
    'fault': 'danger',
    'warning': 'warning',
    'normal': 'success'
  }
  return typeMap[result] || 'info'
}

const getActualResultText = (result: string) => {
  const textMap = {
    'fault': '故障',
    'warning': '预警',
    'normal': '正常'
  }
  return textMap[result] || '未知'
}

const getAccuracyClass = (accuracy: boolean) => {
  return accuracy ? 'accuracy-correct' : 'accuracy-wrong'
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = time.getTime() - now.getTime()
  const days = Math.ceil(diff / (1000 * 60 * 60 * 24))

  if (days <= 0) return '即将发生'
  if (days === 1) return '明天'
  if (days <= 7) return `${days}天后`
  return time.toLocaleDateString('zh-CN')
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  ElMessage.success(`自动刷新已${autoRefresh.value ? '开启' : '关闭'}`)
}

const viewDeviceDetail = (device: any) => {
  selectedDevice.value = device
  // 根据设备类型模拟不同的监控数据
  updateDeviceMonitorData(device)
  showDeviceDetailDialog.value = true
}

const scheduleMaintenance = (device: any) => {
  selectedDevice.value = device
  // 重置表单
  resetMaintenanceForm()
  maintenanceForm.deviceName = device.name
  // 根据设备风险等级设置默认优先级
  if (device.riskLevel === '高') {
    maintenanceForm.priority = 'high'
    maintenanceForm.maintenanceType = 'emergency'
  } else if (device.riskLevel === '中') {
    maintenanceForm.priority = 'medium'
    maintenanceForm.maintenanceType = 'preventive'
  } else {
    maintenanceForm.priority = 'low'
    maintenanceForm.maintenanceType = 'routine'
  }
  showMaintenanceDialog.value = true
}

// 更新设备监控数据
const updateDeviceMonitorData = (device: any) => {
  // 根据设备类型和状态模拟监控数据
  switch (device.type) {
    case '空调系统':
      deviceMonitorData.temperature = device.healthStatus === 'risk' ? 65 : 45
      deviceMonitorData.vibration = device.healthStatus === 'risk' ? 4.2 : 2.3
      deviceMonitorData.vibrationStatus = device.healthStatus === 'risk' ? 'abnormal' : 'normal'
      deviceMonitorData.current = device.healthStatus === 'risk' ? 18.5 : 12.5
      break
    case '电梯系统':
      deviceMonitorData.temperature = device.healthStatus === 'risk' ? 55 : 38
      deviceMonitorData.vibration = device.healthStatus === 'risk' ? 3.8 : 1.8
      deviceMonitorData.vibrationStatus = device.healthStatus === 'risk' ? 'abnormal' : 'normal'
      deviceMonitorData.current = device.healthStatus === 'risk' ? 25.2 : 15.8
      break
    case '消防系统':
      deviceMonitorData.temperature = device.healthStatus === 'risk' ? 48 : 32
      deviceMonitorData.vibration = device.healthStatus === 'risk' ? 2.9 : 1.2
      deviceMonitorData.vibrationStatus = device.healthStatus === 'risk' ? 'abnormal' : 'normal'
      deviceMonitorData.current = device.healthStatus === 'risk' ? 8.9 : 5.2
      break
    default:
      deviceMonitorData.temperature = 42
      deviceMonitorData.vibration = 2.1
      deviceMonitorData.vibrationStatus = 'normal'
      deviceMonitorData.current = 10.3
  }
}

// 重置维护表单
const resetMaintenanceForm = () => {
  maintenanceForm.deviceName = ''
  maintenanceForm.maintenanceType = ''
  maintenanceForm.maintenanceDate = ''
  maintenanceForm.technician = ''
  maintenanceForm.priority = 'medium'
  maintenanceForm.estimatedDuration = 2
  maintenanceForm.description = ''
  maintenanceForm.spareParts = ''
}

// 提交维护安排
const submitMaintenance = async () => {
  if (!maintenanceFormRef.value) return

  try {
    await maintenanceFormRef.value.validate()
    submittingMaintenance.value = true

    // 模拟提交过程
    setTimeout(() => {
      submittingMaintenance.value = false
      showMaintenanceDialog.value = false

      // 更新设备的下次维护时间
      if (selectedDevice.value) {
        const maintenanceDate = new Date(maintenanceForm.maintenanceDate)
        selectedDevice.value.nextMaintenance = maintenanceDate.toLocaleDateString('zh-CN')

        // 如果是高风险设备，安排维护后降低风险等级
        if (selectedDevice.value.riskLevel === '高') {
          selectedDevice.value.riskLevel = '中'
          selectedDevice.value.healthStatus = 'warning'
        }
      }

      ElMessage.success(`维护任务已安排！设备：${maintenanceForm.deviceName}，维护时间：${new Date(maintenanceForm.maintenanceDate).toLocaleString('zh-CN')}`)

      // 重置表单
      resetMaintenanceForm()
    }, 1500)
  } catch (error) {
    ElMessage.error('请完善维护信息')
  }
}

// 打开维护对话框
const openMaintenanceDialog = () => {
  showDeviceDetailDialog.value = false
  setTimeout(() => {
    scheduleMaintenance(selectedDevice.value)
  }, 100)
}

// 关闭设备详情对话框
const handleCloseDeviceDetail = () => {
  showDeviceDetailDialog.value = false
  selectedDevice.value = null
}

// 关闭维护对话框
const handleCloseMaintenanceDialog = () => {
  showMaintenanceDialog.value = false
  resetMaintenanceForm()
}

const handleWarning = (warning: any) => {
  ElMessage.success(`已处理预警: ${warning.deviceName}`)
}

const ignoreWarning = (warning: any) => {
  ElMessage.info(`已忽略预警: ${warning.deviceName}`)
}

const exportWarnings = () => {
  ElMessage.success('预警数据导出成功')
}

const generateReport = () => {
  ElMessage.success('故障预测报告生成成功')
}

const viewHistoryDetail = (row: any) => {
  ElMessage.info(`查看历史详情: ${row.deviceName}`)
}

const analyzeCase = (row: any) => {
  ElMessage.info(`分析案例: ${row.deviceName}`)
}
</script>

<style scoped>
.fault-prediction {
  padding: 0;
}

/* 1. 状态总览样式 - 渐变背景 */
.overview-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.overview-card :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.overview-card :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.status-indicator.normal .status-icon {
  color: #67c23a;
}

.status-indicator.warning .status-icon {
  color: #e6a23c;
}

.status-indicator.danger .status-icon {
  color: #f56c6c;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.overview-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 2px solid;
}

.metric-icon.normal {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border-color: #67c23a;
}

.metric-icon.warning {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border-color: #e6a23c;
}

.metric-icon.risk {
  background: rgba(245, 108, 108, 0.2);
  color: #f56c6c;
  border-color: #f56c6c;
}

.metric-icon.accuracy {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border-color: #409eff;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.metric-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

/* 2. 数据网格样式 - 自适应布局 */
.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.data-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.data-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.device-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.device-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.device-icon.normal {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.device-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.device-icon.risk {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.device-type {
  font-size: 12px;
  color: #909399;
}

.health-score {
  font-size: 18px;
  font-weight: 700;
}

.health-score.score-good {
  color: #67c23a;
}

.health-score.score-warning {
  color: #e6a23c;
}

.health-score.score-danger {
  color: #f56c6c;
}

.device-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.detail-value.risk-low {
  color: #67c23a;
}

.detail-value.risk-medium {
  color: #e6a23c;
}

.detail-value.risk-high {
  color: #f56c6c;
}

.device-actions {
  display: flex;
  gap: 8px;
}

.device-actions .el-button {
  flex: 1;
}

/* 3. 预警网格样式 */
.warning-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.warning-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.warning-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.warning-level {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.warning-level.high {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.warning-level.medium {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border: 1px solid #e6a23c;
}

.warning-level.low {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border: 1px solid #409eff;
}

.warning-time {
  font-size: 12px;
  color: #909399;
}

.warning-content {
  margin-bottom: 16px;
}

.warning-device {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.warning-description {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 8px;
}

.warning-probability {
  font-size: 12px;
  color: #909399;
  font-weight: 600;
}

.warning-actions {
  display: flex;
  gap: 8px;
}

.warning-actions .el-button {
  flex: 1;
}

/* 4. 表格样式 */
.accuracy-correct {
  color: #67c23a;
  font-weight: 600;
}

.accuracy-wrong {
  color: #f56c6c;
  font-weight: 600;
}

/* 通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .data-grid,
  .warning-grid {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .header-controls .el-select,
  .header-controls .el-button,
  .header-controls .el-date-picker {
    width: 100%;
  }
}

/* 设备详情对话框样式 */
.device-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #409eff;
  margin-right: 8px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.info-value.score-good {
  color: #67c23a;
}

.info-value.score-warning {
  color: #e6a23c;
}

.info-value.score-danger {
  color: #f56c6c;
}

.info-value.risk-low {
  color: #67c23a;
}

.info-value.risk-medium {
  color: #e6a23c;
}

.info-value.risk-high {
  color: #f56c6c;
}

.monitor-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.monitor-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.monitor-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.monitor-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.monitor-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.monitor-status.normal {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.monitor-status.abnormal {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

/* 维护表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-radio-group .el-radio {
  margin-right: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-detail-content {
    max-height: 400px;
  }

  .monitor-card {
    margin-bottom: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 4px;
    min-width: auto;
  }
}
</style>
