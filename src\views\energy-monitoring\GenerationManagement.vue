<template>
  <div class="generation-management">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 发电统计概览 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <h3>发电统计概览</h3>
          <div class="header-controls">
            <el-date-picker
              v-model="selectedDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="handleDateRangeChange"
            />
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="generation-stats">
        <div class="stat-item" v-for="(item, index) in generationStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-value-container">
              <span class="stat-value">{{ item.value }}</span>
              <span class="stat-unit">{{ item.unit }}</span>
            </div>
            <div class="stat-trend" :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
              {{ item.comparePeriod }}{{ item.trend === 'up' ? '增长' : '下降' }} {{ item.trend === 'up' ? '+' : '-' }}{{ item.changeRate }}%
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 发电量分析与抵碳量计算 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>发电量趋势与抵碳量分析</h3>
          <el-radio-group v-model="chartTimeRange" size="small" @change="handleTimeRangeChange">
            <el-radio-button label="day">日</el-radio-button>
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div class="trend-carbon-container">
        <div class="trend-section">
          <div class="generation-trend-chart" ref="generationTrendChart"></div>
        </div>
        <div class="carbon-section">
          <h4 class="carbon-title">抵碳量分析</h4>
          <div class="carbon-calculation">
            <div class="carbon-item">
              <div class="carbon-label">今日抵碳量</div>
              <div class="carbon-value">{{ carbonData.today }} kg</div>
              <div class="carbon-desc">相当于种植 {{ Math.round(carbonData.today / 22) }} 棵树</div>
            </div>
            <div class="carbon-item">
              <div class="carbon-label">本月抵碳量</div>
              <div class="carbon-value">{{ carbonData.month }} kg</div>
              <div class="carbon-desc">相当于种植 {{ Math.round(carbonData.month / 22) }} 棵树</div>
            </div>
            <div class="carbon-item">
              <div class="carbon-label">年度抵碳量</div>
              <div class="carbon-value">{{ carbonData.year }} kg</div>
              <div class="carbon-desc">相当于种植 {{ Math.round(carbonData.year / 22) }} 棵树</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 设备监控与性能分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>设备运行状态</h3>
          </template>
          <div class="device-status-chart" ref="deviceStatusChart"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>发电效率分析</h3>
          </template>
          <div class="efficiency-analysis-chart" ref="efficiencyChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 发电预测与建议 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <h3>发电预测与优化建议</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="prediction-section">
            <h4>明日发电预测</h4>
            <div class="prediction-chart" ref="predictionChart"></div>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="suggestions-section">
            <h4>优化建议</h4>
            <div class="suggestions-list">
              <div class="suggestion-item" v-for="(suggestion, index) in suggestions" :key="index">
                <div class="suggestion-icon">
                  <el-icon :color="suggestion.color"><component :is="suggestion.icon" /></el-icon>
                </div>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 发电设备详情管理 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>发电设备详情</h3>
          <div class="header-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索设备名称"
              size="small"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态筛选" size="small" style="width: 120px; margin-right: 10px;" @change="handleStatusFilter">
              <el-option label="全部" value="" />
              <el-option label="正常" value="正常" />
              <el-option label="故障" value="故障" />
              <el-option label="维护" value="维护" />
            </el-select>
            <el-button type="primary" size="small" @click="addDevice">
              <el-icon><Plus /></el-icon>
              添加设备
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredDeviceData" 
        style="width: 100%" 
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="deviceId" label="设备ID" align="center" width="100" />
        <el-table-column prop="deviceName" label="设备名称" align="center" />
        <el-table-column prop="deviceType" label="设备类型" align="center">
          <template #default="scope">
            <el-tag :type="getDeviceTypeColor(scope.row.deviceType)">
              {{ scope.row.deviceType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="installedCapacity" label="装机容量" align="center">
          <template #default="scope">
            <span>{{ scope.row.installedCapacity }} kW</span>
          </template>
        </el-table-column>
        <el-table-column prop="currentPower" label="当前功率" align="center">
          <template #default="scope">
            <span>{{ scope.row.currentPower }} kW</span>
          </template>
        </el-table-column>
        <el-table-column prop="todayGeneration" label="今日发电量" align="center">
          <template #default="scope">
            <span>{{ scope.row.todayGeneration }} kWh</span>
          </template>
        </el-table-column>
        <el-table-column prop="monthGeneration" label="本月发电量" align="center">
          <template #default="scope">
            <span>{{ scope.row.monthGeneration }} kWh</span>
          </template>
        </el-table-column>
        <el-table-column prop="carbonReduction" label="抵碳量" align="center">
          <template #default="scope">
            <span>{{ scope.row.carbonReduction }} kg</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="运行状态" align="center">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="efficiency" label="发电效率" align="center">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.efficiency" 
              :color="getEfficiencyColor(scope.row.efficiency)"
              :stroke-width="8"
              style="width: 90%"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDevice(scope.row)">详情</el-button>
            <el-button type="warning" size="small" @click="editDevice(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      title="添加发电设备"
      width="600px"
      :before-close="handleCloseAddDialog"
    >
      <el-form ref="addDeviceFormRef" :model="newDevice" :rules="deviceRules" label-width="120px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="newDevice.deviceId" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="newDevice.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="newDevice.deviceType" placeholder="请选择设备类型" style="width: 100%">
            <el-option label="光伏板" value="光伏板" />
            <el-option label="风力发电" value="风力发电" />
            <el-option label="水力发电" value="水力发电" />
          </el-select>
        </el-form-item>
        <el-form-item label="装机容量" prop="installedCapacity">
          <el-input v-model.number="newDevice.installedCapacity" placeholder="请输入装机容量">
            <template #suffix>kW</template>
          </el-input>
        </el-form-item>
        <el-form-item label="当前功率" prop="currentPower">
          <el-input v-model.number="newDevice.currentPower" placeholder="请输入当前功率">
            <template #suffix>kW</template>
          </el-input>
        </el-form-item>
        <el-form-item label="今日发电量" prop="todayGeneration">
          <el-input v-model.number="newDevice.todayGeneration" placeholder="请输入今日发电量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="本月发电量" prop="monthGeneration">
          <el-input v-model.number="newDevice.monthGeneration" placeholder="请输入本月发电量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="碳减排量" prop="carbonReduction">
          <el-input v-model.number="newDevice.carbonReduction" placeholder="请输入碳减排量">
            <template #suffix>kg</template>
          </el-input>
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="newDevice.status" placeholder="请选择设备状态" style="width: 100%">
            <el-option label="正常" value="正常" />
            <el-option label="故障" value="故障" />
            <el-option label="维护" value="维护" />
          </el-select>
        </el-form-item>
        <el-form-item label="发电效率" prop="efficiency">
          <el-input v-model.number="newDevice.efficiency" placeholder="请输入发电效率">
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseAddDialog">取消</el-button>
          <el-button type="primary" @click="handleAddDevice" :loading="addDeviceLoading">
            确认添加
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { 
  Lightning, 
  Sunny, 
  ArrowUp,
  ArrowDown,
  Download,
  Search,
  Plus,
  Warning,
  Tools,
  TrendCharts
} from '@element-plus/icons-vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能源监控中心', to: '/energy-monitoring' },
  { text: '发电管理', to: '' }
])

// 初始化日期范围为过去7天
const today = new Date()
const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
const selectedDateRange = ref<[Date, Date]>([weekAgo, today])
const chartTimeRange = ref('day')
const searchKeyword = ref('')
const statusFilter = ref('')

// 添加设备相关状态
const showAddDeviceDialog = ref(false)
const addDeviceLoading = ref(false)
const addDeviceFormRef = ref()

// 新设备数据
const newDevice = ref({
  deviceId: '',
  deviceName: '',
  deviceType: '',
  installedCapacity: 0,
  currentPower: 0,
  todayGeneration: 0,
  monthGeneration: 0,
  carbonReduction: 0,
  status: '正常',
  efficiency: 0
})

// 表单验证规则
const deviceRules = ref({
  deviceId: [
    { required: true, message: '请输入设备ID', trigger: 'blur' },
    { min: 3, max: 20, message: '设备ID长度在3到20个字符', trigger: 'blur' }
  ],
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '设备名称长度在2到50个字符', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  installedCapacity: [
    { required: true, message: '请输入装机容量', trigger: 'blur' },
    { type: 'number', min: 1, message: '装机容量必须大于0', trigger: 'blur' }
  ],
  currentPower: [
    { required: true, message: '请输入当前功率', trigger: 'blur' },
    { type: 'number', min: 0, message: '当前功率不能小于0', trigger: 'blur' }
  ],
  todayGeneration: [
    { required: true, message: '请输入今日发电量', trigger: 'blur' },
    { type: 'number', min: 0, message: '今日发电量不能小于0', trigger: 'blur' }
  ],
  monthGeneration: [
    { required: true, message: '请输入本月发电量', trigger: 'blur' },
    { type: 'number', min: 0, message: '本月发电量不能小于0', trigger: 'blur' }
  ],
  carbonReduction: [
    { required: true, message: '请输入碳减排量', trigger: 'blur' },
    { type: 'number', min: 0, message: '碳减排量不能小于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择设备状态', trigger: 'change' }
  ],
  efficiency: [
    { required: true, message: '请输入发电效率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '发电效率在0-100之间', trigger: 'blur' }
  ]
})

// 发电统计数据
const generationStats = ref([
  {
    name: '总装机容量',
    value: '2,500',
    unit: 'kW',
    color: '#409eff',
    icon: Lightning,
    trend: 'up',
    changeRate: '0',
    comparePeriod: '较上期'
  },
  {
    name: '今日发电量',
    value: '1,856.7',
    unit: 'kWh',
    color: '#67c23a',
    icon: Sunny,
    trend: 'up',
    changeRate: '12.5',
    comparePeriod: '较昨日'
  },
  {
    name: '本月发电量',
    value: '45,678.9',
    unit: 'kWh',
    color: '#e6a23c',
    icon: TrendCharts,
    trend: 'up',
    changeRate: '8.3',
    comparePeriod: '较上月'
  },
  {
    name: '发电效率',
    value: '87.5',
    unit: '%',
    color: '#f56c6c',
    icon: Tools,
    trend: 'up',
    changeRate: '2.1',
    comparePeriod: '较昨日'
  }
])

// 抵碳量数据
const carbonData = ref({
  today: 928.4,
  month: 22856.7,
  year: 234567.8
})

// 设备数据
const deviceData = ref([
  {
    deviceId: 'PV001',
    deviceName: '光伏发电组1',
    deviceType: '光伏板',
    installedCapacity: 500,
    currentPower: 425.6,
    todayGeneration: 856.7,
    monthGeneration: 25678.9,
    carbonReduction: 428.4,
    status: '正常',
    efficiency: 88
  },
  {
    deviceId: 'PV002',
    deviceName: '光伏发电组2',
    deviceType: '光伏板',
    installedCapacity: 500,
    currentPower: 418.3,
    todayGeneration: 822.2,
    monthGeneration: 24123.5,
    carbonReduction: 411.2,
    status: '正常',
    efficiency: 85
  },
  {
    deviceId: 'PV003',
    deviceName: '光伏发电组3',
    deviceType: '光伏板',
    installedCapacity: 500,
    currentPower: 0,
    todayGeneration: 0,
    monthGeneration: 12345.6,
    carbonReduction: 0,
    status: '维护',
    efficiency: 0
  },
  {
    deviceId: 'WD001',
    deviceName: '风力发电机1',
    deviceType: '风力发电',
    installedCapacity: 1000,
    currentPower: 845.8,
    todayGeneration: 1178.8,
    monthGeneration: 35567.3,
    carbonReduction: 589.4,
    status: '正常',
    efficiency: 92
  }
])

// 优化建议
const suggestions = ref([
  {
    title: '设备维护提醒',
    description: 'PV003设备正在维护中，预计明日完成维护工作',
    icon: Tools,
    color: '#e6a23c'
  },
  {
    title: '天气预警',
    description: '明日多云，光伏发电效率可能下降15%',
    icon: Warning,
    color: '#f56c6c'
  },
  {
    title: '效率优化',
    description: '建议调整PV002设备角度，可提升发电效率3%',
    icon: TrendCharts,
    color: '#67c23a'
  }
])

const generationTrendChart = ref()
const deviceStatusChart = ref()
const efficiencyChart = ref()
const predictionChart = ref()

// 过滤后的设备数据
const filteredDeviceData = computed(() => {
  return deviceData.value.filter(device => {
    // 搜索关键词匹配设备名称、设备ID或设备类型
    const keyword = searchKeyword.value.toLowerCase().trim()
    const matchesSearch = keyword === '' || 
      device.deviceName.toLowerCase().includes(keyword) ||
      device.deviceId.toLowerCase().includes(keyword) ||
      device.deviceType.toLowerCase().includes(keyword)
    
    // 状态筛选
    const matchesStatus = statusFilter.value === '' || device.status === statusFilter.value
    
    return matchesSearch && matchesStatus
  })
})

// 获取设备类型颜色
const getDeviceTypeColor = (type: string) => {
  switch (type) {
    case '光伏板': return 'success'
    case '风力发电': return 'primary'
    case '水力发电': return 'info'
    default: return 'default'
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '故障': return 'danger'
    case '维护': return 'warning'
    default: return 'default'
  }
}

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 80) return '#67c23a'
  if (efficiency >= 60) return '#409eff'
  if (efficiency >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 初始化发电量趋势图表
const initGenerationTrendChart = () => {
  const chart = echarts.init(generationTrendChart.value)
  
  // 根据时间范围生成不同的数据
  let xAxisData: string[] = []
  let solarData: number[] = []
  let windData: number[] = []
  let totalData: number[] = []
  
  switch (chartTimeRange.value) {
    case 'day':
      xAxisData = ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
      solarData = [0, 120, 280, 450, 520, 380, 220, 0]
      windData = [180, 185, 190, 195, 188, 192, 185, 180]
      totalData = [180, 305, 470, 645, 708, 572, 405, 180]
      break
    case 'week':
      xAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      solarData = [2800, 3200, 2900, 3500, 3100, 2600, 2400]
      windData = [1800, 1900, 1750, 2000, 1850, 1700, 1650]
      totalData = [4600, 5100, 4650, 5500, 4950, 4300, 4050]
      break
    case 'month':
      xAxisData = ['第1周', '第2周', '第3周', '第4周']
      solarData = [18500, 21200, 19800, 22000]
      windData = [12500, 13200, 12800, 13500]
      totalData = [31000, 34400, 32600, 35500]
      break
    case 'year':
      xAxisData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      solarData = [85000, 92000, 105000, 118000, 125000, 130000, 135000, 132000, 120000, 108000, 95000, 88000]
      windData = [52000, 55000, 58000, 60000, 62000, 65000, 68000, 66000, 63000, 60000, 57000, 54000]
      totalData = [137000, 147000, 163000, 178000, 187000, 195000, 203000, 198000, 183000, 168000, 152000, 142000]
      break
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker} ${item.seriesName}: ${item.value} kWh<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['光伏发电', '风力发电', '总发电量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      name: '发电量(kWh)'
    },
    series: [
      {
        name: '光伏发电',
        type: 'line',
        data: solarData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '风力发电',
        type: 'line',
        data: windData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#409eff' }
      },
      {
        name: '总发电量',
        type: 'line',
        data: totalData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化抵碳量图表已删除

// 初始化设备状态图表
const initDeviceStatusChart = () => {
  const chart = echarts.init(deviceStatusChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 3, name: '正常运行', itemStyle: { color: '#67c23a' } },
          { value: 1, name: '维护中', itemStyle: { color: '#e6a23c' } },
          { value: 0, name: '故障', itemStyle: { color: '#f56c6c' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化效率分析图表
const initEfficiencyChart = () => {
  const chart = echarts.init(efficiencyChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['PV001', 'PV002', 'PV003', 'WD001']
    },
    yAxis: {
      type: 'value',
      name: '效率(%)',
      max: 100
    },
    series: [
      {
        name: '发电效率',
        type: 'bar',
        data: [
          { value: 88, itemStyle: { color: '#67c23a' } },
          { value: 85, itemStyle: { color: '#67c23a' } },
          { value: 0, itemStyle: { color: '#f56c6c' } },
          { value: 92, itemStyle: { color: '#67c23a' } }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化预测图表
const initPredictionChart = () => {
  const chart = echarts.init(predictionChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['历史发电量', '预测发电量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
    },
    yAxis: {
      type: 'value',
      name: '发电量(kWh)'
    },
    series: [
      {
        name: '历史发电量',
        type: 'line',
        data: [0, 120, 280, 450, 520, 380, 220, 0],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '预测发电量',
        type: 'line',
        data: [0, 102, 238, 383, 442, 323, 187, 0],
        smooth: true,
        lineStyle: { type: 'dashed' },
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }
  chart.setOption(option)
}

// 处理日期范围变化
const handleDateRangeChange = async (dates: [Date, Date]) => {
  if (dates && dates.length === 2) {
    selectedDateRange.value = dates
  console.log('日期范围变化:', dates)
    
    // 根据日期范围重新生成统计数据
    await generateStatsByDateRange(dates)
    
    // 重新初始化所有图表
    await nextTick(() => {
      initGenerationTrendChart()
      initDeviceStatusChart()
      initEfficiencyChart()
      initPredictionChart()
    })
  }
}

// 根据日期范围生成统计数据
const generateStatsByDateRange = async (dates: [Date, Date]) => {
  const [startDate, endDate] = dates
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const multiplier = Math.max(1, daysDiff / 7) // 基于天数差异调整数据量
  
  // 更新发电统计数据
  const baseStats = [
    { name: '总装机容量', baseValue: 2500, unit: 'kW', color: '#409eff', icon: Lightning },
    { name: '今日发电量', baseValue: 1856.7, unit: 'kWh', color: '#67c23a', icon: Sunny },
    { name: '本月发电量', baseValue: 45678.9, unit: 'kWh', color: '#e6a23c', icon: TrendCharts },
    { name: '发电效率', baseValue: 87.5, unit: '%', color: '#f56c6c', icon: Tools }
  ]
  
  generationStats.value = baseStats.map(stat => ({
    ...stat,
    value: stat.name === '总装机容量' ? 
      stat.baseValue.toString() : 
      (stat.baseValue * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    trend: Math.random() > 0.5 ? 'up' : 'down',
    changeRate: (Math.random() * 15).toFixed(1),
    comparePeriod: daysDiff <= 1 ? '较昨日' : daysDiff <= 7 ? '较上周' : '较上月'
  }))
  
  console.log('统计数据已更新')
}

// 处理时间范围变化
const handleTimeRangeChange = async (range: string) => {
  chartTimeRange.value = range
  console.log('时间范围变化:', range)
  
  // 根据时间范围更新抵碳量数据
  updateCarbonData(range)
  
  // 重新初始化发电趋势图表
  await nextTick(() => {
    initGenerationTrendChart()
  })
}

// 根据时间范围更新抵碳量数据
const updateCarbonData = (range: string) => {
  const baseCarbon = { today: 928.4, month: 22856.7, year: 234567.8 }
  
  switch (range) {
    case 'day':
      carbonData.value = {
        today: baseCarbon.today * (0.8 + Math.random() * 0.4),
        month: baseCarbon.month * (0.8 + Math.random() * 0.4),
        year: baseCarbon.year * (0.8 + Math.random() * 0.4)
      }
      break
    case 'week':
      carbonData.value = {
        today: baseCarbon.today * 7 * (0.8 + Math.random() * 0.4),
        month: baseCarbon.month * (0.8 + Math.random() * 0.4),
        year: baseCarbon.year * (0.8 + Math.random() * 0.4)
      }
      break
    case 'month':
      carbonData.value = {
        today: baseCarbon.today * 30 * (0.8 + Math.random() * 0.4),
        month: baseCarbon.month * (0.8 + Math.random() * 0.4),
        year: baseCarbon.year * (0.8 + Math.random() * 0.4)
      }
      break
    case 'year':
      carbonData.value = {
        today: baseCarbon.today * 365 * (0.8 + Math.random() * 0.4),
        month: baseCarbon.month * 12 * (0.8 + Math.random() * 0.4),
        year: baseCarbon.year * (0.8 + Math.random() * 0.4)
      }
      break
  }
  
  // 保留一位小数
  carbonData.value.today = Math.round(carbonData.value.today * 10) / 10
  carbonData.value.month = Math.round(carbonData.value.month * 10) / 10
  carbonData.value.year = Math.round(carbonData.value.year * 10) / 10
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
  // filteredDeviceData会自动更新，无需额外处理
}

// 处理状态筛选
const handleStatusFilter = () => {
  console.log('状态筛选:', statusFilter.value)
  // filteredDeviceData会自动更新，无需额外处理
}

// 导出数据
const exportData = () => {
  const [startDate, endDate] = selectedDateRange.value
  const dateRange = `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
  
  // 创建CSV内容
  let csvContent = "数据类型,数值,单位,变化趋势,变化率,对比周期\n"
  
  generationStats.value.forEach(stat => {
    csvContent += `${stat.name},${stat.value},${stat.unit},${stat.trend === 'up' ? '上升' : '下降'},${stat.changeRate}%,${stat.comparePeriod}\n`
  })
  
  csvContent += `\n抵碳量数据\n`
  csvContent += `今日抵碳量,${carbonData.value.today},kg\n`
  csvContent += `本月抵碳量,${carbonData.value.month},kg\n`
  csvContent += `年度抵碳量,${carbonData.value.year},kg\n`
  
  csvContent += "\n发电设备详情\n"
  csvContent += "设备ID,设备名称,设备类型,装机容量,当前功率,今日发电量,本月发电量,碳减排量,状态,效率\n"
  
  filteredDeviceData.value.forEach(device => {
    csvContent += `${device.deviceId},${device.deviceName},${device.deviceType},${device.installedCapacity},${device.currentPower},${device.todayGeneration},${device.monthGeneration},${device.carbonReduction},${device.status},${device.efficiency}%\n`
  })
  
  // 创建下载链接，添加BOM头支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `发电管理数据_${dateRange.replace(/\//g, '-')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  console.log('发电管理数据导出完成')
}

// 重置表单数据
const resetNewDevice = () => {
  newDevice.value = {
    deviceId: '',
    deviceName: '',
    deviceType: '',
    installedCapacity: 0,
    currentPower: 0,
    todayGeneration: 0,
    monthGeneration: 0,
    carbonReduction: 0,
    status: '正常',
    efficiency: 0
  }
}

// 添加设备
const addDevice = () => {
  resetNewDevice()
  showAddDeviceDialog.value = true
}

// 关闭添加设备对话框
const handleCloseAddDialog = () => {
  addDeviceFormRef.value?.resetFields()
  resetNewDevice()
  showAddDeviceDialog.value = false
}

// 处理添加设备
const handleAddDevice = () => {
  addDeviceFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      addDeviceLoading.value = true
      
      // 模拟异步添加设备
      setTimeout(() => {
        // 检查设备ID是否已存在
        const existingDevice = deviceData.value.find(device => device.deviceId === newDevice.value.deviceId)
        if (existingDevice) {
          ElMessage.error('设备ID已存在，请使用其他ID')
          addDeviceLoading.value = false
          return
        }
        
        // 添加设备到列表
        deviceData.value.push({ ...newDevice.value })
        
        // 显示成功消息
        ElMessage.success('设备添加成功！')
        
        // 关闭对话框
        handleCloseAddDialog()
        addDeviceLoading.value = false
        
        console.log('设备添加成功:', newDevice.value)
      }, 1000)
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 查看设备详情
const viewDevice = (device: any) => {
  console.log('查看设备详情:', device)
  // 这里可以打开设备详情对话框
}

// 编辑设备
const editDevice = (device: any) => {
  console.log('编辑设备:', device)
  // 这里可以打开编辑设备的对话框
}

onMounted(async () => {
  // 初始化数据
  await generateStatsByDateRange(selectedDateRange.value)
  updateCarbonData(chartTimeRange.value)
  
  // 初始化图表
  await nextTick(() => {
    initGenerationTrendChart()
    initDeviceStatusChart()
    initEfficiencyChart()
    initPredictionChart()
  })
})
</script>

<style scoped>
.generation-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.generation-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 10px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: #ebeef5;
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 50%;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.trend-carbon-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.trend-section {
  flex: 2;
  min-width: 500px;
}

.carbon-section {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.carbon-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.generation-trend-chart,
.device-status-chart,
.efficiency-analysis-chart,
.prediction-chart {
  height: 300px;
}



.carbon-calculation {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.carbon-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.carbon-label {
  font-size: 14px;
  color: #606266;
  font-weight: 600;
  flex: 1;
}

.carbon-value {
  font-size: 20px;
  font-weight: bold;
  color: #67c23a;
  margin: 0 15px;
}

.carbon-desc {
  font-size: 12px;
  color: #909399;
  flex: 1;
  text-align: right;
}

.prediction-section,
.suggestions-section {
  padding: 20px;
}

.prediction-section h4,
.suggestions-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #303133;
}

.suggestions-list {
  max-height: 300px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.suggestion-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.suggestion-desc {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generation-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .stat-item {
    flex: none;
    max-width: none;
    justify-content: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }
  
  .stat-item:not(:last-child)::after {
    display: none;
  }
  
  .stat-item:last-child {
    margin-bottom: 0;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .suggestion-item {
    flex-direction: column;
    text-align: center;
  }
  
  .suggestion-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style> 