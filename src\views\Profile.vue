<template>
  <div class="profile-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>个人信息</h3>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form :model="basicForm" label-width="100px" :rules="basicRules" ref="basicFormRef">
            <div class="avatar-container">
              <el-avatar :size="100" :src="basicForm.avatar">
                {{ userInitials }}
              </el-avatar>
              <el-button type="primary" size="small" class="avatar-upload-btn">
                更换头像
              </el-button>
            </div>
            
            <el-form-item label="用户名" prop="username">
              <el-input v-model="basicForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="姓名" prop="name">
              <el-input v-model="basicForm.name" />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="basicForm.email" />
            </el-form-item>
            
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="basicForm.phone" />
            </el-form-item>
            
            <el-form-item label="部门" prop="department">
              <el-input v-model="basicForm.department" />
            </el-form-item>
            
            <el-form-item label="职位" prop="position">
              <el-input v-model="basicForm.position" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveBasicInfo">保存</el-button>
              <el-button @click="resetBasicForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 修改密码 -->
        <el-tab-pane label="修改密码" name="password">
          <el-form :model="passwordForm" label-width="100px" :rules="passwordRules" ref="passwordFormRef">
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input v-model="passwordForm.currentPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input v-model="passwordForm.newPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="changePassword">确认修改</el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notification">
          <el-form :model="notificationForm" label-width="100px">
            <el-form-item label="系统通知">
              <el-switch v-model="notificationForm.systemNotification" />
            </el-form-item>
            
            <el-form-item label="设备告警">
              <el-switch v-model="notificationForm.deviceAlert" />
            </el-form-item>
            
            <el-form-item label="任务提醒">
              <el-switch v-model="notificationForm.taskReminder" />
            </el-form-item>
            
            <el-form-item label="报表生成">
              <el-switch v-model="notificationForm.reportGeneration" />
            </el-form-item>
            
            <el-form-item label="接收方式">
              <el-checkbox-group v-model="notificationForm.methods">
                <el-checkbox label="system">系统内</el-checkbox>
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings">保存</el-button>
              <el-button @click="resetNotificationForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '个人信息' }
])

// 当前激活的标签页
const activeTab = ref('basic')

// 用户名首字母（用于头像显示）
const userInitials = computed(() => {
  return basicForm.name ? basicForm.name.charAt(0).toUpperCase() : 'A'
})

// 基本信息表单
const basicFormRef = ref<FormInstance>()
const basicForm = reactive({
  username: 'admin',
  name: '管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  department: '运维部',
  position: '系统管理员',
  avatar: ''
})

// 基本信息验证规则
const basicRules: FormRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 密码表单
const passwordFormRef = ref<FormInstance>()
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const validateConfirmPassword = (_rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 通知设置表单
const notificationForm = reactive({
  systemNotification: true,
  deviceAlert: true,
  taskReminder: false,
  reportGeneration: true,
  methods: ['system', 'email']
})

// 保存基本信息
const saveBasicInfo = async () => {
  if (!basicFormRef.value) return
  
  await basicFormRef.value.validate((valid) => {
    if (valid) {
      // 这里可以调用API保存用户信息
      ElMessage.success('个人信息保存成功')
    }
  })
}

// 重置基本信息表单
const resetBasicForm = () => {
  basicFormRef.value?.resetFields()
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate((valid) => {
    if (valid) {
      // 这里可以调用API修改密码
      ElMessage.success('密码修改成功')
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
  })
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields()
}

// 保存通知设置
const saveNotificationSettings = () => {
  // 这里可以调用API保存通知设置
  ElMessage.success('通知设置保存成功')
}

// 重置通知设置表单
const resetNotificationForm = () => {
  notificationForm.systemNotification = true
  notificationForm.deviceAlert = true
  notificationForm.taskReminder = false
  notificationForm.reportGeneration = true
  notificationForm.methods = ['system', 'email']
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
  width: 100%;
}

.profile-card {
  width: 100%;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-upload-btn {
  margin-top: 10px;
}
</style> 