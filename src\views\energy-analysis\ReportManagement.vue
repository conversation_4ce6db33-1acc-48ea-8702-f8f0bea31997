<template>
  <div class="report-management" v-loading="loading" element-loading-text="正在处理报告数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 报告生成 -->
    <el-card class="report-generator">
      <template #header>
        <div class="card-header">
          <h3>报告生成</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="generateReport" :loading="generating">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新列表
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="setting-group">
            <label class="setting-label">报告类型：</label>
            <el-select v-model="reportConfig.type" placeholder="选择报告类型" size="small">
              <el-option label="能耗分析报告" value="energy-analysis" />
              <el-option label="节能评估报告" value="energy-saving" />
              <el-option label="设备运行报告" value="device-operation" />
              <el-option label="成本分析报告" value="cost-analysis" />
              <el-option label="碳排放报告" value="carbon-emission" />
              <el-option label="自定义报告" value="custom" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="setting-group">
            <label class="setting-label">报告周期：</label>
            <el-select v-model="reportConfig.period" placeholder="选择周期" size="small">
              <el-option label="日报告" value="daily" />
              <el-option label="周报告" value="weekly" />
              <el-option label="月报告" value="monthly" />
              <el-option label="季度报告" value="quarterly" />
              <el-option label="年度报告" value="yearly" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="setting-group">
            <label class="setting-label">时间范围：</label>
            <el-date-picker
              v-model="reportConfig.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
            />
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="setting-group">
            <label class="setting-label">报告格式：</label>
            <el-checkbox-group v-model="reportConfig.formats" size="small">
              <el-checkbox label="pdf">PDF</el-checkbox>
              <el-checkbox label="excel">Excel</el-checkbox>
              <el-checkbox label="word">Word</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="12">
          <div class="setting-group">
            <label class="setting-label">包含内容：</label>
            <el-checkbox-group v-model="reportConfig.contents" size="small">
              <el-checkbox label="summary">摘要总览</el-checkbox>
              <el-checkbox label="trends">趋势分析</el-checkbox>
              <el-checkbox label="comparison">对比分析</el-checkbox>
              <el-checkbox label="charts">图表数据</el-checkbox>
              <el-checkbox label="recommendations">优化建议</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-col>
        
        <el-col :span="12">
          <div class="setting-group">
            <label class="setting-label">数据范围：</label>
            <el-select 
              v-model="reportConfig.scope" 
              multiple 
              placeholder="选择数据范围" 
              size="small"
              collapse-tags
              style="width: 100%"
            >
              <el-option label="全部建筑" value="all-buildings" />
              <el-option label="办公大楼A" value="building-a" />
              <el-option label="办公大楼B" value="building-b" />
              <el-option label="生产车间" value="workshop" />
              <el-option label="配套设施" value="facilities" />
            </el-select>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 报告列表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>报告列表</h3>
          <div class="header-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索报告"
              size="small"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="statusFilter"
              placeholder="状态筛选"
              size="small"
              style="width: 120px; margin-right: 10px;"
              @change="handleStatusFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="生成中" value="generating" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
            <el-button type="danger" size="small" @click="batchDelete" :disabled="selectedReports.length === 0">
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredReports" 
        border 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        @selection-change="handleSelectionChange"
        height="400"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="报告名称" min-width="200" align="center" />
        <el-table-column prop="type" label="报告类型" min-width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getReportTypeColor(row.type)" size="small">
              {{ getReportTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="报告周期" min-width="100" align="center">
          <template #default="{ row }">
            {{ getPeriodName(row.period) }}
          </template>
        </el-table-column>
        <el-table-column prop="dateRange" label="时间范围" min-width="160" align="center" />
        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              <el-icon v-if="row.status === 'generating'" class="is-loading"><Loading /></el-icon>
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" min-width="120" align="center">
          <template #default="{ row }">
            <el-progress 
              v-if="row.status === 'generating'" 
              :percentage="row.progress" 
              :stroke-width="8"
              style="width: 90%"
            />
            <span v-else-if="row.status === 'completed'" style="color: #67c23a;">100%</span>
            <span v-else style="color: #f56c6c;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" min-width="100" align="center">
          <template #default="{ row }">
            <span v-if="row.status === 'completed'">{{ row.fileSize }}</span>
            <span v-else style="color: #909399;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="140" align="center" />
        <el-table-column label="操作" min-width="260" align="center" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="handlePreviewReport(row)"
              :disabled="row.status !== 'completed'"
              style="margin-right: 5px;"
            >
              预览
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="downloadReport(row)"
              :disabled="row.status !== 'completed'"
              style="margin-right: 5px;"
            >
              下载
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="editReport(row)"
              style="margin-right: 5px;"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteReport(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalReports"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 报告预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      title="报告预览"
      width="80%"
      :before-close="handleClosePreview"
    >
      <div v-if="previewReport">
        <div class="report-preview">
          <div class="report-header">
            <h2>{{ previewReport.name }}</h2>
            <div class="report-meta">
              <span>报告类型：{{ getReportTypeName(previewReport.type) }}</span>
              <span>生成时间：{{ previewReport.createTime }}</span>
              <span>时间范围：{{ previewReport.dateRange }}</span>
            </div>
          </div>
          
          <div class="report-content">
            <!-- 摘要总览 -->
            <div class="report-section" v-if="previewReport.contents?.includes('summary')">
              <h3>摘要总览</h3>
              <div class="summary-stats">
                <div class="stat-card">
                  <div class="stat-title">总能耗</div>
                  <div class="stat-value">1,256.8 t标煤</div>
                  <div class="stat-trend up">+3.2%</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">总费用</div>
                  <div class="stat-value">¥456,789</div>
                  <div class="stat-trend up">+4.1%</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">节能量</div>
                  <div class="stat-value">89.6 t标煤</div>
                  <div class="stat-trend down">+12.5%</div>
                </div>
                <div class="stat-card">
                  <div class="stat-title">碳排放</div>
                  <div class="stat-value">325.4 tCO₂</div>
                  <div class="stat-trend down">-2.8%</div>
                </div>
              </div>
            </div>
            
            <!-- 趋势分析 -->
            <div class="report-section" v-if="previewReport.contents?.includes('trends')">
              <h3>趋势分析</h3>
              <div ref="previewTrendChart" style="height: 350px;"></div>
            </div>
            
            <!-- 对比分析 -->
            <div class="report-section" v-if="previewReport.contents?.includes('comparison')">
              <h3>对比分析</h3>
              <div ref="previewComparisonChart" style="height: 350px;"></div>
            </div>
            
            <!-- 优化建议 -->
            <div class="report-section" v-if="previewReport.contents?.includes('recommendations')">
              <h3>优化建议</h3>
              <div class="recommendations">
                <div class="recommendation-item">
                  <el-icon color="#67c23a"><SuccessFilled /></el-icon>
                  <div class="content">
                    <h4>空调系统优化</h4>
                    <p>建议在非工作时段降低空调设定温度，预计可节能15-20%，年节约费用约8万元。</p>
                  </div>
                </div>
                <div class="recommendation-item">
                  <el-icon color="#e6a23c"><WarningFilled /></el-icon>
                  <div class="content">
                    <h4>照明系统改造</h4>
                    <p>建议将传统荧光灯更换为LED灯具，预计可节能30-40%，投资回收期约2年。</p>
                  </div>
                </div>
                <div class="recommendation-item">
                  <el-icon color="#409eff"><InfoFilled /></el-icon>
                  <div class="content">
                    <h4>能耗监控加强</h4>
                    <p>建议增加重点用能设备的实时监控点，提高能耗数据的精确度和实时性。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showPreviewDialog = false">关闭</el-button>
        <el-button type="primary" @click="downloadCurrentReport">下载报告</el-button>
      </template>
    </el-dialog>

    <!-- 编辑报告对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑报告"
      width="60%"
      :before-close="handleCloseEdit"
    >
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="报告名称">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="报告类型">
          <el-select v-model="editForm.type" style="width: 100%">
            <el-option label="能耗分析报告" value="energy-analysis" />
            <el-option label="节能评估报告" value="energy-saving" />
            <el-option label="设备运行报告" value="device-operation" />
            <el-option label="成本分析报告" value="cost-analysis" />
            <el-option label="碳排放报告" value="carbon-emission" />
          </el-select>
        </el-form-item>
        <el-form-item label="报告周期">
          <el-select v-model="editForm.period" style="width: 100%">
            <el-option label="日报告" value="daily" />
            <el-option label="周报告" value="weekly" />
            <el-option label="月报告" value="monthly" />
            <el-option label="季度报告" value="quarterly" />
            <el-option label="年度报告" value="yearly" />
          </el-select>
        </el-form-item>
        <el-form-item label="包含内容">
          <el-checkbox-group v-model="editForm.contents">
            <el-checkbox label="summary">摘要总览</el-checkbox>
            <el-checkbox label="trends">趋势分析</el-checkbox>
            <el-checkbox label="comparison">对比分析</el-checkbox>
            <el-checkbox label="charts">图表数据</el-checkbox>
            <el-checkbox label="recommendations">优化建议</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveReport">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { 
  Refresh, Download, Search, Document, Delete, 
  Loading, SuccessFilled, WarningFilled, InfoFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '报告管理', to: '' }
])

// 基础数据
const loading = ref(false)
const generating = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const selectedReports = ref<any[]>([])
const showPreviewDialog = ref(false)
const showEditDialog = ref(false)
const previewReport = ref<any>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const totalReports = ref(0)

// 图表引用
const previewTrendChart = ref<HTMLElement>()
const previewComparisonChart = ref<HTMLElement>()

// 报告配置
const reportConfig = ref({
  type: 'energy-analysis',
  period: 'monthly',
  dateRange: null as [Date, Date] | null,
  formats: ['pdf'],
  contents: ['summary', 'trends', 'comparison', 'charts'],
  scope: ['all-buildings']
})

// 编辑表单
const editForm = ref({
  id: '',
  name: '',
  type: '',
  period: '',
  contents: []
})

// 报告列表数据
const reportsData = ref([
  {
    id: '1',
    name: '2024年1月能耗分析报告',
    type: 'energy-analysis',
    period: 'monthly',
    dateRange: '2024-01-01 至 2024-01-31',
    status: 'completed',
    progress: 100,
    fileSize: '2.5MB',
    createTime: '2024-01-31 18:30:00',
    contents: ['summary', 'trends', 'comparison', 'charts', 'recommendations']
  },
  {
    id: '2',
    name: '2024年Q1节能评估报告',
    type: 'energy-saving',
    period: 'quarterly',
    dateRange: '2024-01-01 至 2024-03-31',
    status: 'generating',
    progress: 78,
    fileSize: '-',
    createTime: '2024-03-31 16:45:00',
    contents: ['summary', 'trends', 'recommendations']
  },
  {
    id: '3',
    name: '设备运行周报',
    type: 'device-operation',
    period: 'weekly',
    dateRange: '2024-01-22 至 2024-01-28',
    status: 'completed',
    progress: 100,
    fileSize: '1.8MB',
    createTime: '2024-01-28 20:15:00',
    contents: ['summary', 'charts']
  },
  {
    id: '4',
    name: '2023年度碳排放报告',
    type: 'carbon-emission',
    period: 'yearly',
    dateRange: '2023-01-01 至 2023-12-31',
    status: 'failed',
    progress: 0,
    fileSize: '-',
    createTime: '2024-01-15 14:20:00',
    contents: ['summary', 'trends', 'comparison']
  },
  {
    id: '5',
    name: '成本分析月报',
    type: 'cost-analysis',
    period: 'monthly',
    dateRange: '2024-01-01 至 2024-01-31',
    status: 'completed',
    progress: 100,
    fileSize: '3.2MB',
    createTime: '2024-01-31 22:10:00',
    contents: ['summary', 'trends', 'comparison', 'charts']
  }
])

// 过滤后的报告数据
const filteredReports = computed(() => {
  let filtered = reportsData.value
  
  if (searchKeyword.value) {
    filtered = filtered.filter(report => 
      report.name.includes(searchKeyword.value) ||
      getReportTypeName(report.type).includes(searchKeyword.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(report => report.status === statusFilter.value)
  }
  
  totalReports.value = filtered.length
  
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 工具函数
const getReportTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'energy-analysis': '能耗分析',
    'energy-saving': '节能评估',
    'device-operation': '设备运行',
    'cost-analysis': '成本分析',
    'carbon-emission': '碳排放',
    'custom': '自定义'
  }
  return typeMap[type] || type
}

const getReportTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'energy-analysis': 'primary',
    'energy-saving': 'success',
    'device-operation': 'warning',
    'cost-analysis': 'info',
    'carbon-emission': 'danger',
    'custom': ''
  }
  return colorMap[type] || ''
}

const getPeriodName = (period: string) => {
  const periodMap: Record<string, string> = {
    'daily': '日报告',
    'weekly': '周报告',
    'monthly': '月报告',
    'quarterly': '季度报告',
    'yearly': '年度报告'
  }
  return periodMap[period] || period
}

const getStatusName = (status: string) => {
  const statusMap: Record<string, string> = {
    'generating': '生成中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'generating': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status] || ''
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
}

const handleStatusFilter = () => {
  currentPage.value = 1
}

const handleSelectionChange = (selection: any[]) => {
  selectedReports.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 生成报告
const generateReport = async () => {
  if (!reportConfig.value.type) {
    ElMessage.warning('请选择报告类型')
    return
  }
  
  if (!reportConfig.value.period) {
    ElMessage.warning('请选择报告周期')
    return
  }
  
  if (reportConfig.value.formats.length === 0) {
    ElMessage.warning('请选择报告格式')
    return
  }
  
  try {
    generating.value = true
    
    // 模拟报告生成过程
    const newReport = {
      id: Date.now().toString(),
      name: `${getReportTypeName(reportConfig.value.type)}_${new Date().toISOString().slice(0, 10)}`,
      type: reportConfig.value.type,
      period: reportConfig.value.period,
      dateRange: reportConfig.value.dateRange ? 
        `${reportConfig.value.dateRange[0].toISOString().slice(0, 10)} 至 ${reportConfig.value.dateRange[1].toISOString().slice(0, 10)}` :
        '自定义范围',
      status: 'generating',
      progress: 0,
      fileSize: '-',
      createTime: new Date().toLocaleString(),
      contents: reportConfig.value.contents
    }
    
    reportsData.value.unshift(newReport)
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      const report = reportsData.value.find(r => r.id === newReport.id)
      if (report && report.progress < 100) {
        report.progress += Math.random() * 20
        if (report.progress >= 100) {
          report.progress = 100
          report.status = 'completed'
          report.fileSize = (Math.random() * 3 + 1).toFixed(1) + 'MB'
          clearInterval(progressInterval)
          ElMessage.success('报告生成完成！')
        }
      }
    }, 500)
    
    ElMessage.success('报告生成任务已启动')
  } catch (error) {
    ElMessage.error('报告生成失败')
  } finally {
    generating.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 800))
    ElMessage.success('数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 预览报告
const handlePreviewReport = (report: any) => {
  if (report.status !== 'completed') {
    ElMessage.warning('报告尚未生成完成')
    return
  }
  
  previewReport.value = report
  showPreviewDialog.value = true
  
  nextTick(() => {
    if (report.contents?.includes('trends')) {
      initPreviewTrendChart()
    }
    if (report.contents?.includes('comparison')) {
      initPreviewComparisonChart()
    }
  })
}

// 下载报告
const downloadReport = (report: any) => {
  if (report.status !== 'completed') {
    ElMessage.warning('报告尚未生成完成')
    return
  }
  
  // 模拟下载
  const link = document.createElement('a')
  link.download = `${report.name}.pdf`
  link.href = '#' // 实际项目中这里应该是真实的下载链接
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('报告下载开始')
}

// 编辑报告
const editReport = (report: any) => {
  editForm.value = {
    id: report.id,
    name: report.name,
    type: report.type,
    period: report.period,
    contents: report.contents || []
  }
  showEditDialog.value = true
}

// 保存报告
const saveReport = () => {
  const index = reportsData.value.findIndex(r => r.id === editForm.value.id)
  if (index !== -1) {
    reportsData.value[index] = {
      ...reportsData.value[index],
      name: editForm.value.name,
      type: editForm.value.type,
      period: editForm.value.period,
      contents: editForm.value.contents
    }
    ElMessage.success('报告保存成功')
    showEditDialog.value = false
  }
}

// 删除报告
const deleteReport = async (report: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报告吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = reportsData.value.findIndex(r => r.id === report.id)
    if (index !== -1) {
      reportsData.value.splice(index, 1)
      ElMessage.success('报告删除成功')
    }
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedReports.value.length === 0) {
    ElMessage.warning('请选择要删除的报告')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedReports.value.length} 个报告吗？`, '批量删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const idsToDelete = selectedReports.value.map(r => r.id)
    reportsData.value = reportsData.value.filter(r => !idsToDelete.includes(r.id))
    selectedReports.value = []
    
    ElMessage.success('批量删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}

// 关闭预览
const handleClosePreview = () => {
  showPreviewDialog.value = false
  previewReport.value = null
}

// 关闭编辑
const handleCloseEdit = () => {
  showEditDialog.value = false
}

// 下载当前预览的报告
const downloadCurrentReport = () => {
  if (previewReport.value) {
    downloadReport(previewReport.value)
  }
}

// 初始化预览趋势图表
const initPreviewTrendChart = () => {
  if (!previewTrendChart.value) return
  
  const chart = echarts.init(previewTrendChart.value)
  
  const option = {
    title: { text: '能耗趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['用电量', '用水量'], top: 30 },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '用电量',
        type: 'line',
        data: [186.4, 172.8, 195.2, 178.6, 203.1, 189.7],
        itemStyle: { color: '#409eff' }
      },
      {
        name: '用水量',
        type: 'line',
        data: [520, 485, 562, 498, 578, 534],
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化预览对比图表
const initPreviewComparisonChart = () => {
  if (!previewComparisonChart.value) return
  
  const chart = echarts.init(previewComparisonChart.value)
  
  const option = {
    title: { text: '建筑能耗对比', left: 'center' },
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    legend: { data: ['能耗'], top: 30 },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['办公大楼A', '办公大楼B', '生产车间', '配套设施']
    },
    yAxis: { type: 'value', name: '能耗(t标煤)' },
    series: [{
      name: '能耗',
      type: 'bar',
      data: [42.6, 32.8, 68.4, 18.9],
      itemStyle: { color: '#409eff' }
    }]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  // 设置默认日期范围
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  reportConfig.value.dateRange = [lastMonth, now]
  
  totalReports.value = reportsData.value.length
})
</script>

<style scoped>
.report-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.report-generator {
  margin-bottom: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.report-preview {
  max-height: 70vh;
  overflow-y: auto;
}

.report-header {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.report-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.report-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.report-section {
  margin-bottom: 30px;
}

.report-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-trend {
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #f56c6c;
}

.stat-trend.down {
  color: #67c23a;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.recommendation-item .content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.recommendation-item .content p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .setting-group {
    margin-bottom: 15px;
  }
  
  .summary-stats {
    grid-template-columns: 1fr;
  }
  
  .report-meta {
    flex-direction: column;
    gap: 5px;
  }
}
</style> 