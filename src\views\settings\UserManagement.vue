<template>
  <div class="settings-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>用户管理</h3>
        </div>
      </template>
      
      <div class="user-actions">
        <el-button type="primary" @click="showAddUserDialog">添加用户</el-button>
      </div>
      
      <el-table :data="users" border style="width: 100%" class="user-table">
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag :type="getRoleType(scope.row.role)">{{ getRoleName(scope.row.role) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLogin" label="最后登录" width="160" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" link size="small" @click="handleEditUser(scope.row)">
              编辑
            </el-button>
            <el-button 
              type="primary" 
              link 
              size="small" 
              @click="handleToggleUserStatus(scope.row)"
            >
              {{ scope.row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteUser(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      :title="userDialogType === 'add' ? '添加用户' : '编辑用户'"
      v-model="userDialogVisible"
      width="500px"
    >
      <el-form :model="userForm" label-width="80px" :rules="userRules" ref="userFormRef">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="userDialogType === 'edit'" />
        </el-form-item>
        
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="操作员" value="operator" />
            <el-option label="访客" value="guest" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="userDialogType === 'add'">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="userForm.status"
            active-value="active"
            inactive-value="inactive"
            :active-text="userForm.status === 'active' ? '启用' : ''"
            :inactive-text="userForm.status === 'inactive' ? '禁用' : ''"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUserForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '系统设置' },
  { text: '用户管理' }
])

// 用户列表
const users = ref([
  {
    id: 1,
    username: 'admin',
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    lastLogin: '2025-07-12 10:30:45',
    status: 'active'
  },
  {
    id: 2,
    username: 'operator1',
    name: '张三',
    email: '<EMAIL>',
    role: 'operator',
    lastLogin: '2025-07-11 16:45:22',
    status: 'active'
  },
  {
    id: 3,
    username: 'guest1',
    name: '李四',
    email: '<EMAIL>',
    role: 'guest',
    lastLogin: '2025-07-10 09:12:35',
    status: 'inactive'
  }
])

// 用户对话框
const userDialogVisible = ref(false)
const userDialogType = ref<'add' | 'edit'>('add')
const userFormRef = ref<FormInstance>()
const userForm = reactive({
  id: 0,
  username: '',
  name: '',
  email: '',
  role: 'operator',
  password: '',
  status: 'active'
})

// 用户表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 显示添加用户对话框
const showAddUserDialog = () => {
  userDialogType.value = 'add'
  resetUserForm()
  userDialogVisible.value = true
}

// 处理编辑用户
const handleEditUser = (user: any) => {
  userDialogType.value = 'edit'
  Object.assign(userForm, user)
  userDialogVisible.value = true
}

// 处理切换用户状态
const handleToggleUserStatus = (user: any) => {
  const newStatus = user.status === 'active' ? 'inactive' : 'active'
  const actionText = newStatus === 'active' ? '启用' : '禁用'
  
  ElMessageBox.confirm(`确定要${actionText}用户 "${user.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里应该调用API更新用户状态
    user.status = newStatus
    ElMessage.success(`用户已${actionText}`)
  }).catch(() => {})
}

// 处理删除用户
const handleDeleteUser = (user: any) => {
  ElMessageBox.confirm(`确定要删除用户 "${user.name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里应该调用API删除用户
    users.value = users.value.filter(u => u.id !== user.id)
    ElMessage.success('用户已删除')
  }).catch(() => {})
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate((valid) => {
    if (valid) {
      if (userDialogType.value === 'add') {
        // 添加用户
        const newUser = {
          id: Date.now(),
          username: userForm.username,
          name: userForm.name,
          email: userForm.email,
          role: userForm.role,
          lastLogin: '-',
          status: userForm.status
        }
        users.value.push(newUser)
        ElMessage.success('用户添加成功')
      } else {
        // 编辑用户
        const index = users.value.findIndex(u => u.id === userForm.id)
        if (index !== -1) {
          users.value[index] = { ...users.value[index], ...userForm }
          ElMessage.success('用户编辑成功')
        }
      }
      userDialogVisible.value = false
    }
  })
}

// 重置用户表单
const resetUserForm = () => {
  userForm.id = 0
  userForm.username = ''
  userForm.name = ''
  userForm.email = ''
  userForm.role = 'operator'
  userForm.password = ''
  userForm.status = 'active'
}

// 获取角色类型
const getRoleType = (role: string) => {
  const roleMap: Record<string, string> = {
    'admin': 'danger',
    'operator': 'warning',
    'guest': 'info'
  }
  return roleMap[role] || 'info'
}

// 获取角色名称
const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    'admin': '管理员',
    'operator': '操作员',
    'guest': '访客'
  }
  return roleMap[role] || role
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  width: 100%;
}

.settings-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-actions {
  margin-bottom: 20px;
}

.user-table {
  margin-bottom: 20px;
}
</style> 