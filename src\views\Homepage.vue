<template>
  <div class="homepage">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 欢迎区域 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <div class="welcome-content">
            <div class="welcome-text">
              <h1>欢迎使用能耗管理系统</h1>
              <p>{{ currentTime }}</p>
              <p>为您提供全面的能耗监控、分析和管理服务</p>
            </div>
            <div class="welcome-icon">
              <el-icon size="80"><Monitor /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统概览 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="6" v-for="(item, index) in systemOverview" :key="index">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-content">
            <div class="overview-icon" :class="item.type">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-title">{{ item.title }}</div>
              <div class="overview-value">{{ item.value }}</div>
              <div class="overview-desc">{{ item.description }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷功能 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <el-card class="quick-actions-card">
          <template #header>
            <h3>快捷功能</h3>
          </template>
          <div class="quick-actions">
            <el-button 
              v-for="action in quickActions" 
              :key="action.name"
              :type="action.type"
              :icon="action.icon"
              size="large"
              @click="handleQuickAction(action.route)"
              class="quick-action-btn"
            >
              {{ action.name }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card class="status-card">
          <template #header>
            <h3>系统状态</h3>
          </template>
          <div class="status-list">
            <div v-for="status in systemStatus" :key="status.name" class="status-item">
              <div class="status-info">
                <span class="status-name">{{ status.name }}</span>
                <el-tag :type="status.status === '正常' ? 'success' : 'danger'">
                  {{ status.status }}
                </el-tag>
              </div>
              <div class="status-desc">{{ status.description }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="notice-card">
          <template #header>
            <h3>系统公告</h3>
          </template>
          <div class="notice-list">
            <div v-for="notice in systemNotices" :key="notice.id" class="notice-item">
              <div class="notice-header">
                <span class="notice-title">{{ notice.title }}</span>
                <span class="notice-date">{{ notice.date }}</span>
              </div>
              <div class="notice-content">{{ notice.content }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import BreadCrumb from '@/components/BreadCrumb.vue'
import { 
  Monitor, 
  Setting, 
  User, 
  DocumentCopy,
  InfoFilled,
  SuccessFilled,
  WarningFilled
} from '@element-plus/icons-vue'

const router = useRouter()

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '首页' }
])

// 当前时间
const currentTime = ref('')
let timeInterval: number

// 系统概览数据
const systemOverview = ref([
  {
    title: '在线设备',
    value: '42',
    description: '设备总数: 45',
    icon: 'Monitor',
    type: 'primary'
  },
  {
    title: '系统用户',
    value: '8',
    description: '活跃用户: 6',
    icon: 'User',
    type: 'success'
  },
  {
    title: '告警数量',
    value: '3',
    description: '待处理: 1',
    icon: 'WarningFilled',
    type: 'warning'
  },
  {
    title: '系统版本',
    value: 'v1.0.0',
    description: '最新版本',
    icon: 'InfoFilled',
    type: 'info'
  }
])

// 快捷功能
const quickActions = ref([
  {
    name: '基础设置',
    route: '/settings/basic',
    type: 'primary',
    icon: Setting
  },
  {
    name: '告警设置',
    route: '/settings/alert',
    type: 'warning',
    icon: WarningFilled
  },
  {
    name: '用户管理',
    route: '/settings/user',
    type: 'success',
    icon: User
  },
  {
    name: '数据设置',
    route: '/settings/data',
    type: 'info',
    icon: DocumentCopy
  }
])

// 系统状态
const systemStatus = ref([
  {
    name: '数据库连接',
    status: '正常',
    description: '响应时间: 12ms'
  },
  {
    name: '设备通信',
    status: '正常',
    description: '连接设备: 42/45'
  },
  {
    name: '系统负载',
    status: '正常',
    description: 'CPU: 23%, 内存: 45%'
  },
  {
    name: '存储空间',
    status: '正常',
    description: '已使用: 65%'
  }
])

// 系统公告
const systemNotices = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于本周日凌晨2:00-4:00进行例行维护，期间可能影响部分功能使用。',
    date: '2025-07-12'
  },
  {
    id: 2,
    title: '新功能上线',
    content: '智能分析功能已上线，可在相关模块中体验新的数据分析能力。',
    date: '2025-07-10'
  },
  {
    id: 3,
    title: '安全更新',
    content: '系统已完成安全补丁更新，进一步提升了系统安全性。',
    date: '2025-07-08'
  }
])

// 更新当前时间
const updateTime = () => {
  const now = new Date()
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    weekday: 'long'
  }
  currentTime.value = now.toLocaleDateString('zh-CN', options)
}

// 处理快捷功能点击
const handleQuickAction = (route: string) => {
  router.push(route)
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.homepage {
  padding: 0;
}

.mt-20 {
  margin-top: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.welcome-card :deep(.el-card__body) {
  padding: 40px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 28px;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.welcome-text p {
  margin: 5px 0;
  font-size: 16px;
  opacity: 0.9;
}

.welcome-icon {
  opacity: 0.3;
}

.overview-card {
  height: 120px;
  transition: transform 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-5px);
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.overview-icon.primary {
  background-color: #409eff;
}

.overview-icon.success {
  background-color: #67c23a;
}

.overview-icon.warning {
  background-color: #e6a23c;
}

.overview-icon.info {
  background-color: #909399;
}

.overview-info {
  flex: 1;
}

.overview-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-desc {
  font-size: 12px;
  color: #909399;
}

.quick-actions-card h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quick-action-btn {
  flex: 1;
  min-width: 120px;
  height: 50px;
}

.status-card h3,
.notice-card h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.status-item:last-child {
  border-bottom: none;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.status-name {
  font-weight: 500;
  color: #303133;
}

.status-desc {
  font-size: 12px;
  color: #909399;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notice-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.notice-title {
  font-weight: 500;
  color: #303133;
}

.notice-date {
  font-size: 12px;
  color: #909399;
}

.notice-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-text {
    margin-bottom: 20px;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-action-btn {
    width: 100%;
  }
}
</style> 