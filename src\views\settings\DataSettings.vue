<template>
  <div class="settings-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>数据设置</h3>
        </div>
      </template>
      
      <el-form :model="dataSettings" label-width="180px">
        <el-form-item label="数据采集频率 (秒)">
          <el-input-number v-model="dataSettings.collectionInterval" :min="5" :max="3600" />
        </el-form-item>
        
        <el-form-item label="数据存储周期">
          <el-select v-model="dataSettings.retentionPeriod" style="width: 100%">
            <el-option label="1个月" value="1month" />
            <el-option label="3个月" value="3months" />
            <el-option label="6个月" value="6months" />
            <el-option label="1年" value="1year" />
            <el-option label="永久" value="forever" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据备份">
          <el-switch v-model="dataSettings.enableBackup" />
        </el-form-item>
        
        <el-form-item label="备份频率" v-if="dataSettings.enableBackup">
          <el-select v-model="dataSettings.backupFrequency" style="width: 100%">
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveDataSettings">保存设置</el-button>
          <el-button @click="resetDataSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '系统设置' },
  { text: '数据设置' }
])

// 数据设置
const dataSettings = reactive({
  collectionInterval: 30,
  retentionPeriod: '3months',
  enableBackup: true,
  backupFrequency: 'daily'
})

// 保存数据设置
const saveDataSettings = () => {
  ElMessage.success('数据设置保存成功')
}

// 重置数据设置
const resetDataSettings = () => {
  dataSettings.collectionInterval = 30
  dataSettings.retentionPeriod = '3months'
  dataSettings.enableBackup = true
  dataSettings.backupFrequency = 'daily'
  ElMessage.info('数据设置已重置')
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  width: 100%;
}

.settings-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}
</style> 