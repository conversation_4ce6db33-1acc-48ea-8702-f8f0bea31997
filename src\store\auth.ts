import { defineStore } from 'pinia'
import { UserInfo } from '@/types'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as UserInfo | null,
    token: localStorage.getItem('token') || '',
    isAuthenticated: !!localStorage.getItem('token')
  }),
  
  actions: {
    setUser(user: UserInfo) {
      this.user = user
    },
    
    setToken(token: string) {
      this.token = token
      this.isAuthenticated = true
      localStorage.setItem('token', token)
    },
    
    logout() {
      this.user = null
      this.token = ''
      this.isAuthenticated = false
      localStorage.removeItem('token')
    }
  },
  
  getters: {
    userInfo: (state) => state.user,
    isLoggedIn: (state) => state.isAuthenticated
  }
}) 