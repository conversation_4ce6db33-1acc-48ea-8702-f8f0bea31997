<template>
  <div class="exception-rules" v-loading="loading" element-loading-text="正在加载异常规则...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 规则管理操作 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>异常规则管理</h3>
          <div class="header-controls">
            <el-select v-model="selectedBuilding" size="small" placeholder="选择建筑" style="width: 120px;" @change="handleBuildingChange">
              <el-option label="全部建筑" value="" />
              <el-option label="办公大楼A" value="building-a" />
              <el-option label="办公大楼B" value="building-b" />
              <el-option label="生产车间" value="workshop" />
              <el-option label="配套设施" value="facilities" />
            </el-select>
            <el-button type="primary" size="small" @click="addRule">
              <el-icon><Plus /></el-icon>
              新建规则
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <label>规则类型：</label>
            <el-select v-model="filters.ruleType" placeholder="请选择规则类型" size="small" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="阈值规则" value="threshold" />
              <el-option label="趋势规则" value="trend" />
              <el-option label="时间规则" value="time" />
              <el-option label="组合规则" value="combination" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>规则状态：</label>
            <el-select v-model="filters.status" placeholder="请选择规则状态" size="small" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="启用" value="enabled" />
              <el-option label="禁用" value="disabled" />
              <el-option label="草稿" value="draft" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>规则级别：</label>
            <el-select v-model="filters.severity" placeholder="请选择规则级别" size="small" clearable @change="handleFilterChange">
              <el-option label="全部级别" value="" />
              <el-option label="紧急" value="critical" />
              <el-option label="警告" value="warning" />
              <el-option label="提醒" value="info" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>关键字搜索：</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="规则名称、描述" 
              size="small" 
              clearable
              @input="handleKeywordSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 规则统计概览 -->
    <el-card class="overview-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>规则统计概览</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="exportRules">
              <el-icon><Download /></el-icon>
              导出规则
            </el-button>
            <el-button type="warning" size="small" @click="importRules">
              <el-icon><Upload /></el-icon>
              导入规则
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="rules-stats">
        <div class="stat-item" v-for="(item, index) in rulesStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-value-container">
              <span class="stat-value">{{ item.value }}</span>
              <span class="stat-unit">{{ item.unit }}</span>
            </div>
            <div class="stat-trend" :style="{ color: item.trendColor }">{{ item.trend }}</div>
          </div>
          <div class="stat-divider" v-if="index < rulesStats.length - 1"></div>
        </div>
      </div>
    </el-card>

    <!-- 规则效果分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>规则触发趋势</h3>
              <div class="header-controls">
                <el-radio-group v-model="chartTimeRange" size="small" @change="handleChartTimeRangeChange">
                  <el-radio-button label="7d">近7天</el-radio-button>
                  <el-radio-button label="30d">近30天</el-radio-button>
                  <el-radio-button label="90d">近3个月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="ruleTriggerChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>规则类型分布</h3>
            </div>
          </template>
          <div ref="ruleTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 异常规则列表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>异常规则列表</h3>
          <div class="header-controls">
            <span class="record-count">共 {{ filteredRules.length }} 条规则</span>
            <el-button type="danger" size="small" @click="batchDelete" :disabled="selectedRules.length === 0">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedRules.length }})
            </el-button>
            <el-button type="warning" size="small" @click="batchToggle" :disabled="selectedRules.length === 0">
              <el-icon><Switch /></el-icon>
              批量启停 ({{ selectedRules.length }})
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="paginatedRules" 
        border 
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="规则ID" width="120" align="center" />
        <el-table-column prop="name" label="规则名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="ruleType" label="规则类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRuleTypeColor(row.ruleType)" size="small">
              {{ getRuleTypeName(row.ruleType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="targetDevice" label="监控对象" min-width="120" show-overflow-tooltip />
        <el-table-column prop="condition" label="触发条件" min-width="150" show-overflow-tooltip />
        <el-table-column prop="severity" label="告警级别" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getSeverityColor(row.severity)" size="small">
              {{ getSeverityName(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch 
              v-model="row.isEnabled" 
              :active-value="true" 
              :inactive-value="false"
              @change="toggleRuleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="triggerCount" label="触发次数" width="100" align="center">
          <template #default="{ row }">
            <span :style="{ color: row.triggerCount > 0 ? '#f56c6c' : '#909399', fontWeight: 'bold' }">
              {{ row.triggerCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="160" align="center" />
        <el-table-column prop="creator" label="创建人" width="100" align="center" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div style="white-space: nowrap;">
              <el-button type="primary" size="small" @click="editRule(row)" style="margin-right: 5px;">
                编辑
              </el-button>
              <el-button type="info" size="small" @click="testRule(row)" style="margin-right: 5px;">
                测试
              </el-button>
              <el-button type="danger" size="small" @click="deleteRule(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredRules.length"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 规则配置对话框 -->
    <el-dialog
      v-model="showRuleDialog"
      :title="isEditing ? '编辑规则' : '新建规则'"
      width="70%"
      :before-close="handleCloseRule"
    >
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规则名称" prop="name">
              <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则类型" prop="ruleType">
              <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
                <el-option label="阈值规则" value="threshold" />
                <el-option label="趋势规则" value="trend" />
                <el-option label="时间规则" value="time" />
                <el-option label="组合规则" value="combination" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="监控对象" prop="targetDevice">
              <el-select v-model="ruleForm.targetDevice" placeholder="请选择监控设备" style="width: 100%">
                <el-option label="空调主机-01" value="AC001" />
                <el-option label="照明控制器-02" value="LC002" />
                <el-option label="电表-A101" value="EM101" />
                <el-option label="温度传感器-05" value="TS005" />
                <el-option label="变压器-主01" value="TR001" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="告警级别" prop="severity">
              <el-select v-model="ruleForm.severity" placeholder="请选择告警级别" style="width: 100%">
                <el-option label="紧急" value="critical" />
                <el-option label="警告" value="warning" />
                <el-option label="提醒" value="info" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="规则描述" prop="description">
          <el-input 
            v-model="ruleForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入规则描述"
          />
        </el-form-item>
        
        <!-- 阈值规则配置 -->
        <div v-if="ruleForm.ruleType === 'threshold'">
          <el-divider content-position="left">阈值配置</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="监控指标">
                <el-select v-model="ruleForm.config.metric" placeholder="请选择监控指标" style="width: 100%">
                  <el-option label="功率(kW)" value="power" />
                  <el-option label="电流(A)" value="current" />
                  <el-option label="电压(V)" value="voltage" />
                  <el-option label="温度(°C)" value="temperature" />
                  <el-option label="湿度(%)" value="humidity" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="条件">
                <el-select v-model="ruleForm.config.operator" placeholder="请选择条件" style="width: 100%">
                  <el-option label="大于" value=">" />
                  <el-option label="小于" value="<" />
                  <el-option label="等于" value="=" />
                  <el-option label="大于等于" value=">=" />
                  <el-option label="小于等于" value="<=" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阈值">
                <el-input-number v-model="ruleForm.config.threshold" style="width: 100%" :precision="2" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="持续时间">
                <el-input-number v-model="ruleForm.config.duration" style="width: 100%" :min="1" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检查频率">
                <el-input-number v-model="ruleForm.config.checkInterval" style="width: 100%" :min="1" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 趋势规则配置 -->
        <div v-if="ruleForm.ruleType === 'trend'">
          <el-divider content-position="left">趋势配置</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="趋势方向">
                <el-select v-model="ruleForm.config.trendDirection" placeholder="请选择趋势方向" style="width: 100%">
                  <el-option label="上升" value="up" />
                  <el-option label="下降" value="down" />
                  <el-option label="波动" value="fluctuation" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="变化幅度">
                <el-input-number v-model="ruleForm.config.changeRate" style="width: 100%" :precision="1" />
                <span style="margin-left: 8px;">%</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="时间窗口">
                <el-input-number v-model="ruleForm.config.timeWindow" style="width: 100%" :min="5" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        
        <!-- 时间规则配置 -->
        <div v-if="ruleForm.ruleType === 'time'">
          <el-divider content-position="left">时间配置</el-divider>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生效时间">
                <el-time-picker
                  v-model="ruleForm.config.startTime"
                  placeholder="选择开始时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="失效时间">
                <el-time-picker
                  v-model="ruleForm.config.endTime"
                  placeholder="选择结束时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="生效日期">
            <el-checkbox-group v-model="ruleForm.config.weekdays">
              <el-checkbox label="1">周一</el-checkbox>
              <el-checkbox label="2">周二</el-checkbox>
              <el-checkbox label="3">周三</el-checkbox>
              <el-checkbox label="4">周四</el-checkbox>
              <el-checkbox label="5">周五</el-checkbox>
              <el-checkbox label="6">周六</el-checkbox>
              <el-checkbox label="0">周日</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        
        <el-divider content-position="left">通知配置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="通知方式">
              <el-checkbox-group v-model="ruleForm.notificationMethods">
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
                <el-checkbox label="webhook">Webhook</el-checkbox>
                <el-checkbox label="app">App推送</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知人员">
              <el-select v-model="ruleForm.notificationUsers" multiple placeholder="请选择通知人员" style="width: 100%">
                <el-option label="张工程师" value="zhang" />
                <el-option label="李技术员" value="li" />
                <el-option label="王主管" value="wang" />
                <el-option label="刘管理员" value="liu" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="静默时间">
              <el-input-number v-model="ruleForm.silentPeriod" style="width: 100%" :min="0" />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="立即启用">
              <el-switch v-model="ruleForm.isEnabled" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <el-button @click="showRuleDialog = false">取消</el-button>
        <el-button type="info" @click="testRuleConfig">测试规则</el-button>
        <el-button type="primary" @click="saveRule">保存规则</el-button>
      </template>
    </el-dialog>

    <!-- 规则测试对话框 -->
    <el-dialog
      v-model="showTestDialog"
      title="规则测试"
      width="50%"
    >
      <div v-if="selectedRule">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="规则名称">{{ selectedRule.name }}</el-descriptions-item>
          <el-descriptions-item label="规则类型">{{ getRuleTypeName(selectedRule.ruleType) }}</el-descriptions-item>
          <el-descriptions-item label="监控对象">{{ selectedRule.targetDevice }}</el-descriptions-item>
          <el-descriptions-item label="触发条件">{{ selectedRule.condition }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>测试参数</h4>
          <el-form :model="testForm" label-width="100px">
            <el-form-item label="测试数值">
              <el-input-number v-model="testForm.value" :precision="2" style="width: 100%" />
            </el-form-item>
            <el-form-item label="持续时间">
              <el-input-number v-model="testForm.duration" :min="1" style="width: 100%" />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
          </el-form>
        </div>
        
        <div style="margin-top: 20px;" v-if="testResult">
          <h4>测试结果</h4>
          <el-alert
            :title="testResult.message"
            :type="testResult.type"
            :closable="false"
            style="margin-top: 10px;"
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showTestDialog = false">关闭</el-button>
        <el-button type="primary" @click="executeTest">执行测试</el-button>
      </template>
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      :title="batchOperation === 'delete' ? '批量删除规则' : '批量启停规则'"
      width="50%"
    >
      <div style="margin-bottom: 20px;">
        <strong>已选择 {{ selectedRules.length }} 条规则</strong>
      </div>
      
      <div v-if="batchOperation === 'toggle'">
        <el-form :model="batchForm" label-width="100px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="batchForm.action">
              <el-radio label="enable">批量启用</el-radio>
              <el-radio label="disable">批量禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      
      <div v-if="batchOperation === 'delete'">
        <el-alert
          title="警告：删除操作不可恢复，请确认是否继续？"
          type="warning"
          :closable="false"
        />
      </div>
      
      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button 
          :type="batchOperation === 'delete' ? 'danger' : 'primary'" 
          @click="confirmBatchOperation"
        >
          确认{{ batchOperation === 'delete' ? '删除' : '操作' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { 
  Refresh, Download, Upload, Search, Plus, Delete, Switch,
  Setting, WarningFilled, InfoFilled, CircleCheck
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '异常规则', to: '' }
])

// 基础数据
const loading = ref(false)
const selectedBuilding = ref('')
const chartTimeRange = ref('30d')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedRules = ref<any[]>([])
const showRuleDialog = ref(false)
const showTestDialog = ref(false)
const showBatchDialog = ref(false)
const isEditing = ref(false)
const selectedRule = ref<any>(null)
const batchOperation = ref<'delete' | 'toggle'>('delete')

// 图表引用
const ruleTriggerChart = ref<HTMLElement>()
const ruleTypeChart = ref<HTMLElement>()

// 表单引用
const ruleFormRef = ref<FormInstance>()

// 筛选条件
const filters = ref({
  ruleType: '',
  status: '',
  severity: '',
  keyword: ''
})

// 规则表单
const ruleForm = ref({
  id: '',
  name: '',
  ruleType: 'threshold',
  targetDevice: '',
  severity: 'warning',
  description: '',
  isEnabled: true,
  config: {
    metric: 'power',
    operator: '>',
    threshold: 0,
    duration: 5,
    checkInterval: 1,
    trendDirection: 'up',
    changeRate: 10,
    timeWindow: 30,
    startTime: null as Date | null,
    endTime: null as Date | null,
    weekdays: ['1', '2', '3', '4', '5']
  },
  notificationMethods: ['email', 'app'],
  notificationUsers: [],
  silentPeriod: 30
})

// 表单验证规则
const ruleRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ruleType: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  targetDevice: [
    { required: true, message: '请选择监控对象', trigger: 'change' }
  ],
  severity: [
    { required: true, message: '请选择告警级别', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入规则描述', trigger: 'blur' }
  ]
}

// 测试表单
const testForm = ref({
  value: 0,
  duration: 5
})

// 测试结果
const testResult = ref<any>(null)

// 批量操作表单
const batchForm = ref({
  action: 'enable'
})

// 规则统计数据
const rulesStats = ref([
  {
    name: '总规则数',
    value: '45',
    unit: '条',
    trend: '↑ 12.5%',
    trendColor: '#67c23a',
    icon: 'Setting',
    color: '#409eff'
  },
  {
    name: '启用规则',
    value: '38',
    unit: '条',
    trend: '↑ 5.6%',
    trendColor: '#67c23a',
    icon: 'CircleCheck',
    color: '#67c23a'
  },
  {
    name: '今日触发',
    value: '156',
    unit: '次',
    trend: '↓ 8.3%',
    trendColor: '#67c23a',
    icon: 'WarningFilled',
    color: '#e6a23c'
  },
  {
    name: '异常规则',
    value: '3',
    unit: '条',
    trend: '↑ 50.0%',
    trendColor: '#f56c6c',
    icon: 'InfoFilled',
    color: '#f56c6c'
  }
])

// 规则数据
const rulesList = ref([
  {
    id: 'RULE001',
    name: '空调功率超限告警',
    ruleType: 'threshold',
    targetDevice: '空调主机-01',
    condition: '功率 > 15kW 持续5分钟',
    severity: 'warning',
    isEnabled: true,
    triggerCount: 23,
    updateTime: '2024-01-15 14:30:25',
    creator: '张工程师',
    description: '监控空调功率，超过15kW持续5分钟触发告警'
  },
  {
    id: 'RULE002',
    name: '照明设备离线监控',
    ruleType: 'time',
    targetDevice: '照明控制器-02',
    condition: '设备离线 > 3分钟',
    severity: 'critical',
    isEnabled: true,
    triggerCount: 8,
    updateTime: '2024-01-15 13:45:12',
    creator: '李技术员',
    description: '监控照明设备连接状态，离线超过3分钟触发紧急告警'
  },
  {
    id: 'RULE003',
    name: '电表数据异常检测',
    ruleType: 'trend',
    targetDevice: '电表-A101',
    condition: '电流波动 > 20% 在10分钟内',
    severity: 'info',
    isEnabled: false,
    triggerCount: 45,
    updateTime: '2024-01-15 12:20:08',
    creator: '王主管',
    description: '检测电表数据异常波动，10分钟内波动超过20%触发提醒'
  },
  {
    id: 'RULE004',
    name: '温度环境监控',
    ruleType: 'threshold',
    targetDevice: '温度传感器-05',
    condition: '温度 > 40°C 持续2分钟',
    severity: 'warning',
    isEnabled: true,
    triggerCount: 12,
    updateTime: '2024-01-15 11:15:33',
    creator: '刘管理员',
    description: '监控环境温度，超过40°C持续2分钟触发告警'
  },
  {
    id: 'RULE005',
    name: '变压器负载监控',
    ruleType: 'combination',
    targetDevice: '变压器-主01',
    condition: '负载率 > 85% 且温度 > 70°C',
    severity: 'critical',
    isEnabled: true,
    triggerCount: 5,
    updateTime: '2024-01-15 10:30:15',
    creator: '张工程师',
    description: '组合监控变压器负载和温度，同时超限触发紧急告警'
  }
])

// 过滤后的规则
const filteredRules = computed(() => {
  let rules = rulesList.value
  
  // 建筑筛选
  if (selectedBuilding.value) {
    rules = rules.filter(rule => 
      rule.targetDevice.includes(getBuildingName(selectedBuilding.value))
    )
  }
  
  // 规则类型筛选
  if (filters.value.ruleType) {
    rules = rules.filter(rule => rule.ruleType === filters.value.ruleType)
  }
  
  // 规则状态筛选
  if (filters.value.status) {
    const isEnabled = filters.value.status === 'enabled'
    rules = rules.filter(rule => rule.isEnabled === isEnabled)
  }
  
  // 告警级别筛选
  if (filters.value.severity) {
    rules = rules.filter(rule => rule.severity === filters.value.severity)
  }
  
  // 关键字搜索
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    rules = rules.filter(rule => 
      rule.name.toLowerCase().includes(keyword) ||
      rule.description.toLowerCase().includes(keyword) ||
      rule.targetDevice.toLowerCase().includes(keyword)
    )
  }
  
  return rules
})

// 分页后的规则
const paginatedRules = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredRules.value.slice(start, end)
})

// 辅助函数
const getBuildingName = (value: string) => {
  const names: Record<string, string> = {
    'building-a': '办公大楼A',
    'building-b': '办公大楼B',
    'workshop': '生产车间',
    'facilities': '配套设施'
  }
  return names[value] || ''
}

const getRuleTypeName = (type: string) => {
  const names: Record<string, string> = {
    'threshold': '阈值规则',
    'trend': '趋势规则',
    'time': '时间规则',
    'combination': '组合规则'
  }
  return names[type] || type
}

const getRuleTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'threshold': 'primary',
    'trend': 'success',
    'time': 'warning',
    'combination': 'danger'
  }
  return colors[type] || 'info'
}

const getSeverityName = (severity: string) => {
  const names: Record<string, string> = {
    'critical': '紧急',
    'warning': '警告',
    'info': '提醒'
  }
  return names[severity] || severity
}

const getSeverityColor = (severity: string) => {
  const colors: Record<string, string> = {
    'critical': 'danger',
    'warning': 'warning',
    'info': 'info'
  }
  return colors[severity] || 'info'
}

// 事件处理函数
const handleBuildingChange = () => {
  refreshData()
}

const handleFilterChange = () => {
  currentPage.value = 1
}

const handleKeywordSearch = () => {
  currentPage.value = 1
}

const handleChartTimeRangeChange = () => {
  nextTick(() => {
    initRuleTriggerChart()
  })
}

const handleSelectionChange = (selection: any[]) => {
  selectedRules.value = selection
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const refreshData = async () => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    nextTick(() => {
      initRuleTriggerChart()
      initRuleTypeChart()
    })
    
    ElMessage.success('数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const exportRules = () => {
  try {
    const exportData = filteredRules.value.map(item => ({
      规则ID: item.id,
      规则名称: item.name,
      规则类型: getRuleTypeName(item.ruleType),
      监控对象: item.targetDevice,
      触发条件: item.condition,
      告警级别: getSeverityName(item.severity),
      状态: item.isEnabled ? '启用' : '禁用',
      触发次数: item.triggerCount,
      更新时间: item.updateTime,
      创建人: item.creator
    }))
    
    const csvContent = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).join(','))
    ].join('\n')
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.download = `异常规则_${new Date().toISOString().slice(0, 10)}.csv`
    link.href = url
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('规则导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const importRules = () => {
  ElMessage.info('导入功能开发中...')
}

const addRule = () => {
  isEditing.value = false
  ruleForm.value = {
    id: '',
    name: '',
    ruleType: 'threshold',
    targetDevice: '',
    severity: 'warning',
    description: '',
    isEnabled: true,
    config: {
      metric: 'power',
      operator: '>',
      threshold: 0,
      duration: 5,
      checkInterval: 1,
      trendDirection: 'up',
      changeRate: 10,
      timeWindow: 30,
      startTime: null,
      endTime: null,
      weekdays: ['1', '2', '3', '4', '5']
    },
    notificationMethods: ['email', 'app'],
    notificationUsers: [],
    silentPeriod: 30
  }
  showRuleDialog.value = true
}

const editRule = (rule: any) => {
  isEditing.value = true
  ruleForm.value = { ...rule }
  showRuleDialog.value = true
}

const deleteRule = (rule: any) => {
  ElMessageBox.confirm(
    `确定要删除规则"${rule.name}"吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = rulesList.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      rulesList.value.splice(index, 1)
      ElMessage.success('规则删除成功！')
    }
  }).catch(() => {
    // 取消删除
  })
}

const testRule = (rule: any) => {
  selectedRule.value = rule
  testForm.value = {
    value: 0,
    duration: 5
  }
  testResult.value = null
  showTestDialog.value = true
}

const executeTest = () => {
  if (!selectedRule.value) return
  
  // 模拟测试逻辑
  const mockTest = () => {
    const rule = selectedRule.value
    const testValue = testForm.value.value
    
    if (rule.ruleType === 'threshold') {
      const threshold = 15 // 模拟阈值
      const triggered = testValue > threshold
      
      testResult.value = {
        type: triggered ? 'warning' : 'success',
        message: triggered 
          ? `规则将被触发！测试值 ${testValue} > 阈值 ${threshold}` 
          : `规则不会触发。测试值 ${testValue} <= 阈值 ${threshold}`
      }
    } else {
      testResult.value = {
        type: 'success',
        message: '规则测试完成，功能正常'
      }
    }
  }
  
  setTimeout(mockTest, 500)
}

const toggleRuleStatus = (rule: any) => {
  const action = rule.isEnabled ? '启用' : '禁用'
  ElMessage.success(`规则已${action}`)
}

const batchDelete = () => {
  batchOperation.value = 'delete'
  showBatchDialog.value = true
}

const batchToggle = () => {
  batchOperation.value = 'toggle'
  batchForm.value.action = 'enable'
  showBatchDialog.value = true
}

const confirmBatchOperation = () => {
  if (batchOperation.value === 'delete') {
    selectedRules.value.forEach(rule => {
      const index = rulesList.value.findIndex(r => r.id === rule.id)
      if (index > -1) {
        rulesList.value.splice(index, 1)
      }
    })
    ElMessage.success(`批量删除完成！共删除 ${selectedRules.value.length} 条规则`)
  } else if (batchOperation.value === 'toggle') {
    const isEnabled = batchForm.value.action === 'enable'
    selectedRules.value.forEach(rule => {
      const index = rulesList.value.findIndex(r => r.id === rule.id)
      if (index > -1) {
        rulesList.value[index].isEnabled = isEnabled
      }
    })
    ElMessage.success(`批量${isEnabled ? '启用' : '禁用'}完成！共操作 ${selectedRules.value.length} 条规则`)
  }
  
  selectedRules.value = []
  showBatchDialog.value = false
}

const handleCloseRule = () => {
  showRuleDialog.value = false
}

const testRuleConfig = () => {
  ElMessage.info('规则配置测试功能开发中...')
}

const saveRule = async () => {
  if (!ruleFormRef.value) return
  
  await ruleFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (isEditing.value) {
          // 编辑规则
          const index = rulesList.value.findIndex(r => r.id === ruleForm.value.id)
          if (index > -1) {
            rulesList.value[index] = {
              ...rulesList.value[index],
              ...ruleForm.value,
              updateTime: new Date().toLocaleString()
            }
          }
          ElMessage.success('规则更新成功！')
        } else {
          // 新建规则
          const newRule = {
            ...ruleForm.value,
            id: 'RULE' + String(rulesList.value.length + 1).padStart(3, '0'),
            triggerCount: 0,
            updateTime: new Date().toLocaleString(),
            creator: '当前用户',
            condition: generateConditionText(ruleForm.value)
          }
          rulesList.value.unshift(newRule)
          ElMessage.success('规则创建成功！')
        }
        
        showRuleDialog.value = false
      } catch (error) {
        ElMessage.error('保存失败')
      }
    }
  })
}

const generateConditionText = (rule: any) => {
  if (rule.ruleType === 'threshold') {
    return `${rule.config.metric} ${rule.config.operator} ${rule.config.threshold} 持续${rule.config.duration}分钟`
  } else if (rule.ruleType === 'trend') {
    return `${rule.config.trendDirection === 'up' ? '上升' : '下降'} ${rule.config.changeRate}% 在${rule.config.timeWindow}分钟内`
  } else if (rule.ruleType === 'time') {
    return `时间规则 ${rule.config.startTime} - ${rule.config.endTime}`
  } else {
    return '组合规则条件'
  }
}

// 初始化规则触发趋势图表
const initRuleTriggerChart = () => {
  if (!ruleTriggerChart.value) return
  
  const chart = echarts.init(ruleTriggerChart.value)
  
  const days = []
  const thresholdData = []
  const trendData = []
  const timeData = []
  
  // 生成示例数据
  for (let i = 29; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.getMonth() + 1 + '/' + date.getDate())
    
    thresholdData.push(Math.floor(Math.random() * 15) + 5)
    trendData.push(Math.floor(Math.random() * 8) + 2)
    timeData.push(Math.floor(Math.random() * 5) + 1)
  }
  
  const option = {
    title: {
      text: '规则触发趋势统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: {
      data: ['阈值规则', '趋势规则', '时间规则'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: days
    },
    yAxis: {
      type: 'value',
      name: '触发次数',
      axisLabel: { formatter: '{value}次' }
    },
    series: [
      {
        name: '阈值规则',
        type: 'line',
        smooth: true,
        data: thresholdData,
        itemStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
      },
      {
        name: '趋势规则',
        type: 'line',
        smooth: true,
        data: trendData,
        itemStyle: { color: '#67c23a' },
        areaStyle: { color: 'rgba(103, 194, 58, 0.1)' }
      },
      {
        name: '时间规则',
        type: 'line',
        smooth: true,
        data: timeData,
        itemStyle: { color: '#e6a23c' },
        areaStyle: { color: 'rgba(230, 162, 60, 0.1)' }
      }
    ]
  }
  
  chart.setOption(option)
}

// 初始化规则类型分布图表
const initRuleTypeChart = () => {
  if (!ruleTypeChart.value) return
  
  const chart = echarts.init(ruleTypeChart.value)
  
  const option = {
    title: {
      text: '规则类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 60
    },
    series: [
      {
        name: '规则类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 20, name: '阈值规则', itemStyle: { color: '#409eff' } },
          { value: 12, name: '趋势规则', itemStyle: { color: '#67c23a' } },
          { value: 8, name: '时间规则', itemStyle: { color: '#e6a23c' } },
          { value: 5, name: '组合规则', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  
  chart.setOption(option)
}

onMounted(() => {
  nextTick(() => {
    refreshData()
  })
})
</script>

<style scoped>
.exception-rules {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.rules-stats {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 30px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-trend {
  font-size: 11px;
  font-weight: 500;
}

.stat-divider {
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background-color: #ebeef5;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.record-count {
  font-size: 14px;
  color: #909399;
  margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rules-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .stat-item {
    min-width: auto;
    padding: 0;
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .stat-divider {
    display: none;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .filter-item {
    margin-bottom: 15px;
  }
}
</style> 