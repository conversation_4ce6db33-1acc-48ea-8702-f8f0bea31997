<template>
  <div class="topology-view" v-loading="loading" element-loading-text="正在加载拓扑数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 控制面板 -->
    <el-card class="control-panel">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <h3>拓扑控制</h3>
            <div v-if="showBackButton" class="drill-path">
              <el-button type="info" size="small" @click="goBack" class="back-btn">
                <el-icon><ArrowLeft /></el-icon>
                返回上级
              </el-button>
              <span class="path-text">{{ getDrillPathText() }}</span>
            </div>
          </div>
          <div class="header-controls">
            <el-button v-if="showBackButton" type="warning" size="small" @click="refreshTopology(true)" :loading="loading">
              <el-icon><House /></el-icon>
              返回根视图
            </el-button>
            <el-button type="primary" size="small" @click="refreshTopology(false)" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新拓扑
            </el-button>
            <el-button type="success" size="small" @click="showConfigDialog = true">
              <el-icon><Setting /></el-icon>
              配置分项
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="5">
          <div class="control-group">
            <label class="control-label">显示层级：</label>
            <el-select v-model="displayLevel" placeholder="选择显示层级" size="small" @change="handleLevelChange">
              <el-option label="全部层级" value="all" />
              <el-option label="一级分项" value="level1" />
              <el-option label="二级分项" value="level2" />
              <el-option label="三级分项" value="level3" />
            </el-select>
          </div>
        </el-col>

        <el-col :span="5">
          <div class="control-group">
            <label class="control-label">能源类型：</label>
            <el-select v-model="selectedEnergyType" placeholder="选择能源类型" size="small" @change="handleEnergyTypeChange">
              <el-option label="全部" value="all" />
              <el-option label="用电" value="electric" />
              <el-option label="用水" value="water" />
              <el-option label="燃气" value="gas" />
              <el-option label="供暖" value="heating" />
            </el-select>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="control-group">
            <label class="control-label">时间范围：</label>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>

        <el-col :span="4">
          <div class="control-group">
            <label class="control-label">布局模式：</label>
            <el-radio-group v-model="layoutMode" size="small" @change="handleLayoutChange">
              <el-radio-button label="tree">树形</el-radio-button>
              <el-radio-button label="force">力导向</el-radio-button>
            </el-radio-group>
          </div>
        </el-col>

        <el-col :span="4">
          <div class="control-group">
            <label class="control-label">自动刷新：</label>
            <div class="auto-refresh-controls">
              <el-switch
                v-model="autoRefreshEnabled"
                size="small"
                @change="toggleAutoRefresh"
                :disabled="loading"
              />
              <el-select
                v-model="autoRefreshInterval"
                size="small"
                style="width: 70px; margin-left: 8px;"
                @change="setAutoRefreshInterval"
                :disabled="!autoRefreshEnabled"
              >
                <el-option label="10s" :value="10" />
                <el-option label="30s" :value="30" />
                <el-option label="60s" :value="60" />
                <el-option label="120s" :value="120" />
              </el-select>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 刷新状态信息 -->
      <div class="refresh-status" v-if="lastRefreshTime">
        <span class="status-text">
          最后刷新: {{ lastRefreshTime.toLocaleTimeString() }}
        </span>
        <span v-if="autoRefreshEnabled" class="auto-refresh-indicator">
          <el-icon color="#52c41a"><Loading /></el-icon>
          自动刷新已开启
        </span>
      </div>
    </el-card>

    <!-- 拓扑图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 主拓扑图 -->
      <el-col :span="18">
        <el-card>
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <h3>用能拓扑结构</h3>
                <div v-if="loading" class="loading-indicator">
                  <el-icon color="#1890ff"><Loading /></el-icon>
                  <span>数据刷新中...</span>
                </div>
              </div>
              <div class="header-controls">
                <el-button-group size="small">
                  <el-button @click="zoomIn" :disabled="loading">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                  <el-button @click="zoomOut" :disabled="loading">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button @click="resetZoom" :disabled="loading">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </el-button-group>
                <el-button type="text" size="small" @click="exportTopology" :disabled="loading">
                  <el-icon><Download /></el-icon>
                  导出拓扑图
                </el-button>
              </div>
            </div>
          </template>
          <div
            ref="topologyChart"
            class="topology-chart-container"
            style="height: 600px;"
          ></div>
        </el-card>
      </el-col>
      
      <!-- 节点信息面板 -->
      <el-col :span="6">
        <el-card class="node-info-card">
          <template #header>
            <div class="card-header">
              <h3>节点信息</h3>
              <div class="header-controls">
                <el-button type="text" size="small" @click="clearSelection">
                  清除选择
                </el-button>
              </div>
            </div>
          </template>

          <div class="node-info-content">
            <div v-if="selectedNode" class="node-info">
              <!-- 节点头部信息 -->
              <div class="node-header">
                <div class="node-icon-wrapper">
                  <div class="node-icon" :style="{
                    '--node-color': selectedNode.color,
                    '--node-color-light': selectedNode.color + '20'
                  }">
                    <el-icon :size="24" color="white">
                      <component :is="selectedNode.icon" />
                    </el-icon>
                  </div>
                  <div class="node-status-indicator" :class="{ 'status-normal': selectedNode.status === '正常', 'status-error': selectedNode.status === '异常' }"></div>
                </div>
                <div class="node-title">
                  <h4>{{ selectedNode.name }}</h4>
                  <div class="node-meta">
                    <span class="node-type">{{ selectedNode.type }}</span>
                    <span class="node-status" :class="{ 'status-normal': selectedNode.status === '正常', 'status-error': selectedNode.status === '异常' }">
                      {{ selectedNode.status }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 核心数据展示 -->
              <div class="core-metrics">
                <div class="metric-card primary">
                  <div class="metric-icon">
                    <el-icon><Lightning /></el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-value">{{ selectedNode.power }}</div>
                    <div class="metric-label">实时功率 (kW)</div>
                  </div>
                </div>

                <div class="metrics-grid">
                  <div class="metric-item">
                    <div class="metric-number">{{ selectedNode.todayConsumption.toLocaleString() }}</div>
                    <div class="metric-text">今日能耗 (kWh)</div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-number">{{ (selectedNode.monthConsumption / 1000).toFixed(1) }}K</div>
                    <div class="metric-text">本月能耗 (kWh)</div>
                  </div>
                </div>
              </div>

              <!-- 详细信息 -->
              <div class="detail-info">
                <div class="info-row">
                  <span class="info-label">设备层级</span>
                  <span class="info-value">{{ selectedNode.level + 1 }}级分项</span>
                </div>
                <div class="info-row">
                  <span class="info-label">最后更新</span>
                  <span class="info-value">{{ new Date().toLocaleTimeString() }}</span>
                </div>
                <div class="info-row" v-if="selectedNode.hasChildren">
                  <span class="info-label">子节点</span>
                  <span class="info-value">
                    <el-tag type="success" size="small">可下钻查看</el-tag>
                  </span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="node-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="drillDown"
                  :disabled="!selectedNode.hasChildren"
                  class="action-btn primary"
                >
                  <el-icon><ArrowDown /></el-icon>
                  下钻查看
                </el-button>
                <el-button
                  type="default"
                  size="small"
                  @click="viewHistory"
                  class="action-btn secondary"
                >
                  <el-icon><TrendCharts /></el-icon>
                  历史趋势
                </el-button>
              </div>
            </div>

            <div v-else class="no-selection">
              <div class="empty-state">
                <div class="empty-icon">
                  <el-icon :size="48" color="#d9d9d9">
                    <Monitor />
                  </el-icon>
                </div>
                <div class="empty-title">选择节点查看详情</div>
                <div class="empty-description">点击拓扑图中的任意节点<br/>查看详细的能耗信息和状态</div>
                <div class="empty-tips">
                  <div class="tip-item">
                    <el-icon color="#1890ff"><Lightning /></el-icon>
                    <span>实时功率监控</span>
                  </div>
                  <div class="tip-item">
                    <el-icon color="#52c41a"><DataAnalysis /></el-icon>
                    <span>能耗数据分析</span>
                  </div>
                  <div class="tip-item">
                    <el-icon color="#fa8c16"><TrendCharts /></el-icon>
                    <span>历史趋势查看</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 能耗统计概览 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>能耗统计概览</h3>
          <div class="header-controls">
            <el-radio-group v-model="statsType" size="small" @change="handleStatsTypeChange">
              <el-radio-button label="realtime">实时数据</el-radio-button>
              <el-radio-button label="daily">日统计</el-radio-button>
              <el-radio-button label="monthly">月统计</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <div class="stats-overview">
        <div
          v-for="(stat, index) in statsData"
          :key="index"
          class="stat-card"
          @click="selectStatNode(stat)"
        >
          <div
            class="stat-icon"
            :style="{
              '--icon-color': stat.color,
              '--icon-color-dark': stat.colorDark
            }"
          >
            <el-icon :size="32" color="white">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-name">{{ stat.name }}</div>
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-unit">{{ stat.unit }}</div>
            <div class="stat-trend" :class="{ 'trend-up': stat.trend > 0, 'trend-down': stat.trend < 0 }">
              {{ stat.trend > 0 ? '↑' : '↓' }} {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </div>
    </el-card>



    <!-- 自定义分项配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="自定义分项配置"
      width="60%"
      :before-close="handleCloseConfig"
    >
      <el-form :model="configForm" label-width="120px">
        <el-form-item label="分项名称：">
          <el-input v-model="configForm.name" placeholder="请输入分项名称" />
        </el-form-item>
        <el-form-item label="分项类型：">
          <el-select v-model="configForm.type" placeholder="请选择分项类型">
            <el-option label="用电分项" value="electric" />
            <el-option label="用水分项" value="water" />
            <el-option label="燃气分项" value="gas" />
            <el-option label="供暖分项" value="heating" />
          </el-select>
        </el-form-item>
        <el-form-item label="父级分项：">
          <el-tree-select
            v-model="configForm.parent"
            :data="treeData"
            :render-after-expand="false"
            placeholder="请选择父级分项"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="计量设备：">
          <el-select v-model="configForm.devices" multiple placeholder="请选择关联的计量设备">
            <el-option
              v-for="device in availableDevices"
              :key="device.id"
              :label="device.name"
              :value="device.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </template>
    </el-dialog>

    <!-- 历史趋势对话框 -->
    <el-dialog
      v-model="showHistoryDialog"
      title="历史趋势分析"
      width="80%"
      :before-close="handleCloseHistory"
    >
      <div v-if="selectedNode">
        <h4>{{ selectedNode.name }} - 历史能耗趋势</h4>
        <div ref="historyChart" style="height: 400px; margin-top: 20px;"></div>
      </div>
      <template #footer>
        <el-button @click="showHistoryDialog = false">关闭</el-button>
        <el-button type="primary" @click="exportHistory">导出数据</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue'
import {
  Refresh, Download, Setting, ZoomIn, ZoomOut, ArrowDown, TrendCharts,
  OfficeBuilding, Factory, House, Lightning, Cpu, Monitor, Calendar, DataAnalysis, ArrowLeft, Loading
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能耗分析平台', to: '/energy-analysis' },
  { text: '拓扑视图', to: '' }
])

// 基础数据
const loading = ref(false)
const displayLevel = ref('all')
const selectedEnergyType = ref('all')
const dateRange = ref<[Date, Date]>()
const layoutMode = ref('tree')
const statsType = ref('realtime')
const selectedNode = ref<any>(null)
const showConfigDialog = ref(false)
const showHistoryDialog = ref(false)

// 下钻相关状态
const drillPath = ref<string[]>([]) // 下钻路径
const currentParentId = ref<string>('root') // 当前父节点ID
const showBackButton = ref(false) // 是否显示返回按钮

// 图表引用
const topologyChart = ref<HTMLElement>()
const historyChart = ref<HTMLElement>()

// 配置表单
const configForm = ref({
  name: '',
  type: '',
  parent: '',
  devices: []
})

// 拓扑数据
const topologyData = ref({
  nodes: [
    {
      id: 'root',
      name: '总配电室',
      type: '配电设备',
      level: 0,
      x: 400,
      y: 50,
      symbolSize: 80,
      itemStyle: { color: '#409eff' },
      icon: 'Lightning',
      power: 2580.5,
      todayConsumption: 18650,
      monthConsumption: 485200,
      status: '正常',
      hasChildren: true
    },
    {
      id: 'building-a',
      name: '办公大楼A',
      type: '建筑分项',
      level: 1,
      x: 200,
      y: 200,
      symbolSize: 60,
      itemStyle: { color: '#67c23a' },
      icon: 'OfficeBuilding',
      power: 856.2,
      todayConsumption: 6420,
      monthConsumption: 186420,
      status: '正常',
      hasChildren: true
    },
    {
      id: 'building-b',
      name: '办公大楼B',
      type: '建筑分项',
      level: 1,
      x: 400,
      y: 200,
      symbolSize: 60,
      itemStyle: { color: '#e6a23c' },
      icon: 'OfficeBuilding',
      power: 642.8,
      todayConsumption: 4680,
      monthConsumption: 142680,
      status: '正常',
      hasChildren: true
    },
    {
      id: 'workshop',
      name: '生产车间',
      type: '建筑分项',
      level: 1,
      x: 600,
      y: 200,
      symbolSize: 60,
      itemStyle: { color: '#f56c6c' },
      icon: 'Factory',
      power: 1081.5,
      todayConsumption: 7550,
      monthConsumption: 298560,
      status: '正常',
      hasChildren: true
    },
    // 二级分项 - 办公大楼A
    {
      id: 'floor-1a',
      name: '办公大楼A-1楼',
      type: '楼层分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#95d475' },
      icon: 'House',
      power: 428.1,
      todayConsumption: 3210,
      monthConsumption: 93210,
      status: '正常',
      hasChildren: true
    },
    {
      id: 'floor-2a',
      name: '办公大楼A-2楼',
      type: '楼层分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#95d475' },
      icon: 'House',
      power: 428.1,
      todayConsumption: 3210,
      monthConsumption: 93210,
      status: '正常',
      hasChildren: true
    },
    // 二级分项 - 办公大楼B
    {
      id: 'floor-1b',
      name: '办公大楼B-1楼',
      type: '楼层分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#f0c674' },
      icon: 'House',
      power: 321.4,
      todayConsumption: 2340,
      monthConsumption: 71340,
      status: '正常',
      hasChildren: true
    },
    {
      id: 'floor-2b',
      name: '办公大楼B-2楼',
      type: '楼层分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#f0c674' },
      icon: 'House',
      power: 321.4,
      todayConsumption: 2340,
      monthConsumption: 71340,
      status: '正常',
      hasChildren: true
    },
    // 二级分项 - 生产车间
    {
      id: 'production-line-1',
      name: '生产线1',
      type: '设备分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#ff8a80' },
      icon: 'Cpu',
      power: 540.8,
      todayConsumption: 3775,
      monthConsumption: 149280,
      status: '正常',
      hasChildren: false
    },
    {
      id: 'production-line-2',
      name: '生产线2',
      type: '设备分项',
      level: 2,
      symbolSize: 40,
      itemStyle: { color: '#ff8a80' },
      icon: 'Cpu',
      power: 540.7,
      todayConsumption: 3775,
      monthConsumption: 149280,
      status: '异常',
      hasChildren: false
    },
    // 三级分项 - 具体设备
    {
      id: 'ac-1a',
      name: '空调系统-1A',
      type: '终端设备',
      level: 3,
      symbolSize: 30,
      itemStyle: { color: '#81c784' },
      icon: 'Monitor',
      power: 85.6,
      todayConsumption: 642,
      monthConsumption: 18642,
      status: '正常',
      hasChildren: false
    },
    {
      id: 'lighting-1a',
      name: '照明系统-1A',
      type: '终端设备',
      level: 3,
      symbolSize: 30,
      itemStyle: { color: '#81c784' },
      icon: 'Lightning',
      power: 45.2,
      todayConsumption: 339,
      monthConsumption: 9839,
      status: '正常',
      hasChildren: false
    },
    {
      id: 'office-1a',
      name: '办公设备-1A',
      type: '终端设备',
      level: 3,
      symbolSize: 30,
      itemStyle: { color: '#81c784' },
      icon: 'Cpu',
      power: 125.3,
      todayConsumption: 940,
      monthConsumption: 27340,
      status: '正常',
      hasChildren: false
    },
    // 办公大楼A-2楼设备
    {
      id: 'ac-2a',
      name: '空调系统-2A',
      type: '终端设备',
      level: 3,
      symbolSize: 30,
      itemStyle: { color: '#81c784' },
      icon: 'Monitor',
      power: 92.4,
      todayConsumption: 693,
      monthConsumption: 20130,
      status: '异常',
      hasChildren: false
    },
    {
      id: 'lighting-2a',
      name: '照明系统-2A',
      type: '终端设备',
      level: 3,
      symbolSize: 30,
      itemStyle: { color: '#81c784' },
      icon: 'Lightning',
      power: 48.7,
      todayConsumption: 365,
      monthConsumption: 10595,
      status: '正常',
      hasChildren: false
    }
  ],
  links: [
    // 一级连接
    { source: 'root', target: 'building-a' },
    { source: 'root', target: 'building-b' },
    { source: 'root', target: 'workshop' },
    // 二级连接
    { source: 'building-a', target: 'floor-1a' },
    { source: 'building-a', target: 'floor-2a' },
    { source: 'building-b', target: 'floor-1b' },
    { source: 'building-b', target: 'floor-2b' },
    { source: 'workshop', target: 'production-line-1' },
    { source: 'workshop', target: 'production-line-2' },
    // 三级连接
    { source: 'floor-1a', target: 'ac-1a' },
    { source: 'floor-1a', target: 'lighting-1a' },
    { source: 'floor-1a', target: 'office-1a' },
    { source: 'floor-2a', target: 'ac-2a' },
    { source: 'floor-2a', target: 'lighting-2a' }
  ]
})

// 统计数据
const statsData = computed(() => {
  const baseStats = [
    {
      name: '总用电量',
      value: '2,580.5',
      unit: 'kW',
      trend: 5.2,
      color: '#1890ff',
      colorDark: '#0050b3',
      icon: 'Lightning'
    },
    {
      name: '办公大楼A',
      value: '856.2',
      unit: 'kW',
      trend: 3.8,
      color: '#52c41a',
      colorDark: '#237804',
      icon: 'OfficeBuilding'
    },
    {
      name: '办公大楼B',
      value: '642.8',
      unit: 'kW',
      trend: -2.1,
      color: '#fa8c16',
      colorDark: '#ad4e00',
      icon: 'OfficeBuilding'
    },
    {
      name: '生产车间',
      value: '1,081.5',
      unit: 'kW',
      trend: 8.5,
      color: '#722ed1',
      colorDark: '#391085',
      icon: 'Factory'
    }
  ]

  if (statsType.value === 'daily') {
    return baseStats.map(stat => ({
      ...stat,
      value: (parseFloat(stat.value.replace(',', '')) * 24).toLocaleString(),
      unit: 'kWh'
    }))
  } else if (statsType.value === 'monthly') {
    return baseStats.map(stat => ({
      ...stat,
      value: (parseFloat(stat.value.replace(',', '')) * 720).toLocaleString(),
      unit: 'kWh'
    }))
  }

  return baseStats
})

// 树形数据
const treeData = ref([
  {
    value: 'root',
    label: '总配电室',
    children: [
      {
        value: 'building-a',
        label: '办公大楼A',
        children: [
          { value: 'floor-1a', label: '1楼' },
          { value: 'floor-2a', label: '2楼' }
        ]
      },
      {
        value: 'building-b',
        label: '办公大楼B',
        children: [
          { value: 'floor-1b', label: '1楼' },
          { value: 'floor-2b', label: '2楼' }
        ]
      }
    ]
  }
])

// 可用设备
const availableDevices = ref([
  { id: 'meter-001', name: '电表-001' },
  { id: 'meter-002', name: '电表-002' },
  { id: 'meter-003', name: '电表-003' },
  { id: 'sensor-001', name: '传感器-001' },
  { id: 'sensor-002', name: '传感器-002' }
])

// 过滤后的拓扑数据
const filteredTopologyData = computed(() => {
  let nodes = [...topologyData.value.nodes]
  let links = [...topologyData.value.links]

  // 根据下钻状态过滤节点
  if (currentParentId.value !== 'root') {
    // 下钻模式：只显示当前父节点的直接子节点
    const childNodeIds = links
      .filter(link => link.source === currentParentId.value)
      .map(link => link.target)

    // 包含当前父节点和其子节点
    nodes = nodes.filter(node =>
      node.id === currentParentId.value || childNodeIds.includes(node.id)
    )

    // 只保留相关的连接
    const nodeIds = new Set(nodes.map(n => n.id))
    links = links.filter(link => nodeIds.has(link.source) && nodeIds.has(link.target))
  } else {
    // 根据显示层级过滤
    if (displayLevel.value !== 'all') {
      const maxLevel = parseInt(displayLevel.value.replace('level', ''))
      nodes = nodes.filter(node => node.level <= maxLevel)

      // 过滤对应的连接
      const nodeIds = new Set(nodes.map(n => n.id))
      links = links.filter(link => nodeIds.has(link.source) && nodeIds.has(link.target))
    }
  }

  // 根据能源类型过滤（这里可以根据实际需求扩展）
  if (selectedEnergyType.value !== 'all') {
    // 可以根据节点的能源类型属性进行过滤
    // 这里暂时保持所有节点
  }

  return { nodes, links }
})

// 事件处理
const handleLevelChange = () => {
  updateTopologyDisplay()
}

const handleEnergyTypeChange = () => {
  updateTopologyDisplay()
}

const handleDateRangeChange = () => {
  refreshTopology()
}

const handleLayoutChange = () => {
  initTopologyChart()
}

const handleStatsTypeChange = () => {
  // 统计类型变化通过computed自动处理
}

// 刷新拓扑
const refreshTopology = async (resetDrill = false) => {
  try {
    loading.value = true

    // 显示刷新提示
    const loadingMessage = ElMessage({
      message: '正在刷新拓扑数据...',
      type: 'info',
      duration: 0,
      showClose: false
    })

    // 如果需要重置下钻状态
    if (resetDrill) {
      resetDrillState()
      ElMessage.info('已重置到根视图')
    }

    // 模拟数据刷新
    await refreshTopologyData()

    // 关闭加载提示
    loadingMessage.close()

    // 重新初始化图表
    nextTick(() => {
      initTopologyChart()
    })

    ElMessage.success('拓扑数据刷新成功！')
  } catch (error) {
    ElMessage.error('刷新失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 刷新拓扑数据
const refreshTopologyData = async () => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 1200))

  // 模拟数据更新 - 随机更新功率和能耗数据
  topologyData.value.nodes = topologyData.value.nodes.map(node => {
    const powerVariation = (Math.random() - 0.5) * 0.1 // ±5%的变化
    const consumptionVariation = (Math.random() - 0.5) * 0.2 // ±10%的变化

    return {
      ...node,
      power: Math.max(0, node.power * (1 + powerVariation)),
      todayConsumption: Math.max(0, Math.round(node.todayConsumption * (1 + consumptionVariation))),
      monthConsumption: Math.max(0, Math.round(node.monthConsumption * (1 + consumptionVariation))),
      // 随机更新状态（90%概率保持正常）
      status: Math.random() > 0.1 ? '正常' : '异常'
    }
  })

  // 更新统计数据
  updateStatsData()
}

// 更新拓扑显示
const updateTopologyDisplay = () => {
  nextTick(() => {
    initTopologyChart()
  })
}

// 缩放控制
const zoomIn = () => {
  const chart = echarts.getInstanceByDom(topologyChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'dataZoom',
      start: 10,
      end: 90
    })
  }
}

const zoomOut = () => {
  const chart = echarts.getInstanceByDom(topologyChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }
}

const resetZoom = () => {
  const chart = echarts.getInstanceByDom(topologyChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'restore'
    })
  }
}

// 导出拓扑图
const exportTopology = async () => {
  try {
    if (!topologyChart.value) return

    const chart = echarts.getInstanceByDom(topologyChart.value)
    if (!chart) return

    const imageDataURL = chart.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    })

    const link = document.createElement('a')
    link.download = `拓扑图_${new Date().toISOString().slice(0, 10)}.png`
    link.href = imageDataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('拓扑图导出成功！')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 清除选择
const clearSelection = () => {
  selectedNode.value = null
}

// 下钻查看
const drillDown = async () => {
  if (!selectedNode.value || !selectedNode.value.hasChildren) return

  try {
    loading.value = true
    ElMessage.info(`正在加载 ${selectedNode.value.name} 的子级数据...`)

    // 添加到下钻路径
    drillPath.value.push(selectedNode.value.name)
    currentParentId.value = selectedNode.value.id
    showBackButton.value = true

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 清除当前选中的节点
    selectedNode.value = null

    // 重新初始化拓扑图，显示子级节点
    nextTick(() => {
      initTopologyChart()
    })

    ElMessage.success(`已进入 ${drillPath.value[drillPath.value.length - 1]} 的子级视图`)
  } catch (error) {
    ElMessage.error('下钻查看失败')
  } finally {
    loading.value = false
  }
}

// 返回上级
const goBack = async () => {
  if (drillPath.value.length === 0) return

  try {
    loading.value = true

    // 移除最后一个路径
    const lastPath = drillPath.value.pop()

    // 更新当前父节点ID
    if (drillPath.value.length === 0) {
      currentParentId.value = 'root'
      showBackButton.value = false
    } else {
      // 找到上级节点的ID
      const parentName = drillPath.value[drillPath.value.length - 1]
      const parentNode = topologyData.value.nodes.find(node => node.name === parentName)
      currentParentId.value = parentNode?.id || 'root'
    }

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 600))

    // 清除当前选中的节点
    selectedNode.value = null

    // 重新初始化拓扑图
    nextTick(() => {
      initTopologyChart()
    })

    ElMessage.success(`已返回到 ${drillPath.value.length > 0 ? drillPath.value[drillPath.value.length - 1] : '根'} 级视图`)
  } catch (error) {
    ElMessage.error('返回上级失败')
  } finally {
    loading.value = false
  }
}

// 获取下钻路径文本
const getDrillPathText = () => {
  if (drillPath.value.length === 0) return ''
  return `当前位置: ${drillPath.value.join(' > ')}`
}

// 重置下钻状态
const resetDrillState = () => {
  drillPath.value = []
  currentParentId.value = 'root'
  showBackButton.value = false
  selectedNode.value = null
}

// 更新统计数据
const updateStatsData = () => {
  // 重新计算总用电量
  const totalPower = topologyData.value.nodes
    .filter(node => node.level === 1) // 只计算一级分项
    .reduce((sum, node) => sum + node.power, 0)

  // 更新最后刷新时间
  lastRefreshTime.value = new Date()

  ElMessage.success(`数据已更新，总功率: ${totalPower.toFixed(1)}kW`)
}

// 最后刷新时间
const lastRefreshTime = ref(new Date())

// 自动刷新相关
const autoRefreshEnabled = ref(false)
const autoRefreshInterval = ref(30) // 秒
const autoRefreshTimer = ref<NodeJS.Timeout | null>(null)

// 开启/关闭自动刷新
const toggleAutoRefresh = () => {
  if (autoRefreshEnabled.value) {
    stopAutoRefresh()
  } else {
    startAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }

  autoRefreshEnabled.value = true
  autoRefreshTimer.value = setInterval(() => {
    if (!loading.value) {
      refreshTopology(false)
    }
  }, autoRefreshInterval.value * 1000)

  ElMessage.success(`已开启自动刷新，间隔 ${autoRefreshInterval.value} 秒`)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }

  autoRefreshEnabled.value = false
  ElMessage.info('已关闭自动刷新')
}

// 设置自动刷新间隔
const setAutoRefreshInterval = (interval: number) => {
  autoRefreshInterval.value = interval
  if (autoRefreshEnabled.value) {
    startAutoRefresh() // 重新启动以应用新间隔
  }
}



// 查看历史趋势
const viewHistory = () => {
  if (!selectedNode.value) return

  showHistoryDialog.value = true
  nextTick(() => {
    initHistoryChart()
  })
}

// 选择统计节点
const selectStatNode = (stat: any) => {
  // 在拓扑图中高亮对应节点
  const node = topologyData.value.nodes.find(n => n.name.includes(stat.name.replace('总用电量', '总配电室')))
  if (node) {
    selectedNode.value = node
    highlightNode(node.id)
  }
}

// 高亮节点
const highlightNode = (nodeId: string) => {
  const chart = echarts.getInstanceByDom(topologyChart.value!)
  if (chart) {
    chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: topologyData.value.nodes.findIndex(n => n.id === nodeId)
    })
  }
}

// 配置相关
const handleCloseConfig = () => {
  showConfigDialog.value = false
  configForm.value = {
    name: '',
    type: '',
    parent: '',
    devices: []
  }
}

const saveConfig = () => {
  if (!configForm.value.name || !configForm.value.type) {
    ElMessage.warning('请填写完整的配置信息')
    return
  }

  ElMessage.success('分项配置保存成功！')
  showConfigDialog.value = false
  handleCloseConfig()
}

// 历史趋势相关
const handleCloseHistory = () => {
  showHistoryDialog.value = false
}

const exportHistory = () => {
  if (!selectedNode.value) return

  const data = {
    节点名称: selectedNode.value.name,
    导出时间: new Date().toISOString(),
    历史数据: [
      { 时间: '2024-01-01', 能耗: 856.2 },
      { 时间: '2024-01-02', 能耗: 892.5 },
      { 时间: '2024-01-03', 能耗: 834.7 }
    ]
  }

  const jsonData = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonData], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.download = `${selectedNode.value.name}_历史趋势.json`
  link.href = url
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('历史数据导出成功！')
}

// 初始化拓扑图表
const initTopologyChart = () => {
  if (!topologyChart.value) return

  const chart = echarts.init(topologyChart.value)

  let option: any = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e1e8ed',
      borderWidth: 1,
      textStyle: {
        color: '#2c3e50',
        fontSize: 13
      },
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px;',
      formatter: (params: any) => {
        const data = params.data
        const statusColor = data.status === '正常' ? '#52c41a' : '#ff4d4f'
        return `
          <div style="padding: 12px; min-width: 200px;">
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
              <div style="width: 12px; height: 12px; border-radius: 50%; background: ${data.itemStyle?.color || '#409eff'}; margin-right: 8px;"></div>
              <h4 style="margin: 0; font-size: 16px; color: #2c3e50;">${data.name}</h4>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px;">
              <div><span style="color: #8c8c8c;">类型：</span><span style="color: #2c3e50; font-weight: 500;">${data.type}</span></div>
              <div><span style="color: #8c8c8c;">状态：</span><span style="color: ${statusColor}; font-weight: 500;">${data.status}</span></div>
              <div><span style="color: #8c8c8c;">实时功率：</span><span style="color: #1890ff; font-weight: 600;">${data.power}kW</span></div>
              <div><span style="color: #8c8c8c;">今日能耗：</span><span style="color: #52c41a; font-weight: 600;">${data.todayConsumption}kWh</span></div>
            </div>
          </div>
        `
      }
    },
    animationDuration: 1500,
    animationEasing: 'elasticOut',
    animationDelay: (idx: number) => idx * 100
  }

  if (layoutMode.value === 'tree') {
    // 树形布局
    const treeData = convertToTreeData()
    option.series = [{
      type: 'tree',
      data: [treeData],
      top: '12%',
      left: '8%',
      bottom: '8%',
      right: '8%',
      orient: 'TB',
      layout: 'orthogonal',
      symbolSize: (val: any, params: any) => {
        const size = params.data.symbolSize || 50
        return params.data.status === '异常' ? size + 10 : size
      },
      symbol: 'circle',
      label: {
        show: true,
        position: 'bottom',
        fontSize: 11,
        color: '#2c3e50',
        fontWeight: '500',
        distance: 8,
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        borderRadius: 4,
        padding: [2, 6]
      },
      leaves: {
        label: {
          position: 'bottom',
          verticalAlign: 'middle',
          align: 'center'
        }
      },
      emphasis: {
        focus: 'descendant',
        scale: 1.1,
        itemStyle: {
          shadowBlur: 20,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      expandAndCollapse: true,
      animationDuration: 800,
      animationDurationUpdate: 600,
      animationEasing: 'cubicOut',
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 3,
        shadowBlur: 8,
        shadowColor: 'rgba(0, 0, 0, 0.1)'
      },
      lineStyle: {
        color: '#d9d9d9',
        width: 2,
        curveness: 0.2,
        shadowBlur: 4,
        shadowColor: 'rgba(0, 0, 0, 0.1)'
      }
    }]
  } else {
    // 力导向布局
    option.series = [{
      type: 'graph',
      layout: 'force',
      data: filteredTopologyData.value.nodes.map(node => {
        // 根据节点层级设置不同的颜色和大小
        const levelColors = {
          0: { color: '#1890ff', gradient: ['#40a9ff', '#1890ff'] }, // 根节点 - 蓝色
          1: { color: '#52c41a', gradient: ['#73d13d', '#52c41a'] }, // 一级 - 绿色
          2: { color: '#fa8c16', gradient: ['#ffa940', '#fa8c16'] }, // 二级 - 橙色
          3: { color: '#722ed1', gradient: ['#9254de', '#722ed1'] }  // 三级 - 紫色
        }

        const levelConfig = levelColors[node.level] || levelColors[0]
        const isAbnormal = node.status === '异常'

        return {
          ...node,
          value: node.power,
          symbolSize: (node.symbolSize || 50) + (isAbnormal ? 8 : 0),
          itemStyle: {
            color: isAbnormal ? '#ff4d4f' : {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                { offset: 0, color: levelConfig.gradient[0] },
                { offset: 1, color: levelConfig.gradient[1] }
              ]
            },
            borderColor: isAbnormal ? '#ff7875' : '#fff',
            borderWidth: isAbnormal ? 4 : 3,
            shadowBlur: isAbnormal ? 15 : 8,
            shadowColor: isAbnormal ? 'rgba(255, 77, 79, 0.4)' : 'rgba(0, 0, 0, 0.1)',
            shadowOffsetY: 2
          },
          category: node.level
        }
      }),
      links: filteredTopologyData.value.links.map(link => ({
        ...link,
        lineStyle: {
          color: '#bfbfbf',
          width: 2,
          curveness: 0.2,
          opacity: 0.8
        }
      })),
      categories: [
        { name: '配电设备', itemStyle: { color: '#1890ff' } },
        { name: '建筑分项', itemStyle: { color: '#52c41a' } },
        { name: '楼层分项', itemStyle: { color: '#fa8c16' } },
        { name: '设备分项', itemStyle: { color: '#722ed1' } }
      ],
      roam: true,
      focusNodeAdjacency: true,
      draggable: true,
      force: {
        repulsion: [100, 500],
        gravity: 0.1,
        edgeLength: [80, 200],
        layoutAnimation: true
      },
      label: {
        show: true,
        position: 'bottom',
        fontSize: 11,
        color: '#2c3e50',
        fontWeight: '500',
        distance: 8,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 4,
        padding: [2, 6],
        shadowBlur: 4,
        shadowColor: 'rgba(0, 0, 0, 0.1)'
      },
      lineStyle: {
        color: '#d9d9d9',
        curveness: 0.3,
        width: 2,
        opacity: 0.8
      },
      emphasis: {
        focus: 'adjacency',
        scale: 1.15,
        itemStyle: {
          shadowBlur: 25,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        lineStyle: {
          width: 4,
          opacity: 1,
          color: '#40a9ff'
        },
        label: {
          fontSize: 12,
          fontWeight: 'bold'
        }
      },
      select: {
        itemStyle: {
          borderColor: '#40a9ff',
          borderWidth: 4
        }
      }
    }]
  }

  chart.setOption(option, true)

  // 添加点击事件
  chart.off('click')
  chart.on('click', (params: any) => {
    if (params.data) {
      selectedNode.value = {
        ...params.data,
        color: params.data.itemStyle?.color || '#409eff'
      }
    }
  })
}

// 转换为树形数据结构
const convertToTreeData = () => {
  const nodes = filteredTopologyData.value.nodes
  const links = filteredTopologyData.value.links

  // 在下钻模式下，找到当前父节点作为根节点
  const rootNodeId = currentParentId.value
  const rootNode = nodes.find(node => node.id === rootNodeId)
  if (!rootNode) return {}

  // 构建树形结构
  const buildTree = (nodeId: string): any => {
    const node = nodes.find(n => n.id === nodeId)
    if (!node) return null

    const children = links
      .filter(link => link.source === nodeId)
      .map(link => buildTree(link.target))
      .filter(child => child !== null)

    // 根据节点层级设置不同的颜色和样式
    const levelColors = {
      0: { color: '#1890ff', gradient: ['#40a9ff', '#1890ff'] },
      1: { color: '#52c41a', gradient: ['#73d13d', '#52c41a'] },
      2: { color: '#fa8c16', gradient: ['#ffa940', '#fa8c16'] },
      3: { color: '#722ed1', gradient: ['#9254de', '#722ed1'] }
    }

    const levelConfig = levelColors[node.level] || levelColors[0]
    const isAbnormal = node.status === '异常'

    return {
      name: node.name,
      value: node.power,
      symbolSize: (node.symbolSize || 50) + (isAbnormal ? 8 : 0),
      itemStyle: {
        color: isAbnormal ? '#ff4d4f' : {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: levelConfig.gradient[0] },
            { offset: 1, color: levelConfig.gradient[1] }
          ]
        },
        borderColor: isAbnormal ? '#ff7875' : '#fff',
        borderWidth: isAbnormal ? 4 : 3,
        shadowBlur: isAbnormal ? 15 : 8,
        shadowColor: isAbnormal ? 'rgba(255, 77, 79, 0.4)' : 'rgba(0, 0, 0, 0.1)',
        shadowOffsetY: 2
      },
      ...node,
      children: children.length > 0 ? children : undefined
    }
  }

  return buildTree(rootNodeId)
}

// 初始化历史图表
const initHistoryChart = () => {
  if (!historyChart.value) return

  const chart = echarts.init(historyChart.value)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)',
      axisLabel: { formatter: '{value}' }
    },
    series: [
      {
        name: '实时功率',
        type: 'line',
        smooth: true,
        data: [420, 380, 650, 890, 1200, 980, 560],
        itemStyle: { color: '#409eff' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.1)' }
      }
    ]
  }

  chart.setOption(option)
}



// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // F5 或 Ctrl+R 刷新
  if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
    event.preventDefault()
    if (!loading.value) {
      refreshTopology(false)
    }
  }
  // Ctrl+Shift+R 重置到根视图
  else if (event.ctrlKey && event.shiftKey && event.key === 'R') {
    event.preventDefault()
    if (!loading.value) {
      refreshTopology(true)
    }
  }
}

onMounted(() => {
  // 设置默认日期范围
  const now = new Date()
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  dateRange.value = [lastWeek, now]

  // 初始化下钻状态
  resetDrillState()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  nextTick(() => {
    initTopologyChart()
  })
})

onUnmounted(() => {
  // 清理自动刷新定时器
  stopAutoRefresh()

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.topology-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.drill-path {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.back-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.path-text {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  background: rgba(108, 117, 125, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.loading-indicator .el-icon {
  animation: spin 1s linear infinite;
}

.control-panel {
  margin-bottom: 20px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.auto-refresh-controls {
  display: flex;
  align-items: center;
}

.refresh-status {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 12px;
  color: #8c8c8c;
}

.auto-refresh-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.auto-refresh-indicator .el-icon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



.node-info-card {
  height: 100%;
}

.node-info-card .el-card__body {
  height: 600px;
  padding: 0;
  overflow: hidden;
}

.node-info-content {
  height: 600px;
  padding: 16px;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.node-info-content::-webkit-scrollbar {
  width: 6px;
}

.node-info-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.node-info-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.node-info-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.node-info {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
  height: 100%;
}

/* 节点头部样式 */
.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 10px;
  border: 1px solid #e1e8ed;
  position: relative;
  overflow: hidden;
}

.node-icon-wrapper {
  position: relative;
  flex-shrink: 0;
}

.node-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--node-color), var(--node-color));
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.node-status-indicator {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid white;
  z-index: 2;
}

.node-status-indicator.status-normal {
  background: #52c41a;
  box-shadow: 0 0 6px rgba(82, 196, 26, 0.6);
}

.node-status-indicator.status-error {
  background: #ff4d4f;
  box-shadow: 0 0 6px rgba(255, 77, 79, 0.6);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.node-title {
  flex: 1;
  min-width: 0;
}

.node-title h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.node-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.node-type {
  font-size: 11px;
  color: #8c8c8c;
  background: rgba(140, 140, 140, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.node-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.node-status.status-normal {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.node-status.status-error {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 核心数据展示样式 */
.core-metrics {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.metric-card.primary {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 10px;
  color: white;
  box-shadow: 0 3px 12px rgba(24, 144, 255, 0.3);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.metric-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-number {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-text {
  font-size: 11px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 详细信息样式 */
.detail-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e1e8ed;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e1e8ed;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.info-value {
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
}

/* 操作按钮样式 */
.node-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  padding-top: 8px;
}

.action-btn {
  flex: 1;
  height: 32px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  transition: all 0.3s ease;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.action-btn.primary:disabled {
  background: #d9d9d9;
  color: #8c8c8c;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

.action-btn.secondary {
  background: white;
  color: #2c3e50;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn.secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
  color: #1890ff;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  padding: 20px;
}

.empty-state {
  text-align: center;
  max-width: 280px;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 24px;
}

.empty-tips {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #606266;
  text-align: left;
}

.tip-item span {
  font-weight: 500;
}

.topology-chart-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.topology-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  pointer-events: none;
  z-index: 1;
}

.topology-chart-container > div {
  position: relative;
  z-index: 2;
}

.stats-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px 20px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  position: relative;
  overflow: hidden;
}

.stats-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 40px 40px, 25px 25px;
  pointer-events: none;
}

.stat-card {
  flex: 1;
  min-width: 180px;
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.stat-card:not(:last-child) {
  margin-right: 0;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--icon-color, #409eff), var(--icon-color-dark, #1890ff));
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.stat-card:hover .stat-icon::before {
  opacity: 1;
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-content {
  flex: 1;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 11px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-overview {
    flex-direction: column;
    gap: 15px;
  }

  .stat-card {
    min-width: auto;
    margin-right: 0;
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
  }

  .stat-card:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .control-group {
    margin-bottom: 15px;
  }
}
</style>
