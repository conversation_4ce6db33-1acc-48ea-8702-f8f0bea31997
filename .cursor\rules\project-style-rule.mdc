---
description: 
globs: 
alwaysApply: true
---
# 能源管理系统样式规范

## 1. 布局规范

### 1.1 页面结构
- 面包屑导航放置在内容区上方，左对齐
- 内容区域与顶部导航栏保持10px间距
- 各模块之间保持10px-20px的间距

### 1.2 卡片样式
- 卡片使用白色背景，轻微阴影效果
- 卡片标题栏与内容区分离
- 标题文字不换行显示（使用no-wrap类）

## 2. 导航元素

### 2.1 面包屑导航
- 可点击项使用黑色（#000000）
- 不可点击项使用灰色（#909399）
- 面包屑间距：上下内边距10px，下外边距10px

## 3. 数据展示模块

### 3.1 总览模块
- 能源项目横向排列，均分容器宽度
- 各项目间使用浅色分隔线（#ebeef5）分隔
- 图标和文字居中对齐
- 图标使用50px×50px的圆形背景，不同能源类型使用不同颜色
- 数值使用18px粗体，趋势上升使用红色，下降使用绿色

### 3.2 统计概览模块
- 整体容器使用浅灰色背景（#fafafa），圆角8px，内边距30px 20px
- 统计项目横向排列，使用flexbox布局均分容器宽度
- 每个统计项目最小宽度180px，左右内边距15px
- 统计项目间使用1px浅灰色分隔线（#ebeef5）分隔，分隔线高度为项目高度的60%
- 图标区域：50px圆形背景，使用主题色+20%透明度作为背景色，2px边框使用主题色
- 图标大小28px，颜色使用对应的主题色
- 文字信息区域：
  - 名称：13px字体，#606266颜色，底部间距6px，字重500
  - 数值和单位：同行显示，使用flex布局baseline对齐，间距4px
  - 数值：28px粗体（字重700），#303133颜色，行高1
  - 单位：12px字体，#909399颜色，字重500
  - 趋势：11px字体，字重500，上升红色（#f56c6c），下降绿色（#67c23a）
- 响应式设计：小屏幕（≤768px）时垂直排列，移除分隔线，每项添加白色背景和阴影

### 3.3 图表规范
- 饼图使用环形样式，内外半径比例为40%:70%
- 饼图图例位于图表底部，水平排列
- 鼠标悬停时显示详细数据，字体大小18px，粗体
- 折线图使用面积样式，透明度0.3
- 图表容器高度统一为300px

### 3.4 表格样式
- 表头背景色使用浅灰色（#f5f7fa）
- 表头文字颜色为深灰色（#606266）
- 所有单元格内容居中对齐
- 表格使用边框和条纹样式增强可读性
- 状态标签使用不同颜色区分：正常为绿色，故障为红色
- 进度条组件宽度为单元格的90%，居中显示
- 无特殊情况下，表格要放在页面的最下方

## 4. 交互元素

### 4.1 选择器和按钮
- 时间选择框宽度自适应，最小宽度100px
- 按钮使用小尺寸（size="small"）
- 主要操作按钮使用主题色

### 4.2 标签样式
- 设备类型标签根据类型使用不同颜色：
  - 光伏板：绿色（success）
  - 风力发电：蓝色（primary）
  - 水力发电：灰蓝色（info）
- 效率进度条根据数值使用不同颜色：
  - ≥80%：绿色（#67c23a）
  - ≥60%：蓝色（#409eff）
  - ≥40%：橙色（#e6a23c）
  - <40%：红色（#f56c6c）

## 5. 响应式设计

### 5.1 小屏幕适配
- 在小屏幕设备上（≤768px），能源项目垂直排列
- 垂直排列时，图标和文字左对齐
- 垂直排列时，使用底部边框替代右侧边框作为分隔

## 6. 颜色规范

### 6.1 能源类型颜色
- 电：蓝色（#409eff）
- 水：绿色（#67c23a）
- 气：橙色（#e6a23c）
- 热：红色（#f56c6c）
- 冷：灰色（#909399）
- 总发电量：蓝色（#409eff）
- 光伏发电：红色（#f56c6c）
- 风力发电：绿色（#67c23a）
- 抵碳量：灰色（#909399）

### 6.2 图表颜色
- 排名前三项使用红色背景（#f56c6c）
- 抵碳量对比图使用渐变色：绿色、蓝色、红色


这些样式规范确保了整个能源管理系统界面的一致性和专业性，提高了用户体验和数据可读性。通过统一的颜色方案、间距和对齐方式，使界面更加美观和易于使用。