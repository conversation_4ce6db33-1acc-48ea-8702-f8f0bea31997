<template>
  <div class="load-prediction" v-loading="loading" element-loading-text="正在加载预测数据...">
    <BreadCrumb :items="breadcrumbItems" />

    <!-- 预测总览 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="prediction-overview">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>负荷预测分析中心</h2>
                <div class="prediction-status" :class="predictionStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(predictionStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(predictionStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>

          <div class="prediction-metrics">
            <div class="metric-item">
              <div class="metric-icon current">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ predictionStats.currentLoad }}</div>
                <div class="metric-label">当前负荷</div>
                <div class="metric-unit">kW</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon predicted">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ predictionStats.nextHourLoad }}</div>
                <div class="metric-label">下小时预测</div>
                <div class="metric-unit">kW</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon peak">
                <el-icon><WarningFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ predictionStats.todayPeak }}</div>
                <div class="metric-label">今日峰值</div>
                <div class="metric-unit">kW</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon accuracy">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ predictionStats.accuracy }}</div>
                <div class="metric-label">预测准确率</div>
                <div class="metric-unit">%</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时负荷数据 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Timer /></el-icon>
                实时负荷数据
              </h3>
              <div class="header-controls">
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>
              </div>
            </div>
          </template>

          <div class="realtime-grid">
            <div class="data-item" v-for="item in realtimeData" :key="item.id">
              <div class="data-header">
                <div class="data-name">{{ item.name }}</div>
                <div class="data-trend" :class="item.trend">
                  <el-icon>
                    <component :is="getTrendIcon(item.trend)" />
                  </el-icon>
                  <span>{{ item.change }}%</span>
                </div>
              </div>
              <div class="data-value">
                <span class="value">{{ item.value }}</span>
                <span class="unit">{{ item.unit }}</span>
              </div>
              <div class="data-time">{{ item.updateTime }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 负荷预测图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                负荷预测图表
              </h3>
              <div class="header-controls">
                <el-select v-model="selectedTimeRange" size="small" style="width: 120px;">
                  <el-option label="24小时" value="24h" />
                  <el-option label="7天" value="7d" />
                  <el-option label="30天" value="30d" />
                </el-select>
              </div>
            </div>
          </template>

          <div class="chart-container">
            <div class="chart-wrapper">
              <div class="chart-header">
                <div class="chart-legend">
                  <div class="legend-item">
                    <div class="legend-color actual"></div>
                    <span>实际负荷</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color predicted"></div>
                    <span>预测负荷</span>
                  </div>
                </div>
              </div>
              <div class="chart-content" ref="predictionChart">
                <!-- 模拟图表数据展示 -->
                <div class="chart-grid">
                  <div class="chart-line actual-line"></div>
                  <div class="chart-line predicted-line"></div>
                  <div class="chart-points">
                    <div class="chart-point" v-for="i in 24" :key="i"
                         :style="{ left: (i * 4) + '%', bottom: (Math.random() * 60 + 20) + '%' }">
                    </div>
                  </div>
                </div>
                <div class="chart-axes">
                  <div class="y-axis">
                    <div class="axis-label" v-for="value in [0, 500, 1000, 1500, 2000]" :key="value">
                      {{ value }}kW
                    </div>
                  </div>
                  <div class="x-axis">
                    <div class="axis-label" v-for="hour in ['00:00', '06:00', '12:00', '18:00', '24:00']" :key="hour">
                      {{ hour }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预测模型管理 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Setting /></el-icon>
                预测模型管理
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showCreateModelDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建模型
                </el-button>
              </div>
            </div>
          </template>

          <div class="model-grid">
            <div class="model-card" v-for="model in predictionModels" :key="model.id">
              <div class="model-header">
                <div class="model-icon" :class="model.status">
                  <el-icon>
                    <component :is="getModelIcon(model.type)" />
                  </el-icon>
                </div>
                <div class="model-info">
                  <div class="model-name">{{ model.name }}</div>
                  <div class="model-type">{{ model.type }}</div>
                </div>
                <div class="model-status">
                  <el-tag :type="getModelStatusType(model.status)" size="small">
                    {{ getModelStatusText(model.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="model-details">
                <div class="detail-item">
                  <span class="detail-label">准确率：</span>
                  <span class="detail-value success">{{ model.accuracy }}%</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">训练数据：</span>
                  <span class="detail-value">{{ model.trainingData }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">最后训练：</span>
                  <span class="detail-value">{{ model.lastTrained }}</span>
                </div>
              </div>
              <div class="model-actions">
                <el-button type="primary" size="small" @click="trainModel(model)">
                  训练
                </el-button>
                <el-button type="success" size="small" @click="testModel(model)">
                  测试
                </el-button>
                <el-button
                  :type="model.status === 'active' ? 'warning' : 'info'"
                  size="small"
                  @click="toggleModel(model)"
                >
                  {{ model.status === 'active' ? '停用' : '启用' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 历史预测分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                历史预测分析
              </h3>
              <div class="header-controls">
                <el-date-picker
                  v-model="selectedDate"
                  type="date"
                  placeholder="选择日期"
                  size="small"
                  style="width: 150px;"
                />
                <el-button type="info" size="small" @click="exportReport">
                  <el-icon><Operation /></el-icon>
                  导出报告
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="historicalPredictions" border stripe style="width: 100%">
            <el-table-column prop="date" label="日期" min-width="120" align="center" />
            <el-table-column prop="timeSlot" label="时间段" min-width="140" align="center" />
            <el-table-column prop="predictedLoad" label="预测负荷(kW)" min-width="140" align="center" />
            <el-table-column prop="actualLoad" label="实际负荷(kW)" min-width="140" align="center" />
            <el-table-column label="误差率" min-width="100" align="center">
              <template #default="{ row }">
                <span :class="getErrorClass(row.errorRate)">{{ row.errorRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="model" label="预测模型" min-width="180" align="center" />
            <el-table-column label="准确度" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getAccuracyType(row.accuracy)" size="small">
                  {{ getAccuracyText(row.accuracy) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="weather" label="天气条件" min-width="120" align="center" />
            <el-table-column prop="temperature" label="温度(°C)" min-width="100" align="center" />
            <el-table-column label="操作" width="150" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="viewDetail(row)">
                  详情
                </el-button>
                <el-button type="info" size="small" @click="analyzeError(row)">
                  分析
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalPredictions"
            layout="total, sizes, prev, pager, next, jumper"
            style="margin-top: 20px; justify-content: center;"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  Monitor,
  Timer,
  WarningFilled,
  SuccessFilled,
  CircleCloseFilled,
  Setting,
  Operation
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '智能控制中心', to: '/intelligent-control' },
  { text: '负荷预测' }
])

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const predictionStatus = ref('normal')
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const selectedTimeRange = ref('24h')
const selectedDate = ref(new Date())
const showCreateModelDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalPredictions = ref(100)
const predictionChart = ref()

// 预测统计数据
const predictionStats = reactive({
  currentLoad: 1245,
  nextHourLoad: 1320,
  todayPeak: 1580,
  accuracy: 94.2
})

// 实时数据
const realtimeData = ref([
  {
    id: 'RT001',
    name: '总负荷',
    value: 1245,
    unit: 'kW',
    change: '****',
    trend: 'up',
    updateTime: '14:25:30'
  },
  {
    id: 'RT002',
    name: '照明负荷',
    value: 320,
    unit: 'kW',
    change: '-1.2',
    trend: 'down',
    updateTime: '14:25:28'
  },
  {
    id: 'RT003',
    name: '空调负荷',
    value: 680,
    unit: 'kW',
    change: '+4.1',
    trend: 'up',
    updateTime: '14:25:25'
  },
  {
    id: 'RT004',
    name: '设备负荷',
    value: 245,
    unit: 'kW',
    change: '0.0',
    trend: 'stable',
    updateTime: '14:25:22'
  }
])

// 预测模型数据
const predictionModels = ref([
  {
    id: 'MODEL001',
    name: 'LSTM深度学习模型',
    type: '深度学习',
    status: 'active',
    accuracy: 94.2,
    trainingData: '30天',
    lastTrained: '2024-01-15 10:00:00'
  },
  {
    id: 'MODEL002',
    name: 'ARIMA时间序列模型',
    type: '时间序列',
    status: 'inactive',
    accuracy: 89.5,
    trainingData: '60天',
    lastTrained: '2024-01-14 15:30:00'
  },
  {
    id: 'MODEL003',
    name: '支持向量机模型',
    type: '机器学习',
    status: 'training',
    accuracy: 91.8,
    trainingData: '45天',
    lastTrained: '2024-01-13 09:15:00'
  },
  {
    id: 'MODEL004',
    name: '随机森林模型',
    type: '机器学习',
    status: 'inactive',
    accuracy: 87.3,
    trainingData: '90天',
    lastTrained: '2024-01-12 16:45:00'
  }
])

// 历史预测数据
const historicalPredictions = ref([
  {
    date: '2024-01-15',
    timeSlot: '14:00-15:00',
    predictedLoad: 1250,
    actualLoad: 1245,
    errorRate: 0.4,
    accuracy: 'high',
    model: 'LSTM深度学习',
    weather: '晴',
    temperature: 22
  },
  {
    date: '2024-01-15',
    timeSlot: '13:00-14:00',
    predictedLoad: 1180,
    actualLoad: 1200,
    errorRate: 1.7,
    accuracy: 'high',
    model: 'LSTM深度学习',
    weather: '晴',
    temperature: 21
  },
  {
    date: '2024-01-15',
    timeSlot: '12:00-13:00',
    predictedLoad: 1350,
    actualLoad: 1280,
    errorRate: 5.5,
    accuracy: 'medium',
    model: 'ARIMA时间序列',
    weather: '多云',
    temperature: 20
  },
  {
    date: '2024-01-15',
    timeSlot: '11:00-12:00',
    predictedLoad: 1420,
    actualLoad: 1380,
    errorRate: 2.9,
    accuracy: 'high',
    model: 'LSTM深度学习',
    weather: '多云',
    temperature: 19
  }
])

// 工具函数
const getStatusIcon = (status: string) => {
  const iconMap = {
    'normal': SuccessFilled,
    'warning': WarningFilled,
    'danger': CircleCloseFilled
  }
  return iconMap[status] || SuccessFilled
}

const getStatusText = (status: string) => {
  const textMap = {
    'normal': '预测正常',
    'warning': '需要关注',
    'danger': '预测异常'
  }
  return textMap[status] || '未知状态'
}

const getTrendIcon = (trend: string) => {
  const iconMap = {
    'up': WarningFilled,
    'down': SuccessFilled,
    'stable': Monitor
  }
  return iconMap[trend] || Monitor
}

const getModelIcon = (type: string) => {
  const iconMap = {
    '深度学习': Setting,
    '时间序列': Timer,
    '机器学习': Operation
  }
  return iconMap[type] || Setting
}

const getModelStatusType = (status: string) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'info',
    'training': 'warning'
  }
  return typeMap[status] || 'info'
}

const getModelStatusText = (status: string) => {
  const textMap = {
    'active': '运行中',
    'inactive': '未激活',
    'training': '训练中'
  }
  return textMap[status] || '未知'
}

const getErrorClass = (errorRate: number) => {
  if (errorRate <= 2) return 'error-low'
  if (errorRate <= 5) return 'error-medium'
  return 'error-high'
}

const getAccuracyType = (accuracy: string) => {
  const typeMap = {
    'high': 'success',
    'medium': 'warning',
    'low': 'danger'
  }
  return typeMap[accuracy] || 'info'
}

const getAccuracyText = (accuracy: string) => {
  const textMap = {
    'high': '高精度',
    'medium': '中精度',
    'low': '低精度'
  }
  return textMap[accuracy] || '未知'
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  ElMessage.success(`自动刷新已${autoRefresh.value ? '开启' : '关闭'}`)
}

const trainModel = (model: any) => {
  ElMessage.info(`开始训练模型: ${model.name}`)
  model.status = 'training'
}

const testModel = (model: any) => {
  ElMessage.info(`测试模型: ${model.name}`)
}

const toggleModel = (model: any) => {
  if (model.status === 'active') {
    model.status = 'inactive'
    ElMessage.success(`模型 ${model.name} 已停用`)
  } else {
    // 停用其他活跃模型
    predictionModels.value.forEach(m => {
      if (m.id !== model.id && m.status === 'active') {
        m.status = 'inactive'
      }
    })
    model.status = 'active'
    ElMessage.success(`模型 ${model.name} 已启用`)
  }
}

const exportReport = () => {
  ElMessage.success('预测报告导出成功')
}

const viewDetail = (row: any) => {
  ElMessage.info(`查看详情: ${row.date} ${row.timeSlot}`)
}

const analyzeError = (row: any) => {
  ElMessage.info(`分析误差: ${row.date} ${row.timeSlot}`)
}
</script>

<style scoped>
.load-prediction {
  padding: 0;
}

/* 预测总览样式 */
.prediction-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.prediction-overview :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.prediction-overview :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.prediction-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.prediction-status.normal .status-icon {
  color: #67c23a;
}

.prediction-status.warning .status-icon {
  color: #e6a23c;
}

.prediction-status.danger .status-icon {
  color: #f56c6c;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.prediction-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.metric-icon.current {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border: 2px solid #67c23a;
}

.metric-icon.predicted {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border: 2px solid #409eff;
}

.metric-icon.peak {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border: 2px solid #e6a23c;
}

.metric-icon.accuracy {
  background: rgba(144, 147, 153, 0.2);
  color: #909399;
  border: 2px solid #909399;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
}

.metric-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

/* 图表样式 */
.chart-container {
  height: 400px;
  position: relative;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  padding-left: 60px;
  padding-bottom: 40px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 3px;
  border-radius: 2px;
}

.legend-color.actual {
  background: #409eff;
}

.legend-color.predicted {
  background: #67c23a;
}

.chart-content {
  position: relative;
  height: calc(100% - 50px);
  background: linear-gradient(to bottom,
    transparent 0%,
    rgba(64, 158, 255, 0.05) 20%,
    rgba(64, 158, 255, 0.1) 40%,
    rgba(64, 158, 255, 0.05) 60%,
    transparent 100%);
  border-radius: 4px;
}

.chart-grid {
  position: relative;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px);
  background-size: 4.16% 20%;
}

.chart-line {
  position: absolute;
  width: 100%;
  height: 3px;
  border-radius: 2px;
}

.chart-line.actual-line {
  background: linear-gradient(to right,
    #409eff 0%,
    #409eff 60%,
    transparent 60%);
  top: 40%;
  animation: drawLine 2s ease-in-out;
}

.chart-line.predicted-line {
  background: linear-gradient(to right,
    transparent 0%,
    transparent 60%,
    #67c23a 60%,
    #67c23a 100%);
  top: 35%;
  border-style: dashed;
  animation: drawLine 2s ease-in-out 0.5s both;
}

.chart-points {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #409eff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  animation: fadeIn 0.5s ease-in-out;
}

.chart-axes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.y-axis {
  position: absolute;
  left: -55px;
  top: 0;
  height: 100%;
  width: 50px;
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  padding: 10px 0;
}

.x-axis {
  position: absolute;
  bottom: -35px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.axis-label {
  font-size: 12px;
  color: #909399;
}

.y-axis .axis-label {
  text-align: right;
  width: 100%;
}

.x-axis .axis-label {
  text-align: center;
}

@keyframes drawLine {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 50%) scale(0);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 50%) scale(1);
  }
}

/* 实时数据样式 */
.realtime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.data-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.data-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.data-name {
  font-weight: 600;
  color: #303133;
}

.data-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.data-trend.up {
  color: #f56c6c;
}

.data-trend.down {
  color: #67c23a;
}

.data-trend.stable {
  color: #909399;
}

.data-value {
  margin-bottom: 8px;
}

.data-value .value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
}

.data-value .unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.data-time {
  font-size: 12px;
  color: #c0c4cc;
}

/* 模型管理样式 */
.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.model-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.model-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.model-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.model-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.model-icon.active {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.model-icon.inactive {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.model-icon.training {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.model-info {
  flex: 1;
}

.model-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.model-type {
  font-size: 12px;
  color: #909399;
}

.model-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.detail-value.success {
  color: #67c23a;
}

.model-actions {
  display: flex;
  gap: 8px;
}

.model-actions .el-button {
  flex: 1;
}

/* 表格样式 */
.error-low {
  color: #67c23a;
  font-weight: 600;
}

.error-medium {
  color: #e6a23c;
  font-weight: 600;
}

.error-high {
  color: #f56c6c;
  font-weight: 600;
}

/* 通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .prediction-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .model-grid {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .header-controls .el-select,
  .header-controls .el-button,
  .header-controls .el-date-picker {
    width: 100%;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
