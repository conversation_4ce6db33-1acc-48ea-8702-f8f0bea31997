import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 3003,
    strictPort: true, // 端口被占用时不会自动尝试下一个可用端口
    cors: true, // 启用CORS
    // 可选：如果需要HTTPS，可以配置以下选项
    // https: {
    //   key: fs.readFileSync('path/to/private.key'),
    //   cert: fs.readFileSync('path/to/certificate.crt')
    // }
  }
})
