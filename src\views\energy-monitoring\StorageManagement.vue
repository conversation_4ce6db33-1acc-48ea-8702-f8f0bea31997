<template>
  <div class="storage-management" v-loading="loading" element-loading-text="正在加载数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 储能统计概览 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <h3>储能统计概览</h3>
          <div class="header-controls">
            <el-date-picker
              v-model="selectedDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="handleDateRangeChange"
            />
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="refreshAllData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="storage-stats">
        <div class="stat-item" v-for="(item, index) in storageStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-value-container">
              <span class="stat-value">{{ item.value }}</span>
              <span class="stat-unit">{{ item.unit }}</span>
            </div>
            <div class="stat-trend" :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
              {{ item.comparePeriod }}{{ item.trend === 'up' ? '增长' : '下降' }} {{ item.trend === 'up' ? '+' : '-' }}{{ item.changeRate }}%
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 充放电数据趋势 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>充放电数据趋势</h3>
              <div class="header-controls">
                <el-radio-group v-model="timeRange" size="small" @change="handleTimeRangeChange">
                  <el-radio-button label="today">今天</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="chargingTrendChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>储能设备状态</h3>
            </div>
          </template>
          <div ref="deviceStatusChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备效率和容量分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>储能效率分析</h3>
            </div>
          </template>
          <div ref="efficiencyChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>容量预测</h3>
            </div>
          </template>
          <div ref="capacityPredictionChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备列表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>储能设备列表</h3>
          <div class="header-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索设备"
              size="small"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select
              v-model="statusFilter"
              placeholder="筛选状态"
              size="small"
              style="width: 120px; margin-right: 10px;"
              @change="handleStatusFilter"
            >
              <el-option label="全部" value="" />
              <el-option label="正常" value="正常" />
              <el-option label="充电中" value="充电中" />
              <el-option label="放电中" value="放电中" />
              <el-option label="故障" value="故障" />
            </el-select>
            <el-button type="primary" size="small" @click="addDevice">
              <el-icon><Plus /></el-icon>
              添加设备
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="filteredDevices" 
        style="width: 100%" 
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="deviceId" label="设备ID" min-width="80" align="center" />
        <el-table-column prop="deviceName" label="设备名称" min-width="120" align="center" />
        <el-table-column prop="deviceType" label="设备类型" min-width="90" align="center">
          <template #default="{ row }">
            <el-tag :type="getDeviceTypeColor(row.deviceType)" size="small">
              {{ row.deviceType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容量" min-width="80" align="center">
          <template #default="{ row }">
            {{ row.capacity }}kWh
          </template>
        </el-table-column>
        <el-table-column prop="currentCapacity" label="当前容量" min-width="100" align="center">
          <template #default="{ row }">
            {{ row.currentCapacity }}kWh
          </template>
        </el-table-column>
        <el-table-column prop="efficiency" label="效率" min-width="100" align="center">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.efficiency" 
              :color="getEfficiencyColor(row.efficiency)"
              :stroke-width="8"
              style="width: 90%"
            />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdate" label="最后更新" min-width="130" align="center" />
        <el-table-column label="操作" align="center" width="220">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDevice(row)" style="margin-right: 5px;">
              详情
            </el-button>
            <el-button type="warning" size="small" @click="editDevice(row)" style="margin-right: 5px;">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteDevice(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 异常预警 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>异常预警</h3>
        </div>
      </template>
      
      <div class="alert-list">
        <div class="alert-item" v-for="(alert, index) in alerts" :key="index">
          <div class="alert-icon">
            <el-icon :size="24" :color="alert.level === 'high' ? '#f56c6c' : alert.level === 'medium' ? '#e6a23c' : '#909399'">
              <Warning />
            </el-icon>
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ alert.time }}</div>
          </div>
          <div class="alert-actions">
            <el-button type="primary" size="small" @click="handleAlert(alert)">
              处理
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 添加设备对话框 -->
    <el-dialog
      v-model="showAddDeviceDialog"
      title="添加储能设备"
      width="600px"
      :before-close="handleCloseAddDialog"
    >
      <el-form ref="addDeviceFormRef" :model="newDevice" :rules="deviceRules" label-width="120px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="newDevice.deviceId" placeholder="请输入设备ID" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="newDevice.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="newDevice.deviceType" placeholder="请选择设备类型" style="width: 100%">
            <el-option label="锂电池" value="锂电池" />
            <el-option label="磷酸铁锂" value="磷酸铁锂" />
            <el-option label="钠离子" value="钠离子" />
          </el-select>
        </el-form-item>
        <el-form-item label="容量" prop="capacity">
          <el-input v-model.number="newDevice.capacity" placeholder="请输入容量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="当前容量" prop="currentCapacity">
          <el-input v-model.number="newDevice.currentCapacity" placeholder="请输入当前容量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="newDevice.status" placeholder="请选择设备状态" style="width: 100%">
            <el-option label="正常" value="正常" />
            <el-option label="充电中" value="充电中" />
            <el-option label="放电中" value="放电中" />
            <el-option label="故障" value="故障" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备效率" prop="efficiency">
          <el-input v-model.number="newDevice.efficiency" placeholder="请输入设备效率">
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseAddDialog">取消</el-button>
          <el-button type="primary" @click="handleAddDevice" :loading="addDeviceLoading">
            确认添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑设备对话框 -->
    <el-dialog
      v-model="showEditDeviceDialog"
      title="编辑储能设备"
      width="600px"
      :before-close="handleCloseEditDialog"
    >
      <el-form ref="editDeviceFormRef" :model="editingDevice" :rules="deviceRules" label-width="120px">
        <el-form-item label="设备ID" prop="deviceId">
          <el-input v-model="editingDevice.deviceId" placeholder="请输入设备ID" readonly />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="editingDevice.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-select v-model="editingDevice.deviceType" placeholder="请选择设备类型" style="width: 100%">
            <el-option label="锂电池" value="锂电池" />
            <el-option label="磷酸铁锂" value="磷酸铁锂" />
            <el-option label="钠离子" value="钠离子" />
          </el-select>
        </el-form-item>
        <el-form-item label="容量" prop="capacity">
          <el-input v-model.number="editingDevice.capacity" placeholder="请输入容量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="当前容量" prop="currentCapacity">
          <el-input v-model.number="editingDevice.currentCapacity" placeholder="请输入当前容量">
            <template #suffix>kWh</template>
          </el-input>
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="editingDevice.status" placeholder="请选择设备状态" style="width: 100%">
            <el-option label="正常" value="正常" />
            <el-option label="充电中" value="充电中" />
            <el-option label="放电中" value="放电中" />
            <el-option label="故障" value="故障" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备效率" prop="efficiency">
          <el-input v-model.number="editingDevice.efficiency" placeholder="请输入设备效率">
            <template #suffix>%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseEditDialog">取消</el-button>
          <el-button type="primary" @click="handleUpdateDevice" :loading="editDeviceLoading">
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="showDeviceDetailDialog"
      title="储能设备详情"
      width="800px"
    >
      <div class="device-detail" v-if="currentDevice">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">{{ currentDevice.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ currentDevice.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag :type="getDeviceTypeColor(currentDevice.deviceType)" size="small">
              {{ currentDevice.deviceType }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="容量">{{ currentDevice.capacity }}kWh</el-descriptions-item>
          <el-descriptions-item label="当前容量">{{ currentDevice.currentCapacity }}kWh</el-descriptions-item>
          <el-descriptions-item label="容量利用率">{{ Math.round((currentDevice.currentCapacity / currentDevice.capacity) * 100) }}%</el-descriptions-item>
          <el-descriptions-item label="设备效率">
            <el-progress 
              :percentage="currentDevice.efficiency" 
              :color="getEfficiencyColor(currentDevice.efficiency)"
              :stroke-width="6"
              style="width: 200px"
            />
          </el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="getStatusColor(currentDevice.status)" size="small">
              {{ currentDevice.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后更新" :span="2">{{ currentDevice.lastUpdate }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDeviceDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ArrowUp, ArrowDown, Download, Search, Plus, Warning, CircleCheck, Lightning, Delete, Edit, View, Monitor, Trophy, Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能源监控中心', to: '/energy-monitoring' },
  { text: '储能管理', to: '' }
])

// 初始化日期范围为过去7天
const today = new Date()
const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
const selectedDateRange = ref<[Date, Date]>([weekAgo, today])
const timeRange = ref('today')
const loading = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')

// 对话框状态
const showAddDeviceDialog = ref(false)
const showEditDeviceDialog = ref(false)
const showDeviceDetailDialog = ref(false)
const addDeviceLoading = ref(false)
const editDeviceLoading = ref(false)

// 表单引用
const addDeviceFormRef = ref()
const editDeviceFormRef = ref()

// 设备数据
const currentDevice = ref<any>(null)
const newDevice = ref({
  deviceId: '',
  deviceName: '',
  deviceType: '',
  capacity: 0,
  currentCapacity: 0,
  status: '正常',
  efficiency: 0,
  lastUpdate: ''
})
const editingDevice = ref({
  deviceId: '',
  deviceName: '',
  deviceType: '',
  capacity: 0,
  currentCapacity: 0,
  status: '正常',
  efficiency: 0,
  lastUpdate: ''
})

// 表单验证规则
const deviceRules = ref({
  deviceId: [
    { required: true, message: '请输入设备ID', trigger: 'blur' },
    { min: 3, max: 20, message: '设备ID长度在3到20个字符', trigger: 'blur' }
  ],
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '设备名称长度在2到50个字符', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  capacity: [
    { required: true, message: '请输入容量', trigger: 'blur' },
    { type: 'number', min: 1, message: '容量必须大于0', trigger: 'blur' }
  ],
  currentCapacity: [
    { required: true, message: '请输入当前容量', trigger: 'blur' },
    { type: 'number', min: 0, message: '当前容量不能小于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择设备状态', trigger: 'change' }
  ],
  efficiency: [
    { required: true, message: '请输入设备效率', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '设备效率在0-100之间', trigger: 'blur' }
  ]
})

// 图表引用
const chargingTrendChart = ref<HTMLDivElement>()
const deviceStatusChart = ref<HTMLDivElement>()
const efficiencyChart = ref<HTMLDivElement>()
const capacityPredictionChart = ref<HTMLDivElement>()

// 储能统计数据
const storageStats = ref([
  {
    name: '总容量',
    value: '2,580',
    unit: 'kWh',
    color: '#409eff',
    icon: Monitor,
    trend: 'up',
    changeRate: '3.2',
    comparePeriod: '较上期'
  },
  {
    name: '可用容量',
    value: '2,156',
    unit: 'kWh',
    color: '#67c23a',
    icon: CircleCheck,
    trend: 'up',
    changeRate: '1.8',
    comparePeriod: '较昨日'
  },
  {
    name: '充电功率',
    value: '450',
    unit: 'kW',
    color: '#e6a23c',
    icon: Lightning,
    trend: 'down',
    changeRate: '0.5',
    comparePeriod: '较昨日'
  },
  {
    name: '放电功率',
    value: '380',
    unit: 'kW',
    color: '#f56c6c',
    icon: Download,
    trend: 'up',
    changeRate: '2.1',
    comparePeriod: '较昨日'
  }
])

// 设备列表数据
const devices = ref([
  {
    deviceId: 'ST001',
    deviceName: '储能柜A1',
    deviceType: '锂电池',
    capacity: 500,
    currentCapacity: 420,
    efficiency: 95,
    status: '正常',
    lastUpdate: '2024-01-15 14:30:00'
  },
  {
    deviceId: 'ST002',
    deviceName: '储能柜A2',
    deviceType: '锂电池',
    capacity: 500,
    currentCapacity: 480,
    efficiency: 92,
    status: '充电中',
    lastUpdate: '2024-01-15 14:29:00'
  },
  {
    deviceId: 'ST003',
    deviceName: '储能柜B1',
    deviceType: '磷酸铁锂',
    capacity: 800,
    currentCapacity: 650,
    efficiency: 88,
    status: '放电中',
    lastUpdate: '2024-01-15 14:28:00'
  },
  {
    deviceId: 'ST004',
    deviceName: '储能柜B2',
    deviceType: '磷酸铁锂',
    capacity: 800,
    currentCapacity: 720,
    efficiency: 90,
    status: '正常',
    lastUpdate: '2024-01-15 14:27:00'
  },
  {
    deviceId: 'ST005',
    deviceName: '储能柜C1',
    deviceType: '钠离子',
    capacity: 300,
    currentCapacity: 0,
    efficiency: 45,
    status: '故障',
    lastUpdate: '2024-01-15 12:15:00'
  }
])

// 异常预警数据
const alerts = ref([
  {
    level: 'high',
    title: '储能柜C1电池温度异常',
    message: '设备温度超过安全阈值，请立即检查',
    time: '2024-01-15 14:25:00'
  },
  {
    level: 'medium',
    title: '储能柜A2充电效率下降',
    message: '充电效率低于85%，建议进行维护',
    time: '2024-01-15 13:45:00'
  },
  {
    level: 'low',
    title: '储能柜B1容量衰减',
    message: '容量衰减超过10%，请关注设备状态',
    time: '2024-01-15 12:30:00'
  }
])

// 筛选后的设备列表
const filteredDevices = computed(() => {
  return devices.value.filter(device => {
    const matchesSearch = device.deviceName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    const matchesStatus = statusFilter.value === '' || device.status === statusFilter.value
    return matchesSearch && matchesStatus
  })
})

// 获取设备类型颜色
const getDeviceTypeColor = (type: string) => {
  switch (type) {
    case '锂电池': return 'success'
    case '磷酸铁锂': return 'primary'
    case '钠离子': return 'warning'
    default: return 'default'
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '充电中': return 'primary'
    case '放电中': return 'warning'
    case '故障': return 'danger'
    default: return 'default'
  }
}

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 90) return '#67c23a'
  if (efficiency >= 80) return '#409eff'
  if (efficiency >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 初始化充放电趋势图表
const initChargingTrendChart = () => {
  const chart = echarts.init(chargingTrendChart.value)
  
  // 根据时间范围生成不同的数据
  let xAxisData: string[] = []
  let chargingData: number[] = []
  let dischargingData: number[] = []
  let netData: number[] = []
  
  switch (timeRange.value) {
    case 'today':
      xAxisData = ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00']
      chargingData = [200, 450, 380, 220, 150, 320, 480, 350]
      dischargingData = [100, 200, 350, 400, 450, 380, 280, 150]
      netData = [100, 250, 30, -180, -300, -60, 200, 200]
      break
    case 'week':
      xAxisData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      chargingData = [1800, 2200, 1900, 2400, 2100, 1600, 1400]
      dischargingData = [1500, 1800, 2100, 2300, 2200, 1900, 1300]
      netData = [300, 400, -200, 100, -100, -300, 100]
      break
    case 'month':
      xAxisData = ['第1周', '第2周', '第3周', '第4周']
      chargingData = [8500, 9200, 8800, 9000]
      dischargingData = [7800, 8500, 8900, 8600]
      netData = [700, 700, -100, 400]
      break
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker} ${item.seriesName}: ${item.value} kW<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['充电功率', '放电功率', '净功率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)'
    },
    series: [
      {
        name: '充电功率',
        type: 'line',
        data: chargingData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '放电功率',
        type: 'line',
        data: dischargingData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '净功率',
        type: 'line',
        data: netData,
        smooth: true,
        lineStyle: { type: 'dashed' },
        itemStyle: { color: '#409eff' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化设备状态图表
const initDeviceStatusChart = () => {
  const chart = echarts.init(deviceStatusChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['正常', '充电中', '放电中', '故障']
    },
    series: [
      {
        name: '设备状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 2, name: '正常', itemStyle: { color: '#67c23a' } },
          { value: 1, name: '充电中', itemStyle: { color: '#409eff' } },
          { value: 1, name: '放电中', itemStyle: { color: '#e6a23c' } },
          { value: 1, name: '故障', itemStyle: { color: '#f56c6c' } }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化效率图表
const initEfficiencyChart = () => {
  const chart = echarts.init(efficiencyChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['储能柜A1', '储能柜A2', '储能柜B1', '储能柜B2', '储能柜C1']
    },
    yAxis: {
      type: 'value',
      name: '效率(%)',
      max: 100
    },
    series: [
      {
        name: '充电效率',
        type: 'bar',
        data: [95, 92, 88, 90, 45],
        itemStyle: {
          color: function(params: any) {
            const value = params.value
            if (value >= 90) return '#67c23a'
            if (value >= 80) return '#409eff'
            if (value >= 60) return '#e6a23c'
            return '#f56c6c'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化容量预测图表
const initCapacityPredictionChart = () => {
  const chart = echarts.init(capacityPredictionChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['历史容量', '预测容量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月']
    },
    yAxis: {
      type: 'value',
      name: '容量(kWh)'
    },
    series: [
      {
        name: '历史容量',
        type: 'line',
        data: [2600, 2580, 2570, 2550, 2540, 2520, null, null],
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '预测容量',
        type: 'line',
        data: [null, null, null, null, null, 2520, 2500, 2480],
        smooth: true,
        lineStyle: { type: 'dashed' },
        itemStyle: { color: '#e6a23c' }
      }
    ]
  }
  chart.setOption(option)
}

// 根据日期范围生成数据
const generateDataByDateRange = (startDate: Date, endDate: Date) => {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const multiplier = Math.max(1, daysDiff / 7) // 基于天数差异调整数据量
  
  // 生成储能统计数据
  const baseStats = [
    { name: '总容量', baseValue: 2580, unit: 'kWh', color: '#409eff', icon: Monitor },
    { name: '可用容量', baseValue: 2156, unit: 'kWh', color: '#67c23a', icon: CircleCheck },
    { name: '充电功率', baseValue: 450, unit: 'kW', color: '#e6a23c', icon: Lightning },
    { name: '放电功率', baseValue: 380, unit: 'kW', color: '#f56c6c', icon: Download }
  ]
  
  return baseStats.map(stat => ({
    ...stat,
    value: (stat.baseValue * multiplier * (0.8 + Math.random() * 0.4)).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
    trend: Math.random() > 0.5 ? 'up' : 'down',
    changeRate: (Math.random() * 10).toFixed(1),
    comparePeriod: daysDiff <= 1 ? '较昨日' : daysDiff <= 7 ? '较上周' : '较上月'
  }))
}

// 处理日期范围变化
const handleDateRangeChange = async (dates: [Date, Date]) => {
  if (dates && dates.length === 2) {
    selectedDateRange.value = dates
    console.log('日期范围变化:', dates)
    
    // 根据日期范围重新生成数据
    await refreshAllData()
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  loading.value = true
  const [startDate, endDate] = selectedDateRange.value
  
  try {
    // 模拟异步加载数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新统计数据
    storageStats.value = generateDataByDateRange(startDate, endDate)
    
    // 重新初始化所有图表
    await nextTick(() => {
      initChargingTrendChart()
      initDeviceStatusChart()
      initEfficiencyChart()
      initCapacityPredictionChart()
    })
    
    console.log('储能数据刷新完成')
  } finally {
    loading.value = false
  }
}

// 处理时间范围变化
const handleTimeRangeChange = async (range: string) => {
  timeRange.value = range
  console.log('时间范围变化:', range)
  
  // 只重新初始化充放电趋势图表
  await nextTick(() => {
    initChargingTrendChart()
  })
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
}

// 处理状态筛选
const handleStatusFilter = () => {
  console.log('状态筛选:', statusFilter.value)
}

// 导出数据
const exportData = () => {
  const [startDate, endDate] = selectedDateRange.value
  const dateRange = `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
  
  // 创建CSV内容
  let csvContent = "数据类型,数值,单位,变化趋势,变化率,对比周期\n"
  
  storageStats.value.forEach(stat => {
    csvContent += `${stat.name},${stat.value},${stat.unit},${stat.trend === 'up' ? '上升' : '下降'},${stat.changeRate}%,${stat.comparePeriod}\n`
  })
  
  csvContent += "\n储能设备数据\n"
  csvContent += "设备ID,设备名称,设备类型,容量,当前容量,效率,状态,最后更新\n"
  
  filteredDevices.value.forEach(device => {
    csvContent += `${device.deviceId},${device.deviceName},${device.deviceType},${device.capacity},${device.currentCapacity},${device.efficiency}%,${device.status},${device.lastUpdate}\n`
  })
  
  csvContent += "\n异常预警数据\n"
  csvContent += "级别,标题,消息,时间\n"
  
  alerts.value.forEach(alert => {
    csvContent += `${alert.level},${alert.title},${alert.message},${alert.time}\n`
  })
  
  // 创建下载链接，添加BOM头支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `储能管理数据_${dateRange.replace(/\//g, '-')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('储能数据导出成功！')
  console.log('储能数据导出完成')
}

// 重置新设备表单
const resetNewDevice = () => {
  newDevice.value = {
    deviceId: '',
    deviceName: '',
    deviceType: '',
    capacity: 0,
    currentCapacity: 0,
    status: '正常',
    efficiency: 0,
    lastUpdate: ''
  }
}

// 添加设备
const addDevice = () => {
  resetNewDevice()
  showAddDeviceDialog.value = true
}

// 关闭添加设备对话框
const handleCloseAddDialog = () => {
  addDeviceFormRef.value?.resetFields()
  resetNewDevice()
  showAddDeviceDialog.value = false
}

// 处理添加设备
const handleAddDevice = () => {
  addDeviceFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      addDeviceLoading.value = true
      
      // 模拟异步添加设备
      setTimeout(() => {
        // 检查设备ID是否已存在
        const existingDevice = devices.value.find(device => device.deviceId === newDevice.value.deviceId)
        if (existingDevice) {
          ElMessage.error('设备ID已存在，请使用其他ID')
          addDeviceLoading.value = false
          return
        }
        
        // 添加设备到列表
        const newDeviceData = {
          ...newDevice.value,
          lastUpdate: new Date().toLocaleString('zh-CN')
        }
        devices.value.push(newDeviceData)
        
        // 显示成功消息
        ElMessage.success('储能设备添加成功！')
        
        // 关闭对话框
        handleCloseAddDialog()
        addDeviceLoading.value = false
        
        console.log('储能设备添加成功:', newDeviceData)
      }, 1000)
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 查看设备详情
const viewDevice = (device: any) => {
  currentDevice.value = device
  showDeviceDetailDialog.value = true
}

// 编辑设备
const editDevice = (device: any) => {
  editingDevice.value = { ...device }
  showEditDeviceDialog.value = true
}

// 关闭编辑设备对话框
const handleCloseEditDialog = () => {
  editDeviceFormRef.value?.resetFields()
  showEditDeviceDialog.value = false
}

// 处理更新设备
const handleUpdateDevice = () => {
  editDeviceFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      editDeviceLoading.value = true
      
      // 模拟异步更新设备
      setTimeout(() => {
        // 查找并更新设备
        const index = devices.value.findIndex(device => device.deviceId === editingDevice.value.deviceId)
        if (index !== -1) {
          devices.value[index] = {
            ...editingDevice.value,
            lastUpdate: new Date().toLocaleString('zh-CN')
          }
          
          // 显示成功消息
          ElMessage.success('储能设备更新成功！')
          
          // 关闭对话框
          handleCloseEditDialog()
          console.log('储能设备更新成功:', editingDevice.value)
        } else {
          ElMessage.error('设备不存在')
        }
        
        editDeviceLoading.value = false
      }, 1000)
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 删除设备
const deleteDevice = (device: any) => {
  ElMessageBox.confirm(
    `确定要删除设备 "${device.deviceName}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从列表中删除设备
    const index = devices.value.findIndex(d => d.deviceId === device.deviceId)
    if (index !== -1) {
      devices.value.splice(index, 1)
      ElMessage.success('储能设备删除成功！')
      console.log('储能设备删除成功:', device)
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 处理预警
const handleAlert = (alert: any) => {
  ElMessageBox.confirm(
    `确定要处理预警 "${alert.title}" 吗？`,
    '处理预警',
    {
      confirmButtonText: '确定处理',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 从预警列表中移除
    const index = alerts.value.findIndex(a => a.title === alert.title && a.time === alert.time)
    if (index !== -1) {
      alerts.value.splice(index, 1)
      ElMessage.success('预警处理成功！')
      console.log('预警处理成功:', alert)
    }
  }).catch(() => {
    ElMessage.info('已取消处理')
  })
}

onMounted(async () => {
  // 初始化数据
  await refreshAllData()
})
</script>

<style scoped>
.storage-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.storage-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 10px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: #ebeef5;
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 50%;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.alert-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.alert-message {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 5px;
}

.alert-time {
  font-size: 11px;
  color: #909399;
}

.alert-actions {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .storage-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .stat-item {
    flex: none;
    max-width: none;
    justify-content: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }
  
  .stat-item:not(:last-child)::after {
    display: none;
  }
  
  .stat-item:last-child {
    margin-bottom: 0;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .alert-item {
    flex-direction: column;
    text-align: center;
  }
  
  .alert-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style> 