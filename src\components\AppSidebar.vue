<template>
  <div class="app-sidebar" :class="{ 'collapsed': isCollapsed }">
    <el-menu
      :default-active="activeMenu"
      class="sidebar-menu"
      :collapse="isCollapsed"
      :router="true"
      :unique-opened="true"
    >
      <el-menu-item index="/homepage">
        <el-icon><House /></el-icon>
        <span>首页</span>
      </el-menu-item>
      
      <el-menu-item index="/data-dashboard">
        <el-icon><Monitor /></el-icon>
        <span>数据大屏</span>
      </el-menu-item>
      
      <el-sub-menu index="/energy-monitoring">
        <template #title>
          <el-icon><Monitor /></el-icon>
          <span>能源监控中心</span>
        </template>
        <el-menu-item index="/energy-monitoring/overview">能源总览</el-menu-item>
        <el-menu-item index="/energy-monitoring/green-power">绿电总览</el-menu-item>
        <el-menu-item index="/energy-monitoring/generation">发电管理</el-menu-item>
        <el-menu-item index="/energy-monitoring/storage">储能管理</el-menu-item>
        <el-menu-item index="/energy-monitoring/charging">充电桩管理</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="/energy-analysis">
        <template #title>
          <el-icon><TrendCharts /></el-icon>
          <span>能耗分析平台</span>
        </template>
        <el-menu-item index="/energy-analysis/topology">拓扑视图</el-menu-item>
        <el-menu-item index="/energy-analysis/trend">能耗趋势</el-menu-item>
        <el-menu-item index="/energy-analysis/comparison">能耗对比</el-menu-item>
        <el-menu-item index="/energy-analysis/reports">报告管理</el-menu-item>
        <el-menu-item index="/energy-analysis/exceptions">异常记录</el-menu-item>
        <el-menu-item index="/energy-analysis/rules">异常规则</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="/device-management">
        <template #title>
          <el-icon><Box /></el-icon>
          <span>设备资产管理</span>
        </template>
        <el-menu-item index="/device-management/list">设备列表</el-menu-item>
        <el-menu-item index="/device-management/maintenance">设备维护</el-menu-item>
        <el-menu-item index="/device-management/units">单元管理</el-menu-item>
        <el-menu-item index="/device-management/safety">安全监测</el-menu-item>
        <el-menu-item index="/device-management/protection">安全保护</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="/intelligent-control">
        <template #title>
          <el-icon><Cpu /></el-icon>
          <span>智能控制中心</span>
        </template>
        <el-menu-item index="/intelligent-control/strategy">策略管理</el-menu-item>
        <el-menu-item index="/intelligent-control/linkage">多系统联动</el-menu-item>
        <el-menu-item index="/intelligent-control/prediction">负荷预测</el-menu-item>
        <el-menu-item index="/intelligent-control/fault-prediction">故障预测</el-menu-item>
      </el-sub-menu>
      
      <el-sub-menu index="/settings">
        <template #title>
          <el-icon><Setting /></el-icon>
          <span>系统设置</span>
        </template>
        <el-menu-item index="/settings/basic">基础设置</el-menu-item>
        <el-menu-item index="/settings/alert">告警设置</el-menu-item>
        <el-menu-item index="/settings/data">数据设置</el-menu-item>
        <el-menu-item index="/settings/user">用户管理</el-menu-item>
        <el-menu-item index="/settings/platform-integration">平台对接</el-menu-item>
        <el-menu-item index="/settings/one-click-report">一键报送</el-menu-item>
        <el-menu-item index="/settings/about">关于系统</el-menu-item>
      </el-sub-menu>
    </el-menu>
    
    <div class="collapse-button" @click="toggleCollapse">
      <el-icon v-if="isCollapsed"><Expand /></el-icon>
      <el-icon v-else><Fold /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Lightning, 
  Monitor, 
  Setting,
  Expand,
  Fold,
  House,
  TrendCharts,
  Box,
  Cpu
} from '@element-plus/icons-vue'

const route = useRoute()
const isCollapsed = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 折叠/展开侧边栏
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}
</script>

<style scoped>
.app-sidebar {
  position: fixed;
  top: var(--header-height);
  left: 0;
  height: calc(100vh - var(--header-height));
  width: var(--sidebar-width);
  background-color: #FFFFFF;
  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);
  transition: width 0.3s;
  z-index: 90;
  display: flex;
  flex-direction: column;
}

.app-sidebar.collapsed {
  width: 64px;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
}

.collapse-button {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-top: 1px solid var(--border-color);
  color: var(--text-color-light);
}

.collapse-button:hover {
  background-color: #F5F7FA;
}
</style>