<template>
  <div class="device-maintenance" v-loading="loading" element-loading-text="正在加载维护数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 维护管理操作 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>维护管理</h3>
          <div class="header-controls">
            <el-select v-model="selectedBuilding" size="small" placeholder="选择建筑" style="width: 120px;" @change="handleBuildingChange">
              <el-option label="全部建筑" value="" />
              <el-option label="办公大楼A" value="building-a" />
              <el-option label="办公大楼B" value="building-b" />
              <el-option label="生产车间" value="workshop" />
              <el-option label="配套设施" value="facilities" />
            </el-select>
            <el-button type="primary" size="small" @click="addMaintenancePlan">
              <el-icon><Plus /></el-icon>
              新建计划
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="warning" size="small" @click="batchOperation">
              <el-icon><Operation /></el-icon>
              批量操作
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <label>维护类型：</label>
            <el-select v-model="filters.maintenanceType" placeholder="请选择维护类型" size="small" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="定期保养" value="routine" />
              <el-option label="预防维护" value="preventive" />
              <el-option label="故障维修" value="corrective" />
              <el-option label="应急抢修" value="emergency" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>执行状态：</label>
            <el-select v-model="filters.status" placeholder="请选择执行状态" size="small" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="in-progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已逾期" value="overdue" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>时间范围：</label>
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>关键字搜索：</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="设备名称、维护内容" 
              size="small" 
              clearable
              @input="handleKeywordSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 维护统计概览 -->
    <el-card class="overview-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>维护统计概览</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="exportMaintenance">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="info" size="small" @click="generateReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="stats-overview">
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(64, 158, 255, 0.2); border: 2px solid #409eff;">
            <el-icon style="color: #409eff; font-size: 28px;"><Calendar /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">本月计划</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ maintenanceStats.totalPlans }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-trend trend-up">较上月 +5</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(103, 194, 58, 0.2); border: 2px solid #67c23a;">
            <el-icon style="color: #67c23a; font-size: 28px;"><CircleCheckFilled /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">已完成</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ maintenanceStats.completed }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-trend trend-up">较上月 +8</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(230, 162, 60, 0.2); border: 2px solid #e6a23c;">
            <el-icon style="color: #e6a23c; font-size: 28px;"><Clock /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">进行中</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ maintenanceStats.inProgress }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-trend trend-down">较上月 -2</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(245, 108, 108, 0.2); border: 2px solid #f56c6c;">
            <el-icon style="color: #f56c6c; font-size: 28px;"><WarningFilled /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">已逾期</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ maintenanceStats.overdue }}</span>
              <span class="stats-unit">项</span>
            </div>
            <div class="stats-trend trend-up">较上月 +1</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 数据可视化 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>维护类型分布</h4>
          </template>
          <div ref="maintenanceTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>维护完成率趋势</h4>
          </template>
          <div ref="completionRateChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 标签页切换 -->
    <el-card style="margin-top: 20px;">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="维护计划" name="plans">
          <template #label>
            <span><el-icon><Calendar /></el-icon> 维护计划</span>
          </template>
          
          <div class="tab-header">
            <div class="tab-title">维护计划列表</div>
            <div class="tab-controls">
              <el-button type="danger" size="small" @click="batchDeletePlans" :disabled="selectedPlans.length === 0">
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedPlans.length }})
              </el-button>
            </div>
          </div>
          
          <el-table 
            :data="planList" 
            border 
            @selection-change="handlePlanSelectionChange"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="planId" label="计划编号" width="120" align="center" />
            <el-table-column prop="deviceName" label="设备名称" min-width="150" align="center" />
            <el-table-column label="维护类型" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="getMaintenanceTypeTagType(row.maintenanceType)">
                  {{ getMaintenanceTypeName(row.maintenanceType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="maintenanceContent" label="维护内容" min-width="200" align="center" />
            <el-table-column prop="plannedDate" label="计划日期" width="120" align="center" />
            <el-table-column prop="assignedTo" label="负责人" width="100" align="center" />
            <el-table-column label="执行状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getPriorityTagType(row.priority)" size="small">
                  {{ getPriorityName(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <div style="white-space: nowrap;">
                  <el-button type="primary" size="small" @click="editPlan(row)" style="margin-right: 5px;">
                    编辑
                  </el-button>
                  <el-button type="success" size="small" @click="executePlan(row)" style="margin-right: 5px;">
                    执行
                  </el-button>
                  <el-button type="danger" size="small" @click="deletePlan(row)">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="维护记录" name="records">
          <template #label>
            <span><el-icon><Document /></el-icon> 维护记录</span>
          </template>
          
          <div class="tab-header">
            <div class="tab-title">维护记录列表</div>
            <div class="tab-controls">
              <el-button type="primary" size="small" @click="addMaintenanceRecord">
                <el-icon><Plus /></el-icon>
                新增记录
              </el-button>
            </div>
          </div>
          
          <el-table 
            :data="recordList" 
            border 
            style="width: 100%"
          >
            <el-table-column prop="recordId" label="记录编号" width="120" align="center" />
            <el-table-column prop="deviceName" label="设备名称" min-width="150" align="center" />
            <el-table-column prop="maintenanceContent" label="维护内容" min-width="200" align="center" />
            <el-table-column prop="executionDate" label="执行日期" width="120" align="center" />
            <el-table-column prop="executor" label="执行人" width="100" align="center" />
            <el-table-column prop="duration" label="耗时(小时)" width="100" align="center" />
            <el-table-column prop="cost" label="费用(元)" width="100" align="center" />
            <el-table-column label="执行结果" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.result === 'success' ? 'success' : 'danger'">
                  {{ row.result === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center" fixed="right">
              <template #default="{ row }">
                <div style="white-space: nowrap;">
                  <el-button type="primary" size="small" @click="viewRecord(row)" style="margin-right: 5px;">
                    查看
                  </el-button>
                  <el-button type="info" size="small" @click="editRecord(row)">
                    编辑
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane label="设备巡检" name="inspection">
          <template #label>
            <span><el-icon><View /></el-icon> 设备巡检</span>
          </template>
          
          <div class="tab-header">
            <div class="tab-title">巡检任务列表</div>
            <div class="tab-controls">
              <el-button type="primary" size="small" @click="addInspectionTask">
                <el-icon><Plus /></el-icon>
                新建巡检
              </el-button>
            </div>
          </div>
          
          <el-table 
            :data="inspectionList" 
            border 
            style="width: 100%"
          >
            <el-table-column prop="taskId" label="任务编号" width="120" align="center" />
            <el-table-column prop="taskName" label="巡检任务" min-width="150" align="center" />
            <el-table-column prop="inspectionArea" label="巡检区域" width="120" align="center" />
            <el-table-column prop="scheduledDate" label="计划日期" width="120" align="center" />
            <el-table-column prop="inspector" label="巡检员" width="100" align="center" />
            <el-table-column label="任务状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getInspectionStatusTagType(row.status)">
                  {{ getInspectionStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="deviceCount" label="设备数量" width="100" align="center" />
            <el-table-column prop="progress" label="完成进度" width="120" align="center">
              <template #default="{ row }">
                <el-progress :percentage="row.progress" :stroke-width="8" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <div style="white-space: nowrap;">
                  <el-button type="primary" size="small" @click="executeInspection(row)" style="margin-right: 5px;">
                    执行
                  </el-button>
                  <el-button type="info" size="small" @click="viewInspection(row)" style="margin-right: 5px;">
                    查看
                  </el-button>
                  <el-button type="success" size="small" @click="completeInspection(row)">
                    完成
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalItems"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 维护计划对话框 -->
    <el-dialog
      v-model="showPlanDialog"
      :title="isEditPlan ? '编辑维护计划' : '新建维护计划'"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="planForm" :rules="planRules" ref="planFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划编号" prop="planId">
              <el-input v-model="planForm.planId" placeholder="自动生成" :disabled="isEditPlan" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备选择" prop="deviceId">
              <el-select v-model="planForm.deviceId" placeholder="请选择设备" style="width: 100%">
                <el-option label="主配电柜电表" value="DEV001" />
                <el-option label="空调控制器" value="DEV002" />
                <el-option label="温湿度传感器" value="DEV003" />
                <el-option label="光伏逆变器" value="DEV004" />
                <el-option label="储能电池管理系统" value="DEV005" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维护类型" prop="maintenanceType">
              <el-select v-model="planForm.maintenanceType" placeholder="请选择维护类型" style="width: 100%">
                <el-option label="定期保养" value="routine" />
                <el-option label="预防维护" value="preventive" />
                <el-option label="故障维修" value="corrective" />
                <el-option label="应急抢修" value="emergency" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划日期" prop="plannedDate">
              <el-date-picker
                v-model="planForm.plannedDate"
                type="date"
                placeholder="请选择计划日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="assignedTo">
              <el-select v-model="planForm.assignedTo" placeholder="请选择负责人" style="width: 100%">
                <el-option label="张工程师" value="zhang" />
                <el-option label="李技术员" value="li" />
                <el-option label="王主管" value="wang" />
                <el-option label="刘管理员" value="liu" />
                <el-option label="陈工程师" value="chen" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="planForm.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="维护内容" prop="maintenanceContent">
          <el-input 
            v-model="planForm.maintenanceContent" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入维护内容"
          />
        </el-form-item>
        
        <el-form-item label="备注说明">
          <el-input 
            v-model="planForm.remarks" 
            type="textarea" 
            :rows="2" 
            placeholder="请输入备注说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showPlanDialog = false">取消</el-button>
        <el-button type="primary" @click="savePlan">保存</el-button>
      </template>
    </el-dialog>

    <!-- 维护记录对话框 -->
    <el-dialog
      v-model="showRecordDialog"
      :title="isEditRecord ? '编辑维护记录' : '新增维护记录'"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="recordForm" :rules="recordRules" ref="recordFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录编号" prop="recordId">
              <el-input v-model="recordForm.recordId" placeholder="自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联计划" prop="planId">
              <el-select v-model="recordForm.planId" placeholder="请选择维护计划" style="width: 100%">
                <el-option label="PLAN001 - 主配电柜检修" value="PLAN001" />
                <el-option label="PLAN002 - 空调系统保养" value="PLAN002" />
                <el-option label="PLAN003 - 传感器校准" value="PLAN003" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行日期" prop="executionDate">
              <el-date-picker
                v-model="recordForm.executionDate"
                type="datetime"
                placeholder="请选择执行日期"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行人" prop="executor">
              <el-input v-model="recordForm.executor" placeholder="请输入执行人" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="耗时(小时)" prop="duration">
              <el-input-number v-model="recordForm.duration" :min="0" :precision="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用(元)" prop="cost">
              <el-input-number v-model="recordForm.cost" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="执行结果" prop="result">
          <el-radio-group v-model="recordForm.result">
            <el-radio label="success">成功</el-radio>
            <el-radio label="failed">失败</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="维护详情" prop="details">
          <el-input 
            v-model="recordForm.details" 
            type="textarea" 
            :rows="4" 
            placeholder="请详细描述维护过程和结果"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showRecordDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保存</el-button>
      </template>
    </el-dialog>

    <!-- 巡检任务对话框 -->
    <el-dialog
      v-model="showInspectionDialog"
      title="新建巡检任务"
      width="50%"
      :close-on-click-modal="false"
    >
      <el-form :model="inspectionForm" :rules="inspectionRules" ref="inspectionFormRef" label-width="100px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="inspectionForm.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="巡检区域" prop="inspectionArea">
              <el-select v-model="inspectionForm.inspectionArea" placeholder="请选择巡检区域" style="width: 100%">
                <el-option label="配电房" value="power-room" />
                <el-option label="机房" value="server-room" />
                <el-option label="屋顶" value="rooftop" />
                <el-option label="地下室" value="basement" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="巡检员" prop="inspector">
              <el-select v-model="inspectionForm.inspector" placeholder="请选择巡检员" style="width: 100%">
                <el-option label="张工程师" value="zhang" />
                <el-option label="李技术员" value="li" />
                <el-option label="王主管" value="wang" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="计划日期" prop="scheduledDate">
          <el-date-picker
            v-model="inspectionForm.scheduledDate"
            type="date"
            placeholder="请选择计划日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="巡检内容">
          <el-input 
            v-model="inspectionForm.content" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入巡检内容和要求"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showInspectionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveInspection">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import {
  Plus,
  Refresh,
  Operation,
  Search,
  Download,
  Document,
  Delete,
  Calendar,
  CircleCheckFilled,
  Clock,
  WarningFilled,
  View
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '设备资产管理', to: '/device-management' },
  { text: '设备维护' }
])

// 响应式数据
const loading = ref(false)
const selectedBuilding = ref('')
const activeTab = ref('plans')
const maintenanceTypeChart = ref()
const completionRateChart = ref()

// 筛选条件
const filters = reactive({
  maintenanceType: '',
  status: '',
  dateRange: [],
  keyword: ''
})

// 维护统计数据
const maintenanceStats = reactive({
  totalPlans: 32,
  completed: 24,
  inProgress: 5,
  overdue: 3
})

// 维护计划列表
const planList = ref([
  {
    planId: 'PLAN001',
    deviceName: '主配电柜电表',
    maintenanceType: 'routine',
    maintenanceContent: '电表校准、接线检查、清洁保养',
    plannedDate: '2024-01-20',
    assignedTo: '张工程师',
    status: 'pending',
    priority: 'high'
  },
  {
    planId: 'PLAN002',
    deviceName: '空调控制器',
    maintenanceType: 'preventive',
    maintenanceContent: '控制器参数检查、通信测试、固件更新',
    plannedDate: '2024-01-18',
    assignedTo: '李技术员',
    status: 'in-progress',
    priority: 'medium'
  },
  {
    planId: 'PLAN003',
    deviceName: '温湿度传感器',
    maintenanceType: 'corrective',
    maintenanceContent: '传感器校准、数据准确性验证',
    plannedDate: '2024-01-15',
    assignedTo: '王主管',
    status: 'overdue',
    priority: 'urgent'
  },
  {
    planId: 'PLAN004',
    deviceName: '光伏逆变器',
    maintenanceType: 'routine',
    maintenanceContent: '逆变器清洁、散热检查、效率测试',
    plannedDate: '2024-01-22',
    assignedTo: '刘管理员',
    status: 'completed',
    priority: 'medium'
  },
  {
    planId: 'PLAN005',
    deviceName: '储能电池管理系统',
    maintenanceType: 'preventive',
    maintenanceContent: '电池状态检查、BMS参数优化',
    plannedDate: '2024-01-25',
    assignedTo: '陈工程师',
    status: 'pending',
    priority: 'high'
  }
])

// 维护记录列表
const recordList = ref([
  {
    recordId: 'REC001',
    deviceName: '主配电柜电表',
    maintenanceContent: '月度例行检查维护',
    executionDate: '2024-01-10',
    executor: '张工程师',
    duration: 2.5,
    cost: 300,
    result: 'success'
  },
  {
    recordId: 'REC002',
    deviceName: '空调控制器',
    maintenanceContent: '控制器故障维修',
    executionDate: '2024-01-08',
    executor: '李技术员',
    duration: 4.0,
    cost: 800,
    result: 'success'
  },
  {
    recordId: 'REC003',
    deviceName: '光伏逆变器',
    maintenanceContent: '逆变器清洁保养',
    executionDate: '2024-01-05',
    executor: '刘管理员',
    duration: 1.5,
    cost: 150,
    result: 'failed'
  }
])

// 巡检任务列表
const inspectionList = ref([
  {
    taskId: 'INSP001',
    taskName: '配电房设备巡检',
    inspectionArea: '配电房',
    scheduledDate: '2024-01-16',
    inspector: '张工程师',
    status: 'in-progress',
    deviceCount: 8,
    progress: 75
  },
  {
    taskId: 'INSP002',
    taskName: '屋顶光伏设备检查',
    inspectionArea: '屋顶',
    scheduledDate: '2024-01-18',
    inspector: '刘管理员',
    status: 'pending',
    deviceCount: 12,
    progress: 0
  },
  {
    taskId: 'INSP003',
    taskName: '机房环境监测',
    inspectionArea: '机房',
    scheduledDate: '2024-01-12',
    inspector: '王主管',
    status: 'completed',
    deviceCount: 6,
    progress: 100
  }
])

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalItems = ref(50)

// 选中数据
const selectedPlans = ref([])

// 对话框控制
const showPlanDialog = ref(false)
const showRecordDialog = ref(false)
const showInspectionDialog = ref(false)
const isEditPlan = ref(false)
const isEditRecord = ref(false)

// 表单数据
const planForm = reactive({
  planId: '',
  deviceId: '',
  maintenanceType: '',
  maintenanceContent: '',
  plannedDate: '',
  assignedTo: '',
  priority: 'medium',
  remarks: ''
})

const recordForm = reactive({
  recordId: '',
  planId: '',
  executionDate: '',
  executor: '',
  duration: 0,
  cost: 0,
  result: 'success',
  details: ''
})

const inspectionForm = reactive({
  taskName: '',
  inspectionArea: '',
  inspector: '',
  scheduledDate: '',
  content: ''
})

// 表单验证规则
const planRules = {
  deviceId: [{ required: true, message: '请选择设备', trigger: 'change' }],
  maintenanceType: [{ required: true, message: '请选择维护类型', trigger: 'change' }],
  maintenanceContent: [{ required: true, message: '请输入维护内容', trigger: 'blur' }],
  plannedDate: [{ required: true, message: '请选择计划日期', trigger: 'change' }],
  assignedTo: [{ required: true, message: '请选择负责人', trigger: 'change' }]
}

const recordRules = {
  planId: [{ required: true, message: '请选择关联计划', trigger: 'change' }],
  executionDate: [{ required: true, message: '请选择执行日期', trigger: 'change' }],
  executor: [{ required: true, message: '请输入执行人', trigger: 'blur' }],
  duration: [{ required: true, message: '请输入耗时', trigger: 'change' }],
  details: [{ required: true, message: '请输入维护详情', trigger: 'blur' }]
}

const inspectionRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  inspectionArea: [{ required: true, message: '请选择巡检区域', trigger: 'change' }],
  inspector: [{ required: true, message: '请选择巡检员', trigger: 'change' }],
  scheduledDate: [{ required: true, message: '请选择计划日期', trigger: 'change' }]
}

const planFormRef = ref()
const recordFormRef = ref()
const inspectionFormRef = ref()

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 维护类型分布图
    if (maintenanceTypeChart.value) {
      const typeChart = echarts.init(maintenanceTypeChart.value)
      const typeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '0%'
        },
        series: [
          {
            name: '维护类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 45, name: '定期保养' },
              { value: 30, name: '预防维护' },
              { value: 15, name: '故障维修' },
              { value: 10, name: '应急抢修' }
            ]
          }
        ]
      }
      typeChart.setOption(typeOption)
    }

    // 维护完成率趋势图
    if (completionRateChart.value) {
      const rateChart = echarts.init(completionRateChart.value)
      const rateOption = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a}: {c}%'
        },
        xAxis: {
          type: 'category',
          data: ['01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15']
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            name: '完成率',
            type: 'line',
            smooth: true,
            areaStyle: { opacity: 0.3 },
            data: [85, 88, 92, 87, 90, 93, 89]
          }
        ]
      }
      rateChart.setOption(rateOption)
    }
  })
}

// 获取维护类型标签类型
const getMaintenanceTypeTagType = (type: string) => {
  const typeMap = {
    routine: 'primary',
    preventive: 'success',
    corrective: 'warning',
    emergency: 'danger'
  }
  return typeMap[type] || ''
}

// 获取维护类型名称
const getMaintenanceTypeName = (type: string) => {
  const nameMap = {
    routine: '定期保养',
    preventive: '预防维护',
    corrective: '故障维修',
    emergency: '应急抢修'
  }
  return nameMap[type] || type
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    pending: 'info',
    'in-progress': 'warning',
    completed: 'success',
    overdue: 'danger'
  }
  return statusMap[status] || ''
}

// 获取状态名称
const getStatusName = (status: string) => {
  const nameMap = {
    pending: '待执行',
    'in-progress': '执行中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return nameMap[status] || status
}

// 获取优先级标签类型
const getPriorityTagType = (priority: string) => {
  const priorityMap = {
    low: 'info',
    medium: '',
    high: 'warning',
    urgent: 'danger'
  }
  return priorityMap[priority] || ''
}

// 获取优先级名称
const getPriorityName = (priority: string) => {
  const nameMap = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return nameMap[priority] || priority
}

// 获取巡检状态标签类型
const getInspectionStatusTagType = (status: string) => {
  return getStatusTagType(status)
}

// 获取巡检状态名称
const getInspectionStatusName = (status: string) => {
  return getStatusName(status)
}

// 事件处理函数
const handleBuildingChange = () => {
  console.log('建筑筛选变更:', selectedBuilding.value)
}

const handleFilterChange = () => {
  console.log('筛选条件变更:', filters)
}

const handleDateRangeChange = () => {
  console.log('时间范围变更:', filters.dateRange)
}

const handleKeywordSearch = () => {
  console.log('关键字搜索:', filters.keyword)
}

const handleTabClick = (tab) => {
  console.log('切换标签页:', tab.props.name)
}

const handlePlanSelectionChange = (selection) => {
  selectedPlans.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const addMaintenancePlan = () => {
  isEditPlan.value = false
  Object.assign(planForm, {
    planId: 'PLAN' + String(Date.now()).slice(-3),
    deviceId: '',
    maintenanceType: '',
    maintenanceContent: '',
    plannedDate: '',
    assignedTo: '',
    priority: 'medium',
    remarks: ''
  })
  showPlanDialog.value = true
}

const editPlan = (plan) => {
  isEditPlan.value = true
  Object.assign(planForm, plan)
  showPlanDialog.value = true
}

const executePlan = (plan) => {
  ElMessageBox.confirm(
    `确定要执行维护计划 "${plan.planId}" 吗？`,
    '确认执行',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    ElMessage.success('维护计划执行成功')
  }).catch(() => {
    // 取消执行
  })
}

const deletePlan = (plan) => {
  ElMessageBox.confirm(
    `确定要删除维护计划 "${plan.planId}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('维护计划删除成功')
  }).catch(() => {
    // 取消删除
  })
}

const savePlan = () => {
  planFormRef.value?.validate((valid) => {
    if (valid) {
      ElMessage.success(isEditPlan.value ? '维护计划更新成功' : '维护计划创建成功')
      showPlanDialog.value = false
    }
  })
}

const batchDeletePlans = () => {
  if (selectedPlans.value.length === 0) {
    ElMessage.warning('请选择要删除的维护计划')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedPlans.value.length} 个维护计划吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success(`成功删除 ${selectedPlans.value.length} 个维护计划`)
    selectedPlans.value = []
  }).catch(() => {
    // 取消删除
  })
}

const addMaintenanceRecord = () => {
  isEditRecord.value = false
  Object.assign(recordForm, {
    recordId: 'REC' + String(Date.now()).slice(-3),
    planId: '',
    executionDate: '',
    executor: '',
    duration: 0,
    cost: 0,
    result: 'success',
    details: ''
  })
  showRecordDialog.value = true
}

const editRecord = (record) => {
  isEditRecord.value = true
  Object.assign(recordForm, record)
  showRecordDialog.value = true
}

const viewRecord = (record) => {
  ElMessage.info('查看维护记录详情功能开发中')
}

const saveRecord = () => {
  recordFormRef.value?.validate((valid) => {
    if (valid) {
      ElMessage.success(isEditRecord.value ? '维护记录更新成功' : '维护记录添加成功')
      showRecordDialog.value = false
    }
  })
}

const addInspectionTask = () => {
  Object.assign(inspectionForm, {
    taskName: '',
    inspectionArea: '',
    inspector: '',
    scheduledDate: '',
    content: ''
  })
  showInspectionDialog.value = true
}

const executeInspection = (task) => {
  ElMessage.info(`开始执行巡检任务: ${task.taskName}`)
}

const viewInspection = (task) => {
  ElMessage.info('查看巡检任务详情功能开发中')
}

const completeInspection = (task) => {
  ElMessage.success(`巡检任务 "${task.taskName}" 已完成`)
}

const saveInspection = () => {
  inspectionFormRef.value?.validate((valid) => {
    if (valid) {
      ElMessage.success('巡检任务创建成功')
      showInspectionDialog.value = false
    }
  })
}

const batchOperation = () => {
  ElMessage.info('批量操作功能开发中')
}

const exportMaintenance = () => {
  ElMessage.success('维护数据导出成功')
}

const generateReport = () => {
  ElMessage.success('维护报告生成成功')
}

// 组件挂载
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.device-maintenance {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.filter-item label {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  min-width: 80px;
}

.stats-overview {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 30px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  align-items: center;
  min-width: 180px;
  padding: 0 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stats-info {
  flex: 1;
}

.stats-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stats-value-row {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stats-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stats-trend {
  font-size: 11px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.stats-divider {
  width: 1px;
  height: 60%;
  background-color: #ebeef5;
  margin: 0 15px;
}

.overview-card :deep(.el-card__body) {
  padding: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tab-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

@media (max-width: 768px) {
  .stats-overview {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-divider {
    display: none;
  }
  
  .stats-item {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
  }
}
</style> 