<template>
  <div class="data-dashboard">
    <!-- 顶部标题栏 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">
          <el-icon><Monitor /></el-icon>
          能耗管理数据大屏
        </h1>
      </div>
      <div class="header-center">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>
      <div class="header-right">
        <div class="fullscreen-control">
          <el-button 
            type="primary" 
            size="large" 
            @click="toggleFullscreen"
            class="fullscreen-btn"
          >
            <el-icon><FullScreen /></el-icon>
            {{ isFullscreen ? '退出全屏' : '全屏显示' }}
          </el-button>
        </div>
        <div class="system-status">
          <span class="status-label">系统状态</span>
          <el-tag type="success" size="large">
            <el-icon><CircleCheckFilled /></el-icon>
            正常运行
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 左侧列 -->
      <div class="dashboard-left">
        <!-- 设备总览 -->
        <div class="panel device-overview">
          <div class="panel-header">
            <h3><el-icon><Setting /></el-icon>设备总览</h3>
          </div>
          <div class="panel-content">
            <div class="overview-grid">
              <div class="overview-item" v-for="item in deviceOverview" :key="item.label">
                <div class="item-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
                  <el-icon :style="{ color: item.color }"><component :is="item.icon" /></el-icon>
                </div>
                <div class="item-info">
                  <div class="item-value">{{ item.value }}</div>
                  <div class="item-label">{{ item.label }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时告警 -->
        <div class="panel alert-panel">
          <div class="panel-header">
            <h3><el-icon><WarningFilled /></el-icon>实时告警</h3>
          </div>
          <div class="panel-content">
            <div class="alert-list">
              <div class="alert-item" v-for="alert in realtimeAlerts" :key="alert.id">
                <div class="alert-level" :class="alert.level"></div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-time">{{ alert.time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统运行状态 -->
        <div class="panel system-status-panel">
          <div class="panel-header">
            <h3><el-icon><Monitor /></el-icon>系统状态</h3>
          </div>
          <div class="panel-content">
            <div class="status-list">
              <div class="status-item" v-for="status in systemStatus" :key="status.name">
                <div class="status-indicator" :class="status.status"></div>
                <div class="status-info">
                  <div class="status-name">{{ status.name }}</div>
                  <div class="status-desc">{{ status.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间列 -->
      <div class="dashboard-center">
        <!-- 实时能耗数据 -->
        <div class="panel realtime-data">
          <div class="panel-header">
            <h3><el-icon><TrendCharts /></el-icon>实时能耗数据</h3>
          </div>
          <div class="panel-content">
            <div class="realtime-grid">
              <div class="realtime-item" v-for="item in realtimeData" :key="item.type">
                <div class="realtime-icon" :style="{ color: item.color }">
                  <el-icon><component :is="item.icon" /></el-icon>
                </div>
                <div class="realtime-info">
                  <div class="realtime-value">{{ item.value }}</div>
                  <div class="realtime-unit">{{ item.unit }}</div>
                  <div class="realtime-type">{{ item.type }}</div>
                </div>
                <div class="realtime-trend" :class="item.trend">
                  <el-icon><component :is="item.trendIcon" /></el-icon>
                  {{ item.trendValue }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 3D能源场站 -->
        <div class="panel station-3d-panel">
          <div class="panel-header">
            <h3><el-icon><Monitor /></el-icon>3D能源场站</h3>
            <div class="chart-controls">
              <el-button-group size="small">
                <el-button :type="autoRotate ? 'primary' : ''" @click="toggleAutoRotate">
                  <el-icon><Setting /></el-icon>
                  {{ autoRotate ? '停止旋转' : '自动旋转' }}
                </el-button>
                <el-button @click="resetCamera">
                  <el-icon><Refresh /></el-icon>
                  重置视角
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="panel-content">
            <EnergyStation3D 
              :devices="stationDevices" 
              :auto-rotate="autoRotate"
              ref="station3D"
            />
          </div>
        </div>
      </div>

      <!-- 右侧列 -->
      <div class="dashboard-right">
        <!-- 能源分布 + 单元排名 -->
        <div class="panel combined-panel">
          <div class="panel-header">
            <h3><el-icon><Grid /></el-icon>能源分布 & 排名</h3>
          </div>
          <div class="panel-content">
            <div class="combined-content">
              <!-- 能源分布图表 -->
              <div class="distribution-section">
                <div ref="energyDistributionChart" class="chart-container-pie"></div>
                <div class="distribution-legend">
                  <div class="legend-item" v-for="item in energyDistribution" :key="item.name">
                    <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                    <span class="legend-name">{{ item.name }}</span>
                    <span class="legend-value">{{ item.value }}%</span>
                  </div>
                </div>
              </div>
              
              <!-- 单元排名 -->
              <div class="ranking-section">
                <h4 class="section-title">单元能耗排名</h4>
                <div class="ranking-list">
                  <div class="ranking-item" v-for="(item, index) in unitRanking.slice(0, 4)" :key="item.name">
                    <div class="ranking-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
                    <div class="ranking-info">
                      <div class="ranking-name">{{ item.name }}</div>
                      <div class="ranking-value">{{ item.consumption }} kWh</div>
                    </div>
                    <div class="ranking-bar">
                      <div class="bar-fill" :style="{ width: item.percentage + '%', backgroundColor: item.color }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 绿电发电统计 -->
        <div class="panel green-energy-panel">
          <div class="panel-header">
            <h3><el-icon><Sunny /></el-icon>绿电发电统计</h3>
          </div>
          <div class="panel-content">
            <div class="green-energy-stats">
              <div class="green-stat-item">
                <div class="stat-icon">
                  <el-icon><Sunny /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ greenEnergyStats.todayGeneration }}</div>
                  <div class="stat-label">今日发电量 (kWh)</div>
                </div>
              </div>
              <div class="green-stat-item">
                <div class="stat-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ greenEnergyStats.monthGeneration }}</div>
                  <div class="stat-label">本月发电量 (kWh)</div>
                </div>
              </div>
              <div class="green-stat-item">
                <div class="stat-icon">
                  <el-icon><Promotion /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ greenEnergyStats.carbonReduction }}</div>
                  <div class="stat-label">减碳量 (kg)</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 节能建议 -->
        <div class="panel suggestions-panel">
          <div class="panel-header">
            <h3><el-icon><InfoIcon /></el-icon>节能建议</h3>
          </div>
          <div class="panel-content">
            <div class="suggestions-list">
              <div class="suggestion-item" v-for="suggestion in energySuggestions.slice(0, 3)" :key="suggestion.id">
                <div class="suggestion-priority" :class="suggestion.priority"></div>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-desc">{{ suggestion.description }}</div>
                  <div class="suggestion-saving">预计节能: {{ suggestion.saving }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import EnergyStation3D from '@/components/EnergyStation3D.vue'
import {
  Monitor,
  Setting,
  WarningFilled,
  TrendCharts,
  InfoFilled,
  Grid,
  List,
  Sunny,
  InfoFilled as InfoIcon,
  CircleCheckFilled,
  Promotion,
  ArrowUp,
  ArrowDown,
  Lightning,
  Cpu,
  Connection,
  Refresh,
  FullScreen
} from '@element-plus/icons-vue'

// 时间相关
const currentTime = ref('')
const currentDate = ref('')
let timeInterval: number

// 全屏相关
const isFullscreen = ref(false)

// 3D场站相关
const station3D = ref()
const autoRotate = ref(false)

// 3D场站设备数据
// 3D场站设备数据
const stationDevices = ref([
  { 
    id: '1', 
    name: '主变压器', 
    type: 'transformer' as const, 
    position: { x: 0, y: 0, z: 0 }, 
    status: 'normal' as const, 
    power: 85, 
    efficiency: 95 
  },
  { 
    id: '2', 
    name: '发电机组1', 
    type: 'generator' as const, 
    position: { x: -15, y: 0, z: -10 }, 
    status: 'normal' as const, 
    power: 78, 
    efficiency: 88 
  },
  { 
    id: '3', 
    name: '发电机组2', 
    type: 'generator' as const, 
    position: { x: -15, y: 0, z: 10 }, 
    status: 'warning' as const, 
    power: 65, 
    efficiency: 82 
  },
  { 
    id: '4', 
    name: '太阳能板阵列', 
    type: 'solar' as const, 
    position: { x: 15, y: 0, z: -15 }, 
    status: 'normal' as const, 
    power: 45, 
    efficiency: 92 
  },
  { 
    id: '5', 
    name: '风力发电机', 
    type: 'wind' as const, 
    position: { x: 15, y: 0, z: 15 }, 
    status: 'normal' as const, 
    power: 38, 
    efficiency: 89 
  },
  { 
    id: '6', 
    name: '储能电池', 
    type: 'battery' as const, 
    position: { x: 0, y: 0, z: -20 }, 
    status: 'normal' as const, 
    power: 55, 
    efficiency: 94 
  },
  { 
    id: '7', 
    name: '办公楼A', 
    type: 'building' as const, 
    position: { x: -25, y: 0, z: 0 }, 
    status: 'normal' as const, 
    power: 25, 
    efficiency: 85 
  },
  { 
    id: '8', 
    name: '办公楼B', 
    type: 'building' as const, 
    position: { x: 25, y: 0, z: 0 }, 
    status: 'normal' as const, 
    power: 30, 
    efficiency: 87 
  },
  // 充电桩区域
  { 
    id: '9', 
    name: '充电桩1', 
    type: 'charging_station' as const, 
    position: { x: -8, y: 0, z: 25 }, 
    status: 'charging' as const, 
    power: 80, 
    efficiency: 95 
  },
  { 
    id: '10', 
    name: '充电桩2', 
    type: 'charging_station' as const, 
    position: { x: -4, y: 0, z: 25 }, 
    status: 'idle' as const, 
    power: 0, 
    efficiency: 95 
  },
  { 
    id: '11', 
    name: '充电桩3', 
    type: 'charging_station' as const, 
    position: { x: 0, y: 0, z: 25 }, 
    status: 'charging' as const, 
    power: 75, 
    efficiency: 93 
  },
  { 
    id: '12', 
    name: '充电桩4', 
    type: 'charging_station' as const, 
    position: { x: 4, y: 0, z: 25 }, 
    status: 'idle' as const, 
    power: 0, 
    efficiency: 95 
  },
  { 
    id: '13', 
    name: '充电桩5', 
    type: 'charging_station' as const, 
    position: { x: 8, y: 0, z: 25 }, 
    status: 'error' as const, 
    power: 0, 
    efficiency: 0 
  },
  { 
    id: '14', 
    name: '充电桩6', 
    type: 'charging_station' as const, 
    position: { x: 12, y: 0, z: 25 }, 
    status: 'charging' as const, 
    power: 90, 
    efficiency: 96 
  }
])

// 3D控制方法
const toggleAutoRotate = () => {
  autoRotate.value = !autoRotate.value
  if (station3D.value) {
    station3D.value.toggleAutoRotate()
  }
}

const resetCamera = () => {
  if (station3D.value) {
    station3D.value.resetCamera()
  }
}

// 全屏控制方法
const toggleFullscreen = async () => {
  try {
    if (!document.fullscreenElement) {
      // 进入全屏
      await document.documentElement.requestFullscreen()
      isFullscreen.value = true
    } else {
      // 退出全屏
      await document.exitFullscreen()
      isFullscreen.value = false
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 添加全屏状态监听器
document.addEventListener('fullscreenchange', handleFullscreenChange)

// 设备总览数据
const deviceOverview = ref([
  { label: '在线设备', value: '156', icon: 'Monitor', color: '#409eff' },
  { label: '离线设备', value: '8', icon: 'Connection', color: '#f56c6c' },
  { label: '告警设备', value: '3', icon: 'WarningFilled', color: '#e6a23c' },
  { label: '维护设备', value: '2', icon: 'Setting', color: '#909399' }
])

// 实时告警数据
const realtimeAlerts = ref([
  { id: 1, title: '空调系统能耗异常', time: '2分钟前', level: 'high' },
  { id: 2, title: '照明回路电流过载', time: '5分钟前', level: 'medium' },
  { id: 3, title: '设备通信中断', time: '8分钟前', level: 'low' },
  { id: 4, title: '温度传感器故障', time: '12分钟前', level: 'medium' }
])

// 系统状态数据
const systemStatus = ref([
  { name: '数据库', status: 'normal', description: '响应时间: 12ms' },
  { name: '设备通信', status: 'normal', description: '连接率: 98.5%' },
  { name: '数据采集', status: 'warning', description: '延迟: 2.3s' },
  { name: '告警服务', status: 'normal', description: '运行正常' }
])

// 实时能耗数据
const realtimeData = ref([
  { 
    type: '总用电量', 
    value: '1,245.6', 
    unit: 'kWh', 
    icon: 'Lightning', 
    color: '#409eff',
    trend: 'up',
    trendIcon: 'ArrowUp',
    trendValue: '5.2%'
  },
  { 
    type: '用水量', 
    value: '89.3', 
    unit: 'm³', 
    icon: 'Cpu', 
    color: '#67c23a',
    trend: 'down',
    trendIcon: 'ArrowDown',
    trendValue: '2.1%'
  },
  { 
    type: '天然气', 
    value: '156.8', 
    unit: 'm³', 
    icon: 'Connection', 
    color: '#e6a23c',
    trend: 'up',
    trendIcon: 'ArrowUp',
    trendValue: '1.8%'
  },
  { 
    type: '蒸汽', 
    value: '45.2', 
    unit: 't', 
    icon: 'Monitor', 
    color: '#f56c6c',
    trend: 'down',
    trendIcon: 'ArrowDown',
    trendValue: '0.5%'
  }
])

// 能源分布数据
const energyDistribution = ref([
  { name: '电力', value: 65, color: '#409eff' },
  { name: '天然气', value: 20, color: '#67c23a' },
  { name: '水', value: 10, color: '#e6a23c' },
  { name: '蒸汽', value: 5, color: '#f56c6c' }
])

// 单元能耗排名
const unitRanking = ref([
  { name: 'A栋办公楼', consumption: 2856, percentage: 100, color: '#f56c6c' },
  { name: 'B栋生产车间', consumption: 2234, percentage: 78, color: '#e6a23c' },
  { name: 'C栋研发中心', consumption: 1876, percentage: 66, color: '#409eff' },
  { name: 'D栋仓储区', consumption: 1456, percentage: 51, color: '#67c23a' },
  { name: 'E栋配套设施', consumption: 987, percentage: 35, color: '#909399' }
])

// 绿电发电统计
const greenEnergyStats = ref({
  todayGeneration: '1,856.7',
  monthGeneration: '45,234.8',
  carbonReduction: '18,567'
})

// 节能建议
const energySuggestions = ref([
  {
    id: 1,
    title: '空调系统优化',
    description: '建议调整空调温度设定，可节能15%',
    saving: '180 kWh/天',
    priority: 'high'
  },
  {
    id: 2,
    title: '照明系统改造',
    description: '更换LED灯具，提升照明效率',
    saving: '95 kWh/天',
    priority: 'medium'
  },
  {
    id: 3,
    title: '设备运行优化',
    description: '调整设备运行时间，避开用电高峰',
    saving: '65 kWh/天',
    priority: 'low'
  }
])

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  currentDate.value = now.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  })
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  // 清理全屏监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
.data-dashboard {
  width: 100%;
  height: 100vh;
  max-width: 100vw;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.dashboard-header {
  height: 70px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid rgba(64, 158, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.dashboard-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 10px;
  text-shadow: 0 0 20px rgba(64, 158, 255, 0.5);
}

.header-center {
  text-align: center;
}

.current-time {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.current-date {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.fullscreen-control {
  display: flex;
  align-items: center;
}

.fullscreen-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.fullscreen-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
}

.fullscreen-btn:active {
  transform: translateY(0);
}

.system-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-size: 14px;
  color: #909399;
}

/* 主要内容区域 */
.dashboard-content {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 15px;
  padding: 15px;
  overflow: hidden;
  min-height: 0;
  box-sizing: border-box;
}

/* 面板通用样式 */
.panel {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(64, 158, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 12px 16px;
  background: rgba(64, 158, 255, 0.1);
  border-bottom: 1px solid rgba(64, 158, 255, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 6px;
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 左侧列样式 */
.dashboard-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.device-overview {
  flex: 0 0 auto;
}

.alert-panel {
  flex: 1;
  min-height: 0;
}

.system-status-panel {
  flex: 0 0 auto;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.item-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  font-size: 14px;
}

.item-info {
  flex: 1;
}

.item-value {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.item-label {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

/* 告警列表 */
.alert-list {
  flex: 1;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-level {
  width: 6px;
  height: 32px;
  border-radius: 3px;
}

.alert-level.high { background: #f56c6c; }
.alert-level.medium { background: #e6a23c; }
.alert-level.low { background: #67c23a; }

.alert-title {
  font-size: 13px;
  color: #fff;
  margin-bottom: 3px;
}

.alert-time {
  font-size: 11px;
  color: #909399;
}

/* 系统状态 */
.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item:last-child {
  border-bottom: none;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-indicator.normal {
  background: #67c23a;
  box-shadow: 0 0 8px rgba(103, 194, 58, 0.5);
}

.status-indicator.warning {
  background: #e6a23c;
  box-shadow: 0 0 8px rgba(230, 162, 60, 0.5);
}

.status-name {
  font-size: 13px;
  color: #fff;
  margin-bottom: 2px;
}

.status-desc {
  font-size: 11px;
  color: #909399;
}

/* 中间列样式 */
.dashboard-center {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.realtime-data {
  flex: 0 0 auto;
}

.chart-panel {
  flex: 1;
  min-height: 0;
}

.station-3d-panel {
  flex: 1;
  min-height: 0;
}

.realtime-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.realtime-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  position: relative;
}

.realtime-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.realtime-value {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}

.realtime-unit {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.realtime-type {
  font-size: 11px;
  color: #909399;
  margin-top: 6px;
}

.realtime-trend {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.realtime-trend.up { color: #67c23a; }
.realtime-trend.down { color: #f56c6c; }

/* 图表容器 */
.chart-container {
  height: 100%;
  width: 100%;
  min-height: 200px;
}

.chart-container-pie {
  height: 180px;
  width: 100%;
}

.distribution-legend {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-name {
  color: #fff;
  flex: 1;
}

.legend-value {
  color: #409eff;
  font-weight: bold;
}

/* 右侧列样式 */
.dashboard-right {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.combined-panel {
  flex: 1;
  min-height: 0;
}

.green-energy-panel {
  flex: 0 0 auto;
}

.suggestions-panel {
  flex: 0 0 auto;
}

/* 组合面板内容 */
.combined-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.distribution-section {
  flex: 0 0 auto;
}

.ranking-section {
  flex: 1;
  min-height: 0;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #409eff;
}

/* 排名列表 */
.ranking-list {
  flex: 1;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.ranking-number.rank-1 { background: #f56c6c; color: #fff; }
.ranking-number.rank-2 { background: #e6a23c; color: #fff; }
.ranking-number.rank-3 { background: #409eff; color: #fff; }
.ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: rgba(255, 255, 255, 0.1);
  color: #909399;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 12px;
  color: #fff;
  margin-bottom: 2px;
}

.ranking-value {
  font-size: 10px;
  color: #909399;
}

.ranking-bar {
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 绿电统计 */
.green-energy-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.green-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.stat-icon {
  width: 32px;
  height: 32px;
  background: rgba(103, 194, 58, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #67c23a;
  font-size: 14px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #67c23a;
}

.stat-label {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

/* 节能建议 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-item {
  display: flex;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-priority {
  width: 4px;
  border-radius: 2px;
}

.suggestion-priority.high { background: #f56c6c; }
.suggestion-priority.medium { background: #e6a23c; }
.suggestion-priority.low { background: #67c23a; }

.suggestion-title {
  font-size: 12px;
  color: #fff;
  margin-bottom: 4px;
}

.suggestion-desc {
  font-size: 10px;
  color: #909399;
  margin-bottom: 4px;
}

.suggestion-saving {
  font-size: 10px;
  color: #67c23a;
  font-weight: bold;
}

/* 图表控制按钮 */
.chart-controls {
  display: flex;
  gap: 5px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.5);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 12px;
    padding: 12px;
  }
}

@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 260px 1fr 260px;
    gap: 10px;
    padding: 10px;
  }
  
  .realtime-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .dashboard-header {
    height: 60px;
    padding: 0 15px;
  }
  
  .dashboard-title {
    font-size: 20px;
  }
  
  .current-time {
    font-size: 24px;
  }
}

@media (max-width: 1400px) {
  .dashboard-content {
    grid-template-columns: 240px 1fr 240px;
    gap: 8px;
    padding: 8px;
  }
  
  .panel-content {
    padding: 12px;
  }
  
  .realtime-item {
    padding: 8px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 10px;
  }
  
  .dashboard-left,
  .dashboard-center,
  .dashboard-right {
    height: auto;
  }
}
</style>