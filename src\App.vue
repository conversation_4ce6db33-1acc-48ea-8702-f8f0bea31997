<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppSidebar from '@/components/AppSidebar.vue'
// 导入stagewise工具栏
import { StagewiseToolbar } from '@stagewise/toolbar-vue'
import VuePlugin from '@stagewise-plugins/vue'

const route = useRoute()
const showLayout = computed(() => route.meta.requiresLayout !== false)
// 确定是否为开发环境
const isDev = import.meta.env.DEV
// stagewise配置
const stagwiseConfig = {
  plugins: [VuePlugin]
}
</script>

<template>
  <div class="app-container">
    <AppHeader v-if="showLayout" />
    <AppSidebar v-if="showLayout" />
    <main :class="{'main-content': showLayout}">
      <router-view />
    </main>
    <!-- 仅在开发环境中显示stagewise工具栏 -->
    <StagewiseToolbar v-if="isDev" :config="stagwiseConfig" />
  </div>
</template>

<style>
@import '@/styles/main.css';
</style>
