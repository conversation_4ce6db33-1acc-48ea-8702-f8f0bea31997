/* 全局CSS变量 */
:root {
  --header-height: 60px;
  --sidebar-width: 200px;
  --content-padding: 20px;
  --gap-sm: 8px;
  --gap-md: 16px;
  --gap-lg: 24px;
  --primary-color: #409EFF;
  --text-color: #303133;
  --text-color-light: #909399;
  --border-color: #DCDFE6;
  --bg-color: #F5F7FA;
}

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
  background-color: var(--bg-color);
}

#app {
  height: 100%;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-content {
  margin-left: var(--sidebar-width);
  margin-top: var(--header-height);
  padding: 10px var(--content-padding) var(--content-padding);
  height: calc(100vh - var(--header-height));
  overflow-y: auto;
  overflow-x: hidden;
  max-width: calc(100vw - var(--sidebar-width));
  box-sizing: border-box;
}

/* 通用样式 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
} 