<template>
  <div class="charging-management">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 充电桩统计概览 -->
    <el-card class="overview-card" v-loading="loading" element-loading-text="数据加载中...">
      <template #header>
        <div class="card-header">
          <h3>充电桩统计概览</h3>
          <div class="header-controls">
            <el-date-picker
              v-model="selectedDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="handleDateRangeChange"
              :disabled="loading"
            />
            <el-button type="success" size="small" @click="refreshAllData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="primary" size="small" @click="exportData" :loading="loading">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="charging-stats">
        <div class="stat-item" v-for="(item, index) in chargingStats" :key="index">
          <div class="stat-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-name">{{ item.name }}</div>
            <div class="stat-value-container">
              <span class="stat-value">{{ item.value }}</span>
              <span class="stat-unit">{{ item.unit }}</span>
            </div>
            <div class="stat-trend" :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
              {{ item.comparePeriod }}{{ item.trend === 'up' ? '增长' : '下降' }} {{ item.trend === 'up' ? '+' : '-' }}{{ item.changeRate }}%
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 充电桩状态和使用情况 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card v-loading="loading" element-loading-text="图表更新中...">
          <template #header>
            <div class="card-header">
              <h3>充电功率趋势</h3>
              <div class="header-controls">
                <el-radio-group v-model="timeRange" size="small" @change="handleTimeRangeChange" :disabled="loading">
                  <el-radio-button label="today">今天</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="chargingTrendChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card v-loading="loading" element-loading-text="状态统计中...">
          <template #header>
            <div class="card-header">
              <h3>充电桩状态</h3>
            </div>
          </template>
          <div ref="chargingStatusChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 充电模式分析和收费统计 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card v-loading="loading" element-loading-text="模式分析中...">
          <template #header>
            <div class="card-header">
              <h3>充电模式分析</h3>
            </div>
          </template>
          <div ref="chargingModeChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card v-loading="loading" element-loading-text="收费统计中...">
          <template #header>
            <div class="card-header">
              <h3>收费统计</h3>
            </div>
          </template>
          <div ref="revenueChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 充电桩设备管理 -->
    <el-card style="margin-top: 20px;" v-loading="loading" element-loading-text="设备数据加载中...">
      <template #header>
        <div class="card-header">
          <h3>充电桩设备管理</h3>
          <div class="header-controls">
            <div class="view-controls">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="card">卡片视图</el-radio-button>
                <el-radio-button label="table">表格视图</el-radio-button>
              </el-radio-group>
            </div>
            <div class="filter-controls">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索设备名称/ID/型号/位置"
                size="small"
                style="width: 220px;"
                @input="handleSearch"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-select
                v-model="statusFilter"
                placeholder="筛选状态"
                size="small"
                style="width: 120px;"
                @change="handleStatusFilter"
              >
                <el-option label="全部" value="" />
                <el-option label="空闲" value="空闲" />
                <el-option label="充电中" value="充电中" />
                <el-option label="故障" value="故障" />
                <el-option label="维护" value="维护" />
              </el-select>
              <el-button type="info" size="small" @click="showStrategyDialog = true">
                <el-icon><Setting /></el-icon>
                充电策略
              </el-button>
              <el-button type="primary" size="small" @click="addCharger">
                <el-icon><Plus /></el-icon>
                添加设备
              </el-button>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="device-cards">
        <div class="device-card" v-for="charger in filteredChargers" :key="charger.chargerId">
          <div class="device-card-header">
            <div class="device-info">
              <h4>{{ charger.chargerName }}</h4>
              <el-tag :type="getChargerTypeColor(charger.chargerType)" size="small">
                {{ charger.chargerType }}
              </el-tag>
            </div>
            <div class="device-status">
              <el-tag :type="getStatusColor(charger.status)" size="small">
                {{ charger.status }}
              </el-tag>
            </div>
          </div>
          <div class="device-card-body">
            <div class="device-metrics">
              <div class="metric-item">
                <span class="metric-label">充电量</span>
                <span class="metric-value">{{ charger.chargingEnergy }}kWh</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">换电量</span>
                <span class="metric-value">{{ charger.batterySwapCount }}次</span>
              </div>
              <div class="metric-item">
                <span class="metric-label">放电量</span>
                <span class="metric-value">{{ charger.dischargingEnergy }}kWh</span>
              </div>
            </div>
            <div class="device-connection">
              <span class="connection-label">物联状态:</span>
              <el-tag :type="charger.iotConnected ? 'success' : 'danger'" size="small">
                {{ charger.iotConnected ? '已连接' : '未连接' }}
              </el-tag>
            </div>
            <div class="device-model">
              <span class="model-label">设备型号:</span>
              <span class="model-value">{{ charger.deviceModel }}</span>
            </div>
          </div>
          <div class="device-card-footer">
            <div class="device-location">
              <el-icon><LocationInformation /></el-icon>
              {{ charger.location }}
            </div>
            <div class="device-actions">
              <el-button type="primary" size="small" @click="viewCharger(charger)">
                详情
              </el-button>
              <el-button type="warning" size="small" @click="editCharger(charger)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="deleteCharger(charger)">
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table 
          :data="filteredChargers" 
          style="width: 100%" 
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
          border
        >
          <el-table-column prop="chargerId" label="设备ID" width="120" align="center" />
          <el-table-column prop="chargerName" label="设备名称" width="150" align="center" />
          <el-table-column prop="chargerType" label="设备类型" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getChargerTypeColor(row.chargerType)" size="small">
                {{ row.chargerType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="chargingEnergy" label="充电量" width="100" align="center">
            <template #default="{ row }">
              {{ row.chargingEnergy }}kWh
            </template>
          </el-table-column>
          <el-table-column prop="batterySwapCount" label="换电量" width="100" align="center">
            <template #default="{ row }">
              {{ row.batterySwapCount }}次
            </template>
          </el-table-column>
          <el-table-column prop="dischargingEnergy" label="放电量" width="100" align="center">
            <template #default="{ row }">
              {{ row.dischargingEnergy }}kWh
            </template>
          </el-table-column>
          <el-table-column prop="iotConnected" label="物联状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.iotConnected ? 'success' : 'danger'" size="small">
                {{ row.iotConnected ? '已连接' : '未连接' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="deviceModel" label="设备型号" width="120" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)" size="small">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="location" label="位置" width="120" align="center" />
          <el-table-column prop="lastUpdate" label="最后更新" width="150" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewCharger(row)">
                详情
              </el-button>
              <el-button type="warning" size="small" @click="editCharger(row)">
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="deleteCharger(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 充电记录 -->
    <el-card style="margin-top: 20px;" v-loading="loading" element-loading-text="充电记录加载中...">
      <template #header>
        <div class="card-header">
          <h3>充电记录</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="refreshRecords" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新记录
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="chargingRecords" 
        style="width: 100%" 
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="recordId" label="记录ID" min-width="110" align="center" />
        <el-table-column prop="chargerId" label="充电桩ID" min-width="110" align="center" />
        <el-table-column prop="userId" label="用户ID" min-width="110" align="center" />
        <el-table-column prop="startTime" label="开始时间" min-width="150" align="center" />
        <el-table-column prop="endTime" label="结束时间" min-width="150" align="center" />
        <el-table-column prop="duration" label="充电时长" min-width="100" align="center" />
        <el-table-column prop="energy" label="充电量" min-width="100" align="center">
          <template #default="{ row }">
            {{ row.energy }}kWh
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="费用" min-width="90" align="center">
          <template #default="{ row }">
            ¥{{ row.cost }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" min-width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getPaymentStatusColor(row.paymentStatus)" size="small">
              {{ row.paymentStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="80">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewRecord(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加充电桩对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加充电桩"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="addFormRef"
        :model="chargerForm"
        :rules="chargerRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备ID" prop="chargerId">
              <el-input
                v-model="chargerForm.chargerId"
                placeholder="请输入设备ID"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="chargerName">
              <el-input
                v-model="chargerForm.chargerName"
                placeholder="请输入设备名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="chargerType">
              <el-select v-model="chargerForm.chargerType" placeholder="请选择设备类型" style="width: 100%">
                <el-option label="直流快充" value="直流快充" />
                <el-option label="交流慢充" value="交流慢充" />
                <el-option label="超级快充" value="超级快充" />
                <el-option label="换电设备" value="换电设备" />
                <el-option label="储能设备" value="储能设备" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备功率" prop="power">
              <el-input-number
                v-model="chargerForm.power"
                :min="1"
                :max="1000"
                :step="1"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #909399;">kW</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备位置" prop="location">
              <el-input
                v-model="chargerForm.location"
                placeholder="请输入设备位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="deviceModel">
              <el-input
                v-model="chargerForm.deviceModel"
                placeholder="请输入设备型号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="chargerForm.status" placeholder="请选择设备状态" style="width: 200px">
            <el-option label="空闲" value="空闲" />
            <el-option label="充电中" value="充电中" />
            <el-option label="故障" value="故障" />
            <el-option label="维护" value="维护" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false" :disabled="loading">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmAddCharger($refs.addFormRef)"
            :loading="loading"
          >
            确认添加
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑充电桩对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑充电桩"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="chargerForm"
        :rules="chargerRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备ID" prop="chargerId">
              <el-input
                v-model="chargerForm.chargerId"
                placeholder="请输入设备ID"
                maxlength="20"
                show-word-limit
                readonly
                style="background-color: #f5f7fa;"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 4px;">设备ID不可修改</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="chargerName">
              <el-input
                v-model="chargerForm.chargerName"
                placeholder="请输入设备名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="chargerType">
              <el-select v-model="chargerForm.chargerType" placeholder="请选择设备类型" style="width: 100%">
                <el-option label="直流快充" value="直流快充" />
                <el-option label="交流慢充" value="交流慢充" />
                <el-option label="超级快充" value="超级快充" />
                <el-option label="换电设备" value="换电设备" />
                <el-option label="储能设备" value="储能设备" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备功率" prop="power">
              <el-input-number
                v-model="chargerForm.power"
                :min="1"
                :max="1000"
                :step="1"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #909399;">kW</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备位置" prop="location">
              <el-input
                v-model="chargerForm.location"
                placeholder="请输入设备位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备型号" prop="deviceModel">
              <el-input
                v-model="chargerForm.deviceModel"
                placeholder="请输入设备型号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="chargerForm.status" placeholder="请选择设备状态" style="width: 200px">
            <el-option label="空闲" value="空闲" />
            <el-option label="充电中" value="充电中" />
            <el-option label="故障" value="故障" />
            <el-option label="维护" value="维护" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false" :disabled="loading">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmEditCharger($refs.editFormRef)"
            :loading="loading"
          >
            保存修改
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 充电桩详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="充电桩详情"
      width="700px"
    >
      <div v-if="viewingCharger">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备ID">
            <el-tag>{{ viewingCharger.chargerId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">
            {{ viewingCharger.chargerName }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag :type="getChargerTypeColor(viewingCharger.chargerType)">
              {{ viewingCharger.chargerType }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="getStatusColor(viewingCharger.status)">
              {{ viewingCharger.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备功率">
            {{ viewingCharger.power }}kW
          </el-descriptions-item>
          <el-descriptions-item label="当前功率">
            {{ viewingCharger.currentPower }}kW
          </el-descriptions-item>
          <el-descriptions-item label="功率利用率">
            <el-progress 
              :percentage="viewingCharger.utilizationRate" 
              :color="getUtilizationColor(viewingCharger.utilizationRate)"
              style="width: 90%;"
            />
          </el-descriptions-item>
          <el-descriptions-item label="充电量">
            {{ viewingCharger.chargingEnergy }}kWh
          </el-descriptions-item>
          <el-descriptions-item label="换电量">
            {{ viewingCharger.batterySwapCount }}次
          </el-descriptions-item>
          <el-descriptions-item label="放电量">
            {{ viewingCharger.dischargingEnergy }}kWh
          </el-descriptions-item>
          <el-descriptions-item label="总能量">
            {{ viewingCharger.totalEnergy }}kWh
          </el-descriptions-item>
          <el-descriptions-item label="能量平衡">
            <span :style="{ color: viewingCharger.energyBalance >= 0 ? '#67c23a' : '#f56c6c' }">
              {{ viewingCharger.energyBalance >= 0 ? '+' : '' }}{{ viewingCharger.energyBalance }}kWh
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="物联状态">
            <el-tag :type="viewingCharger.iotConnected ? 'success' : 'danger'">
              {{ viewingCharger.iotConnected ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备型号">
            {{ viewingCharger.deviceModel }}
          </el-descriptions-item>
          <el-descriptions-item label="设备位置">
            <el-icon><LocationInformation /></el-icon>
            {{ viewingCharger.location }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ viewingCharger.lastUpdate }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="editCharger(viewingCharger)">
            编辑设备
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 充电记录详情对话框 -->
    <el-dialog
      v-model="showRecordDetailDialog"
      title="充电记录详情"
      width="650px"
    >
      <div v-if="viewingRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            <el-tag>{{ viewingRecord.recordId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ viewingRecord.userId }}
          </el-descriptions-item>
          <el-descriptions-item label="充电桩ID">
            <el-tag type="primary">{{ viewingRecord.chargerId }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="充电桩名称">
            {{ viewingRecord.chargerName }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型">
            <el-tag :type="getChargerTypeColor(viewingRecord.chargerType)">
              {{ viewingRecord.chargerType }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusColor(viewingRecord.paymentStatus)">
              {{ viewingRecord.paymentStatus }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ viewingRecord.startTime }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ viewingRecord.endTime }}
          </el-descriptions-item>
          <el-descriptions-item label="充电时长">
            <el-tag type="info">{{ viewingRecord.duration }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="充电量">
            <strong style="color: #67c23a;">{{ viewingRecord.energy }}kWh</strong>
          </el-descriptions-item>
          <el-descriptions-item label="平均功率">
            {{ viewingRecord.avgPower }}kW
          </el-descriptions-item>
          <el-descriptions-item label="充电效率">
            {{ viewingRecord.efficiency }}%
          </el-descriptions-item>
          <el-descriptions-item label="总费用">
            <strong style="color: #f56c6c;">¥{{ viewingRecord.cost }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="单价">
            ¥{{ viewingRecord.pricePerKwh }}/kWh
          </el-descriptions-item>
        </el-descriptions>

        <!-- 费用明细 -->
        <div style="margin-top: 20px;">
          <el-divider content-position="left">费用明细</el-divider>
          <el-table 
            :data="[{
              item: '充电服务费',
              amount: (viewingRecord.cost * 0.8).toFixed(2),
              description: '基础充电费用'
            }, {
              item: '平台服务费',
              amount: (viewingRecord.cost * 0.15).toFixed(2),
              description: '平台维护费用'
            }, {
              item: '其他费用',
              amount: (viewingRecord.cost * 0.05).toFixed(2),
              description: '税费等其他费用'
            }]"
            style="width: 100%"
            size="small"
          >
            <el-table-column prop="item" label="费用项目" align="center" />
            <el-table-column prop="amount" label="金额(元)" align="center" />
            <el-table-column prop="description" label="说明" align="center" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRecordDetailDialog = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 充电策略配置对话框 -->
    <el-dialog
      v-model="showStrategyDialog"
      title="充电策略配置"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-tabs type="border-card">
        <!-- 峰谷电价配置 -->
        <el-tab-pane label="峰谷电价">
          <div style="margin-bottom: 20px;">
            <el-switch
              v-model="chargingStrategy.enableDynamicPricing"
              active-text="启用动态电价"
              inactive-text="使用固定电价"
            />
            <el-input-number
              v-if="!chargingStrategy.enableDynamicPricing"
              v-model="chargingStrategy.normalPrice"
              :min="0.1"
              :max="5"
              :step="0.1"
              style="margin-left: 20px; width: 150px;"
            />
            <span v-if="!chargingStrategy.enableDynamicPricing" style="margin-left: 8px;">元/kWh</span>
          </div>

          <div v-if="chargingStrategy.enableDynamicPricing">
            <!-- 高峰时段 -->
            <el-divider content-position="left">
              高峰时段配置
              <el-button type="primary" size="small" style="margin-left: 10px;" @click="addPeriod('peak')">
                添加高峰时段
              </el-button>
            </el-divider>
            
            <div v-for="(period, index) in chargingStrategy.peakHours" :key="`peak-${index}`" style="margin-bottom: 15px;">
              <el-row :gutter="10" align="middle">
                <el-col :span="5">
                  <el-input v-model="period.name" placeholder="时段名称" />
                </el-col>
                <el-col :span="4">
                  <el-time-select
                    v-model="period.start"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    placeholder="开始时间"
                  />
                </el-col>
                <el-col :span="4">
                  <el-time-select
                    v-model="period.end"
                    start="00:30"
                    step="00:30"
                    end="24:00"
                    placeholder="结束时间"
                  />
                </el-col>
                <el-col :span="4">
                  <el-input-number
                    v-model="period.price"
                    :min="0.1"
                    :max="5"
                    :step="0.1"
                    style="width: 100%;"
                  />
                </el-col>
                <el-col :span="2">
                  <span>元/kWh</span>
                </el-col>
                <el-col :span="3">
                  <el-button type="danger" size="small" @click="removePeriod('peak', index)">删除</el-button>
                </el-col>
              </el-row>
            </div>

            <!-- 低谷时段 -->
            <el-divider content-position="left">
              低谷时段配置
              <el-button type="success" size="small" style="margin-left: 10px;" @click="addPeriod('valley')">
                添加低谷时段
              </el-button>
            </el-divider>
            
            <div v-for="(period, index) in chargingStrategy.valleyHours" :key="`valley-${index}`" style="margin-bottom: 15px;">
              <el-row :gutter="10" align="middle">
                <el-col :span="5">
                  <el-input v-model="period.name" placeholder="时段名称" />
                </el-col>
                <el-col :span="4">
                  <el-time-select
                    v-model="period.start"
                    start="00:00"
                    step="00:30"
                    end="23:30"
                    placeholder="开始时间"
                  />
                </el-col>
                <el-col :span="4">
                  <el-time-select
                    v-model="period.end"
                    start="00:30"
                    step="00:30"
                    end="24:00"
                    placeholder="结束时间"
                  />
                </el-col>
                <el-col :span="4">
                  <el-input-number
                    v-model="period.price"
                    :min="0.1"
                    :max="5"
                    :step="0.1"
                    style="width: 100%;"
                  />
                </el-col>
                <el-col :span="2">
                  <span>元/kWh</span>
                </el-col>
                <el-col :span="3">
                  <el-button type="danger" size="small" @click="removePeriod('valley', index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>

        <!-- 充电控制策略 -->
        <el-tab-pane label="充电控制">
          <el-form label-width="150px">
            <el-form-item label="最大并发充电数">
              <el-slider
                v-model="chargingStrategy.maxConcurrentCharging"
                :min="20"
                :max="100"
                :step="5"
                show-stops
                show-input
                style="width: 300px;"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                当前设置：同时最多 {{ chargingStrategy.maxConcurrentCharging }} 个充电桩工作
              </div>
            </el-form-item>
            
            <el-form-item label="紧急预留容量">
              <el-slider
                v-model="chargingStrategy.emergencyReserve"
                :min="10"
                :max="50"
                :step="5"
                show-stops
                show-input
                style="width: 300px;"
              />
              <div style="font-size: 12px; color: #909399; margin-top: 5px;">
                预留 {{ chargingStrategy.emergencyReserve }}% 容量用于紧急充电
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showStrategyDialog = false" :disabled="loading">取消</el-button>
          <el-button type="primary" @click="saveChargingStrategy" :loading="loading">
            保存策略
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ArrowUp, ArrowDown, Download, Search, Plus, Refresh, LocationInformation, Setting } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能源监控中心', to: '/energy-monitoring' },
  { text: '充电桩管理', to: '' }
])

// 时间范围
const today = new Date()
const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
const selectedDateRange = ref<[Date, Date]>([yesterday, today])
const timeRange = ref('today')
const loading = ref(false)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')
const viewMode = ref('card')

// 对话框状态管理
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const showRecordDetailDialog = ref(false)
const showStrategyDialog = ref(false)
const editingCharger = ref<any>(null)
const viewingCharger = ref<any>(null)
const viewingRecord = ref<any>(null)

// 充电策略配置
const chargingStrategy = ref({
  peakHours: [
    { start: '09:00', end: '12:00', price: 1.2, name: '上午高峰' },
    { start: '18:00', end: '21:00', price: 1.5, name: '晚间高峰' }
  ],
  valleyHours: [
    { start: '23:00', end: '07:00', price: 0.5, name: '夜间低谷' },
    { start: '13:00', end: '17:00', price: 0.8, name: '下午平段' }
  ],
  normalPrice: 1.0,
  enableDynamicPricing: true,
  maxConcurrentCharging: 80,
  emergencyReserve: 20
})

// 添加/编辑表单数据
const chargerForm = ref({
  chargerId: '',
  chargerName: '',
  chargerType: '直流快充',
  power: 120,
  location: '',
  deviceModel: '',
  status: '空闲'
})

// 表单验证规则
const chargerRules = {
  chargerId: [
    { required: true, message: '请输入设备ID', trigger: 'blur' },
    { min: 3, max: 20, message: '设备ID长度在3到20个字符', trigger: 'blur' }
  ],
  chargerName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '设备名称长度在2到50个字符', trigger: 'blur' }
  ],
  chargerType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  power: [
    { required: true, message: '请输入设备功率', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '功率范围在1到1000kW之间', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入设备位置', trigger: 'blur' }
  ],
  deviceModel: [
    { required: true, message: '请输入设备型号', trigger: 'blur' }
  ]
}

// 图表引用
const chargingTrendChart = ref<HTMLDivElement>()
const chargingStatusChart = ref<HTMLDivElement>()
const chargingModeChart = ref<HTMLDivElement>()
const revenueChart = ref<HTMLDivElement>()

// 充电桩统计数据
const chargingStats = ref([
  {
    name: '充电桩总数',
    value: '128',
    unit: '个',
    color: '#409eff',
    icon: 'Connection',
    trend: 'up',
    changeRate: '5.2',
    comparePeriod: '较上期'
  },
  {
    name: '充电量',
    value: '15,680',
    unit: 'kWh',
    color: '#67c23a',
    icon: 'Lightning',
    trend: 'up',
    changeRate: '12.8',
    comparePeriod: '较昨日'
  },
  {
    name: '换电量',
    value: '2,350',
    unit: '次',
    color: '#e6a23c',
    icon: 'RefreshRight',
    trend: 'up',
    changeRate: '8.3',
    comparePeriod: '较昨日'
  },
  {
    name: '放电量',
    value: '4,520',
    unit: 'kWh',
    color: '#f56c6c',
    icon: 'Bottom',
    trend: 'down',
    changeRate: '2.1',
    comparePeriod: '较昨日'
  },
  {
    name: '今日收入',
    value: '12,580',
    unit: '元',
    color: '#909399',
    icon: 'Money',
    trend: 'up',
    changeRate: '15.6',
    comparePeriod: '较昨日'
  }
])

// 充电桩设备列表数据
const chargers = ref([
  {
    chargerId: 'CP001',
    chargerName: '快充桩A1',
    chargerType: '直流快充',
    power: 120,
    currentPower: 85,
    chargingEnergy: 1250.5,
    batterySwapCount: 0,
    dischargingEnergy: 0,
    utilizationRate: 78,
    status: '充电中',
    location: 'A区1号',
    iotConnected: true,
    deviceModel: 'DC-120kW-V2.0',
    lastUpdate: '2024-01-15 14:30:00'
  },
  {
    chargerId: 'CP002',
    chargerName: '快充桩A2',
    chargerType: '直流快充',
    power: 120,
    currentPower: 0,
    chargingEnergy: 980.2,
    batterySwapCount: 0,
    dischargingEnergy: 0,
    utilizationRate: 65,
    status: '空闲',
    location: 'A区2号',
    iotConnected: true,
    deviceModel: 'DC-120kW-V2.0',
    lastUpdate: '2024-01-15 14:29:00'
  },
  {
    chargerId: 'CP003',
    chargerName: '慢充桩B1',
    chargerType: '交流慢充',
    power: 7,
    currentPower: 7,
    chargingEnergy: 450.8,
    batterySwapCount: 0,
    dischargingEnergy: 0,
    utilizationRate: 85,
    status: '充电中',
    location: 'B区1号',
    iotConnected: true,
    deviceModel: 'AC-7kW-V1.5',
    lastUpdate: '2024-01-15 14:28:00'
  },
  {
    chargerId: 'CP004',
    chargerName: '换电桩B2',
    chargerType: '换电设备',
    power: 50,
    currentPower: 0,
    chargingEnergy: 0,
    batterySwapCount: 156,
    dischargingEnergy: 0,
    utilizationRate: 72,
    status: '空闲',
    location: 'B区2号',
    iotConnected: true,
    deviceModel: 'BS-50kW-V3.0',
    lastUpdate: '2024-01-15 14:27:00'
  },
  {
    chargerId: 'CP005',
    chargerName: '储能桩C1',
    chargerType: '储能设备',
    power: 350,
    currentPower: 0,
    chargingEnergy: 2450.0,
    batterySwapCount: 0,
    dischargingEnergy: 890.5,
    utilizationRate: 25,
    status: '故障',
    location: 'C区1号',
    iotConnected: false,
    deviceModel: 'ES-350kW-V4.0',
    lastUpdate: '2024-01-15 12:15:00'
  }
])

// 充电记录数据
const chargingRecords = ref([
  {
    recordId: 'R001',
    chargerId: 'CP001',
    userId: 'U001',
    startTime: '2024-01-15 13:00:00',
    endTime: '2024-01-15 14:30:00',
    duration: '1小时30分钟',
    energy: 45.6,
    cost: 28.5,
    paymentStatus: '已支付'
  },
  {
    recordId: 'R002',
    chargerId: 'CP003',
    userId: 'U002',
    startTime: '2024-01-15 12:30:00',
    endTime: '2024-01-15 18:00:00',
    duration: '5小时30分钟',
    energy: 38.5,
    cost: 19.2,
    paymentStatus: '已支付'
  },
  {
    recordId: 'R003',
    chargerId: 'CP002',
    userId: 'U003',
    startTime: '2024-01-15 11:00:00',
    endTime: '2024-01-15 12:20:00',
    duration: '1小时20分钟',
    energy: 52.3,
    cost: 31.4,
    paymentStatus: '待支付'
  }
])

// 筛选后的充电桩列表
const filteredChargers = computed(() => {
  return chargers.value.filter(charger => {
    // 多字段搜索：设备名称、设备ID、设备型号
    const searchLower = searchKeyword.value.toLowerCase()
    const matchesSearch = searchKeyword.value === '' || 
      charger.chargerName.toLowerCase().includes(searchLower) ||
      charger.chargerId.toLowerCase().includes(searchLower) ||
      charger.deviceModel.toLowerCase().includes(searchLower) ||
      charger.location.toLowerCase().includes(searchLower)
    
    // 状态筛选
    const matchesStatus = statusFilter.value === '' || charger.status === statusFilter.value
    
    return matchesSearch && matchesStatus
  })
})

// 获取充电桩类型颜色
const getChargerTypeColor = (type: string) => {
  switch (type) {
    case '直流快充': return 'success'
    case '交流慢充': return 'primary'
    case '超级快充': return 'danger'
    case '换电设备': return 'warning'
    case '储能设备': return 'info'
    default: return 'default'
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '空闲': return 'success'
    case '充电中': return 'warning'
    case '故障': return 'danger'
    case '维护': return 'info'
    default: return 'default'
  }
}

// 获取利用率颜色
const getUtilizationColor = (rate: number) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#409eff'
  if (rate >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 获取支付状态颜色
const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case '已支付': return 'success'
    case '待支付': return 'warning'
    case '已退款': return 'info'
    default: return 'default'
  }
}

// 生成图表数据（根据时间范围）
const generateChartData = (range: string) => {
  const chartData = {
    today: {
      xAxisData: ['0:00', '3:00', '6:00', '9:00', '12:00', '15:00', '18:00', '21:00'],
      dcFastData: [120, 380, 450, 520, 480, 420, 350, 280],
      acSlowData: [50, 120, 180, 220, 250, 200, 150, 100],
      superFastData: [0, 200, 350, 400, 350, 300, 250, 150]
    },
    week: {
      xAxisData: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      dcFastData: [3200, 4100, 3800, 4500, 4200, 5100, 4800],
      acSlowData: [1800, 2100, 1900, 2300, 2200, 2800, 2500],
      superFastData: [1500, 2200, 2000, 2600, 2400, 3200, 2900]
    },
    month: {
      xAxisData: ['第1周', '第2周', '第3周', '第4周'],
      dcFastData: [28000, 32000, 35000, 30000],
      acSlowData: [15000, 18000, 20000, 17000],
      superFastData: [12000, 16000, 18000, 15000]
    }
  }
  return chartData[range as keyof typeof chartData] || chartData.today
}

// 初始化充电功率趋势图表
const initChargingTrendChart = () => {
  if (!chargingTrendChart.value) return
  
  const chart = echarts.init(chargingTrendChart.value)
  const data = generateChartData(timeRange.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value}kW<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['直流快充', '交流慢充', '超级快充']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.xAxisData
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)'
    },
    series: [
      {
        name: '直流快充',
        type: 'line',
        data: data.dcFastData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '交流慢充',
        type: 'line',
        data: data.acSlowData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#409eff' }
      },
      {
        name: '超级快充',
        type: 'line',
        data: data.superFastData,
        smooth: true,
        areaStyle: { opacity: 0.3 },
        itemStyle: { color: '#f56c6c' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化充电桩状态图表
const initChargingStatusChart = () => {
  if (!chargingStatusChart.value) return
  
  const chart = echarts.init(chargingStatusChart.value)
  
  // 计算实时状态数据
  const statusCount = {
    '空闲': 0,
    '充电中': 0,
    '故障': 0,
    '维护': 0
  }
  
  chargers.value.forEach(charger => {
    if (charger.status in statusCount) {
      statusCount[charger.status as keyof typeof statusCount]++
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c}个 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['空闲', '充电中', '故障', '维护']
    },
    series: [
      {
        name: '充电桩状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: statusCount['空闲'], name: '空闲', itemStyle: { color: '#67c23a' } },
          { value: statusCount['充电中'], name: '充电中', itemStyle: { color: '#e6a23c' } },
          { value: statusCount['故障'], name: '故障', itemStyle: { color: '#f56c6c' } },
          { value: statusCount['维护'], name: '维护', itemStyle: { color: '#909399' } }
        ]
      }
    ]
  }
  chart.setOption(option)
}

// 初始化充电模式图表
const initChargingModeChart = () => {
  if (!chargingModeChart.value) return
  
  const chart = echarts.init(chargingModeChart.value)
  
  // 计算充电模式使用统计
  const modeCount = {
    '直流快充': 0,
    '交流慢充': 0,
    '超级快充': 0
  }
  
  chargers.value.forEach(charger => {
    if (charger.chargerType in modeCount) {
      modeCount[charger.chargerType as keyof typeof modeCount] += Math.floor(charger.chargingEnergy / 10)
    }
  })
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function(params: any) {
        return `${params[0].name}<br/>${params[0].marker}使用次数: ${params[0].value}次`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['直流快充', '交流慢充', '超级快充']
    },
    yAxis: {
      type: 'value',
      name: '使用次数'
    },
    series: [
      {
        name: '充电模式使用',
        type: 'bar',
        data: [modeCount['直流快充'], modeCount['交流慢充'], modeCount['超级快充']],
        itemStyle: {
          color: function(params: any) {
            const colors = ['#67c23a', '#409eff', '#f56c6c']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化收费统计图表
const initRevenueChart = () => {
  if (!revenueChart.value) return
  
  const chart = echarts.init(revenueChart.value)
  
  // 根据时间范围生成收入数据
  const generateRevenueData = () => {
    const baseDaily = [8500, 9200, 11200, 12800, 13500, 12580]
    const baseMonthly = [255000, 276000, 336000, 384000, 405000, 377400]
    
    // 基于选择的日期范围调整数据
    const daysDiff = selectedDateRange.value[1] && selectedDateRange.value[0] 
      ? Math.ceil((selectedDateRange.value[1].getTime() - selectedDateRange.value[0].getTime()) / (1000 * 60 * 60 * 24))
      : 7
    
    const multiplier = Math.max(0.5, Math.min(2.0, daysDiff / 30))
    
    return {
      daily: baseDaily.map(val => Math.round(val * multiplier)),
      monthly: baseMonthly.map(val => Math.round(val * multiplier))
    }
  }
  
  const revenueData = generateRevenueData()
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['日收入', '月收入']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '收入(元)',
      axisLabel: {
        formatter: function(value: number) {
          return value >= 1000 ? (value / 1000).toFixed(1) + 'k' : value
        }
      }
    },
    series: [
      {
        name: '日收入',
        type: 'bar',
        data: revenueData.daily,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '月收入',
        type: 'line',
        data: revenueData.monthly,
        itemStyle: { color: '#67c23a' },
        yAxisIndex: 0
      }
    ]
  }
  chart.setOption(option)
}

// 初始化所有图表
const initAllCharts = () => {
  if (chargingTrendChart.value) initChargingTrendChart()
  if (chargingStatusChart.value) initChargingStatusChart()
  if (chargingModeChart.value) initChargingModeChart()
  if (revenueChart.value) initRevenueChart()
}

// 根据日期范围生成动态数据
const generateDataByDateRange = (dates: [Date, Date]) => {
  if (!dates || !dates[0] || !dates[1]) return

  const startDate = new Date(dates[0])
  const endDate = new Date(dates[1])
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  // 根据天数差异计算数据倍率
  const dataMultiplier = Math.max(0.5, Math.min(2.0, daysDiff / 7))
  
  // 更新充电桩统计数据
  const baseStats = [
    { base: 128, unit: '个', name: '充电桩总数', color: '#409eff', icon: 'Connection' },
    { base: 15680, unit: 'kWh', name: '充电量', color: '#67c23a', icon: 'Lightning' },
    { base: 2350, unit: '次', name: '换电量', color: '#e6a23c', icon: 'RefreshRight' },
    { base: 4520, unit: 'kWh', name: '放电量', color: '#f56c6c', icon: 'Bottom' },
    { base: 12580, unit: '元', name: '今日收入', color: '#909399', icon: 'Money' }
  ]
  
  chargingStats.value = baseStats.map((stat, index) => ({
    name: stat.name,
    value: Math.round(stat.base * dataMultiplier).toLocaleString(),
    unit: stat.unit,
    color: stat.color,
    icon: stat.icon,
    trend: Math.random() > 0.3 ? 'up' : 'down',
    changeRate: (Math.random() * 20).toFixed(1),
    comparePeriod: daysDiff <= 1 ? '较昨日' : daysDiff <= 7 ? '较上周' : '较上期'
  }))
}

// 处理日期范围变化
const handleDateRangeChange = async (dates: [Date, Date]) => {
  console.log('日期范围变化:', dates)
  if (!dates || !dates[0] || !dates[1]) return
  
  loading.value = true
  try {
    // 模拟异步数据加载
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 生成新的数据
    generateDataByDateRange(dates)
    
    // 重新初始化图表
    nextTick(() => {
      initAllCharts()
    })
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理时间范围变化
const handleTimeRangeChange = async (range: string) => {
  console.log('时间范围变化:', range)
  
  loading.value = true
  try {
    // 模拟异步数据加载
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 重新初始化充电功率趋势图表
    if (chargingTrendChart.value) {
      initChargingTrendChart()
    }
  } catch (error) {
    console.error('更新图表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索防抖定时器
let searchTimeout: number

// 处理搜索
const handleSearch = () => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  // 设置新的防抖定时器
  searchTimeout = setTimeout(() => {
    console.log('搜索关键词:', searchKeyword.value)
    console.log('匹配结果数:', filteredChargers.value.length)
  }, 300)
}

// 处理状态筛选
const handleStatusFilter = () => {
  console.log('状态筛选:', statusFilter.value)
  console.log('筛选结果数:', filteredChargers.value.length)
}

// 导出数据为CSV格式
const exportData = async () => {
  try {
    loading.value = true
    
    // 准备导出数据
    const exportDate = new Date().toLocaleDateString()
    const dateRange = selectedDateRange.value
    const startDate = dateRange[0]?.toLocaleDateString() || ''
    const endDate = dateRange[1]?.toLocaleDateString() || ''
    
    // 1. 统计概览数据
    const statsData = [
      ['充电桩管理系统数据导出'],
      ['导出时间:', exportDate],
      ['数据时间范围:', `${startDate} 至 ${endDate}`],
      [''],
      ['统计概览'],
      ['指标名称', '数值', '单位', '趋势', '变化率'],
      ...chargingStats.value.map(stat => [
        stat.name,
        stat.value,
        stat.unit,
        stat.trend === 'up' ? '上升' : '下降',
        `${stat.changeRate}%`
      ]),
      [''],
    ]
    
    // 2. 设备数据
    const deviceData = [
      ['充电桩设备信息'],
      ['设备ID', '设备名称', '设备类型', '充电量(kWh)', '换电量(次)', '放电量(kWh)', '物联状态', '设备型号', '状态', '位置', '最后更新'],
      ...filteredChargers.value.map(charger => [
        charger.chargerId,
        charger.chargerName,
        charger.chargerType,
        charger.chargingEnergy,
        charger.batterySwapCount,
        charger.dischargingEnergy,
        charger.iotConnected ? '已连接' : '未连接',
        charger.deviceModel,
        charger.status,
        charger.location,
        charger.lastUpdate
      ]),
      [''],
    ]
    
    // 3. 充电记录数据
    const recordData = [
      ['充电记录信息'],
      ['记录ID', '充电桩ID', '用户ID', '开始时间', '结束时间', '充电时长', '充电量(kWh)', '费用(元)', '支付状态'],
      ...chargingRecords.value.map(record => [
        record.recordId,
        record.chargerId,
        record.userId,
        record.startTime,
        record.endTime,
        record.duration,
        record.energy,
        record.cost,
        record.paymentStatus
      ])
    ]
    
    // 合并所有数据
    const allData = [...statsData, ...deviceData, ...recordData]
    
    // 转换为CSV格式
    const csvContent = '\uFEFF' + allData.map(row => 
      row.map(cell => `"${cell}"`).join(',')
    ).join('\n')
    
    // 创建并下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `充电桩管理数据_${exportDate.replace(/\//g, '')}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    console.log('数据导出成功')
    
    // 显示成功消息
    ElMessage.success('数据导出成功！')
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('数据导出失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetChargerForm = () => {
  chargerForm.value = {
    chargerId: '',
    chargerName: '',
    chargerType: '直流快充',
    power: 120,
    location: '',
    deviceModel: '',
    status: '空闲'
  }
}

// 检查设备ID唯一性
const checkChargerIdUnique = (id: string, excludeId?: string) => {
  return !chargers.value.some(charger => 
    charger.chargerId === id && charger.chargerId !== excludeId
  )
}

// 添加充电桩
const addCharger = () => {
  resetChargerForm()
  showAddDialog.value = true
}

// 确认添加充电桩
const confirmAddCharger = async (formEl: any) => {
  if (!formEl) return
  
  try {
    await formEl.validate()
    
    // 检查设备ID唯一性
    if (!checkChargerIdUnique(chargerForm.value.chargerId)) {
      ElMessage.error('设备ID已存在，请使用其他ID')
      return
    }
    
    loading.value = true
    
    // 模拟异步添加过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建新充电桩对象
    const newCharger = {
      ...chargerForm.value,
      currentPower: 0,
      chargingEnergy: 0,
      batterySwapCount: 0,
      dischargingEnergy: 0,
      utilizationRate: 0,
      iotConnected: true,
      lastUpdate: new Date().toLocaleString()
    }
    
    // 添加到列表
    chargers.value.push(newCharger)
    
    showAddDialog.value = false
    ElMessage.success('充电桩添加成功！')
    
  } catch (error) {
    console.error('添加失败:', error)
    ElMessage.error('添加失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 查看充电桩详情
const viewCharger = (charger: any) => {
  viewingCharger.value = {
    ...charger,
    // 计算利用率等额外信息
    utilizationRate: Math.round((charger.currentPower / charger.power) * 100),
    totalEnergy: charger.chargingEnergy + charger.dischargingEnergy,
    energyBalance: charger.chargingEnergy - charger.dischargingEnergy
  }
  showDetailDialog.value = true
}

// 编辑充电桩
const editCharger = (charger: any) => {
  editingCharger.value = charger
  chargerForm.value = {
    chargerId: charger.chargerId,
    chargerName: charger.chargerName,
    chargerType: charger.chargerType,
    power: charger.power,
    location: charger.location,
    deviceModel: charger.deviceModel,
    status: charger.status
  }
  showEditDialog.value = true
}

// 确认编辑充电桩
const confirmEditCharger = async (formEl: any) => {
  if (!formEl) return
  
  try {
    await formEl.validate()
    
    // 检查设备ID唯一性（排除自己）
    if (!checkChargerIdUnique(chargerForm.value.chargerId, editingCharger.value?.chargerId)) {
      ElMessage.error('设备ID已存在，请使用其他ID')
      return
    }
    
    loading.value = true
    
    // 模拟异步编辑过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 找到要编辑的充电桩并更新
    const index = chargers.value.findIndex(c => c.chargerId === editingCharger.value?.chargerId)
    if (index !== -1) {
      chargers.value[index] = {
        ...chargers.value[index],
        ...chargerForm.value,
        lastUpdate: new Date().toLocaleString()
      }
    }
    
    showEditDialog.value = false
    editingCharger.value = null
    ElMessage.success('充电桩信息更新成功！')
    
  } catch (error) {
    console.error('编辑失败:', error)
    ElMessage.error('编辑失败，请检查表单信息')
  } finally {
    loading.value = false
  }
}

// 删除充电桩
const deleteCharger = async (charger: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除充电桩 "${charger.chargerName}" (${charger.chargerId}) 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    loading.value = true
    
    // 模拟异步删除过程
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 从列表中移除
    const index = chargers.value.findIndex(c => c.chargerId === charger.chargerId)
    if (index !== -1) {
      chargers.value.splice(index, 1)
    }
    
    ElMessage.success('充电桩删除成功！')
    
  } catch (error) {
    if (error === 'cancel') {
      console.log('用户取消删除')
    } else {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 生成随机充电记录
const generateRandomRecords = () => {
  const recordCount = Math.floor(Math.random() * 5) + 3 // 3-7条记录
  const newRecords = []
  
  for (let i = 0; i < recordCount; i++) {
    const charger = chargers.value[Math.floor(Math.random() * chargers.value.length)]
    const startTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    const endTime = new Date(startTime.getTime() + Math.random() * 6 * 60 * 60 * 1000)
    const energy = Math.random() * 80 + 10
    const cost = energy * (0.5 + Math.random() * 1.0)
    
    newRecords.push({
      recordId: `R${Date.now()}_${i}`,
      chargerId: charger.chargerId,
      userId: `U${String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')}`,
      startTime: startTime.toLocaleString(),
      endTime: endTime.toLocaleString(),
      duration: `${Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60))}分钟`,
      energy: Math.round(energy * 10) / 10,
      cost: Math.round(cost * 10) / 10,
      paymentStatus: Math.random() > 0.2 ? '已支付' : '待支付'
    })
  }
  
  return newRecords
}

// 刷新记录
const refreshRecords = async () => {
  try {
    loading.value = true
    
    // 模拟异步数据加载
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    // 生成新的充电记录
    chargingRecords.value = generateRandomRecords()
    
    ElMessage.success('充电记录刷新成功！')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败，请重试')
  } finally {
    loading.value = false
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  try {
    loading.value = true
    
    // 模拟异步数据加载
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 重新生成统计数据
    generateDataByDateRange(selectedDateRange.value)
    
    // 重新生成充电记录
    chargingRecords.value = generateRandomRecords()
    
    // 重新初始化图表
    nextTick(() => {
      initAllCharts()
    })
    
    ElMessage.success('数据刷新成功！')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('数据刷新失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查看充电记录详情
const viewRecord = (record: any) => {
  // 计算额外信息
  const charger = chargers.value.find(c => c.chargerId === record.chargerId)
  const avgPower = record.energy ? (record.energy / parseFloat(record.duration.replace(/[^\d.]/g, ''))).toFixed(2) : '0'
  const efficiency = charger ? ((record.energy / charger.power) * 100).toFixed(1) : '0'
  
  viewingRecord.value = {
    ...record,
    chargerName: charger?.chargerName || '未知设备',
    chargerType: charger?.chargerType || '未知类型',
    avgPower,
    efficiency,
    pricePerKwh: (record.cost / record.energy).toFixed(2)
  }
  showRecordDetailDialog.value = true
}

// 保存充电策略
const saveChargingStrategy = async () => {
  try {
    loading.value = true
    
    // 模拟异步保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showStrategyDialog.value = false
    ElMessage.success('充电策略配置保存成功！')
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('策略保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 添加时段
const addPeriod = (type: 'peak' | 'valley') => {
  const newPeriod = {
    start: '00:00',
    end: '01:00',
    price: type === 'peak' ? 1.2 : 0.8,
    name: type === 'peak' ? '高峰时段' : '低谷时段'
  }
  
  if (type === 'peak') {
    chargingStrategy.value.peakHours.push(newPeriod)
  } else {
    chargingStrategy.value.valleyHours.push(newPeriod)
  }
}

// 删除时段
const removePeriod = (type: 'peak' | 'valley', index: number) => {
  if (type === 'peak') {
    chargingStrategy.value.peakHours.splice(index, 1)
  } else {
    chargingStrategy.value.valleyHours.splice(index, 1)
  }
}

onMounted(() => {
  nextTick(() => {
    // 初始化数据
    generateDataByDateRange(selectedDateRange.value)
    // 初始化图表
    initAllCharts()
  })
})
</script>

<style scoped>
.charging-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.overview-card {
  margin-bottom: 20px;
}

.charging-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 10px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: #ebeef5;
}

.stat-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 50%;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.stat-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stat-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stat-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

/* 头部控件样式 */
.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.view-controls {
  flex-shrink: 0;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 设备卡片样式 */
.device-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.device-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.device-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.device-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.device-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.device-card-body {
  margin-bottom: 12px;
}

.device-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.device-connection, .device-model {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.connection-label, .model-label {
  color: #606266;
  margin-right: 8px;
}

.model-value {
  color: #303133;
  font-weight: 500;
}

.device-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ebeef5;
  padding-top: 8px;
}

.device-location {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.device-location .el-icon {
  margin-right: 4px;
}

.device-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charging-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .stat-item {
    flex: none;
    max-width: none;
    justify-content: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }
  
  .stat-item:not(:last-child)::after {
    display: none;
  }
  
  .stat-item:last-child {
    margin-bottom: 0;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .filter-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .device-cards {
    grid-template-columns: 1fr;
  }
  
  .device-metrics {
    grid-template-columns: 1fr;
  }
  
  .device-card-footer {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 