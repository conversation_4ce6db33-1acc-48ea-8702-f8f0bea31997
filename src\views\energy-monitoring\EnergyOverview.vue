<template>
  <div class="energy-overview" v-loading="loading" element-loading-text="正在加载数据...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 能源总览统计 -->
    <el-card class="overview-card">
      <template #header>
        <div class="card-header">
          <h3>能源总览统计</h3>
          <div class="header-controls">
          <el-date-picker
              v-model="selectedDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            size="small"
              @change="handleDateRangeChange"
          />
            <el-button type="primary" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
            <el-button type="success" size="small" @click="refreshAllData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="energy-stats">
        <div class="energy-item" v-for="(item, index) in energyStats" :key="index">
          <div class="energy-icon" :style="{ backgroundColor: item.color + '20', borderColor: item.color }">
            <el-icon :size="28" :color="item.color">
              <component :is="item.icon" />
            </el-icon>
          </div>
          <div class="energy-info">
            <div class="energy-name">{{ item.name }}</div>
            <div class="energy-value-container">
              <span class="energy-value">{{ item.value }}</span>
              <span class="energy-unit">{{ item.unit }}</span>
            </div>
            <div class="energy-trend" :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
              {{ item.comparePeriod }}{{ item.trend === 'up' ? '增长' : '下降' }} {{ item.trend === 'up' ? '+' : '-' }}{{ item.changeRate }}%
            </div>
          </div>
          <div class="energy-divider" v-if="index < energyStats.length - 1"></div>
        </div>
      </div>
    </el-card>

    <!-- 能源排名与趋势分析 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>能源消耗排名</h3>
          </template>
          <div class="ranking-chart" ref="rankingChart"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <h3>能源消耗趋势</h3>
          </template>
          <div class="trend-chart" ref="trendChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 能源分布与对比 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card>
          <template #header>
            <h3>能源分布</h3>
          </template>
          <div class="distribution-chart" ref="distributionChart"></div>
        </el-card>
      </el-col>
      
      <el-col :span="16">
        <el-card>
          <template #header>
                          <div class="card-header">
                <div class="header-title-group">
                  <h3>能源对比分析</h3>
                  <el-select v-model="compareType" size="small" @change="handleCompareChange" style="min-width: 100px;" placeholder="请选择">
                    <el-option label="同比" value="year" />
                    <el-option label="环比" value="month" />
                    <el-option label="日对比" value="day" />
                  </el-select>
                </div>
              </div>
          </template>
          <div class="compare-chart" ref="compareChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时监控数据 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <h3>实时监控数据</h3>
      </template>
      
      <el-table 
        :data="realtimeData" 
        style="width: 100%" 
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
        border
      >
        <el-table-column prop="name" label="能源类型" align="center" />
        <el-table-column prop="currentValue" label="当前值" align="center">
          <template #default="scope">
            <span>{{ scope.row.currentValue }} {{ scope.row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="todayTotal" label="今日累计" align="center">
          <template #default="scope">
            <span>{{ scope.row.todayTotal }} {{ scope.row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="monthTotal" label="本月累计" align="center">
          <template #default="scope">
            <span>{{ scope.row.monthTotal }} {{ scope.row.unit }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="efficiency" label="效率" align="center">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.efficiency" 
              :color="getEfficiencyColor(scope.row.efficiency)"
              :stroke-width="8"
              style="width: 90%"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import { Download, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { 
  Lightning, 
  Sunny, 
  ArrowUp,
  ArrowDown,
  Promotion,
  Refrigerator,
  Cpu
} from '@element-plus/icons-vue'

// 面包屑导航
const breadcrumbItems = ref([
  { text: '首页', to: '/homepage' },
  { text: '能源监控中心', to: '/energy-monitoring' },
  { text: '能源总览', to: '' }
])

// 初始化日期范围为过去7天
const today = new Date()
const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
const selectedDateRange = ref<[Date, Date]>([weekAgo, today])
const compareType = ref('month')
const loading = ref(false)

// 能源统计数据
const energyStats = ref([
  {
    name: '总用电量',
    value: '1,234.5',
    unit: 'kWh',
    color: '#409eff',
    icon: Lightning,
    trend: 'up',
    changeRate: '5.2',
    comparePeriod: '较昨日'
  },
  {
    name: '用水量',
    value: '456.8',
    unit: 'm³',
    color: '#67c23a',
    icon: Promotion,
    trend: 'down',
    changeRate: '2.1',
    comparePeriod: '较昨日'
  },
  {
    name: '天然气',
    value: '89.2',
    unit: 'm³',
    color: '#e6a23c',
    icon: Sunny,
    trend: 'up',
    changeRate: '1.8',
    comparePeriod: '较昨日'
  },
  {
    name: '供热量',
    value: '567.3',
    unit: 'GJ',
    color: '#f56c6c',
    icon: Cpu,
    trend: 'up',
    changeRate: '3.5',
    comparePeriod: '较昨日'
  },
  {
    name: '制冷量',
    value: '234.7',
    unit: 'GJ',
    color: '#909399',
    icon: Refrigerator,
    trend: 'down',
    changeRate: '1.2',
    comparePeriod: '较昨日'
  }
])

// 实时监控数据
const realtimeData = ref([
  {
    name: '电力',
    currentValue: '125.6',
    todayTotal: '1,234.5',
    monthTotal: '35,678.9',
    unit: 'kWh',
    status: '正常',
    efficiency: 85
  },
  {
    name: '水',
    currentValue: '45.2',
    todayTotal: '456.8',
    monthTotal: '12,345.6',
    unit: 'm³',
    status: '正常',
    efficiency: 78
  },
  {
    name: '天然气',
    currentValue: '12.3',
    todayTotal: '89.2',
    monthTotal: '2,567.8',
    unit: 'm³',
    status: '正常',
    efficiency: 92
  },
  {
    name: '供热',
    currentValue: '67.8',
    todayTotal: '567.3',
    monthTotal: '15,234.7',
    unit: 'GJ',
    status: '正常',
    efficiency: 73
  },
  {
    name: '制冷',
    currentValue: '23.4',
    todayTotal: '234.7',
    monthTotal: '6,789.2',
    unit: 'GJ',
    status: '正常',
    efficiency: 68
  }
])

const rankingChart = ref()
const trendChart = ref()
const distributionChart = ref()
const compareChart = ref()

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 80) return '#67c23a'
  if (efficiency >= 60) return '#409eff'
  if (efficiency >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 初始化排名图表
const initRankingChart = () => {
  const chart = echarts.init(rankingChart.value)
  
  // 从energyStats数据生成图表数据
  const sortedStats = [...energyStats.value].sort((a, b) => parseFloat(a.value) - parseFloat(b.value))
  const names = sortedStats.map(stat => stat.name)
  const values = sortedStats.map(stat => parseFloat(stat.value))
  const colors = sortedStats.map(stat => stat.color)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const data = params[0]
        const stat = sortedStats[data.dataIndex]
        return `${data.name}: ${data.value} ${stat.unit}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: names
    },
    series: [
      {
        name: '消耗量',
        type: 'bar',
        data: values,
        itemStyle: {
          color: function(params: any) {
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  const chart = echarts.init(trendChart.value)
  
  // 根据日期范围生成时间轴数据
  const [startDate, endDate] = selectedDateRange.value
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  let timeData: string[] = []
  let dataPointCount = 7
  
  if (daysDiff <= 1) {
    // 一天内显示小时数据
    timeData = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
  } else if (daysDiff <= 7) {
    // 一周内显示天数据
    timeData = []
    for (let i = 0; i < daysDiff; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
      timeData.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
    }
    dataPointCount = daysDiff
  } else {
    // 超过一周显示周数据
    const weeks = Math.min(12, Math.ceil(daysDiff / 7))
    timeData = []
    for (let i = 0; i < weeks; i++) {
      const date = new Date(startDate.getTime() + i * 7 * 24 * 60 * 60 * 1000)
      timeData.push(`第${i + 1}周`)
    }
    dataPointCount = weeks
  }
  
  // 为每个能源类型生成数据
  const generateSeriesData = (baseValue: number, variance: number = 0.3) => {
    const data: number[] = []
    for (let i = 0; i < dataPointCount; i++) {
      const value = baseValue * (1 + (Math.random() - 0.5) * variance)
      data.push(Math.round(value))
    }
    return data
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((item: any) => {
          const unit = energyStats.value.find(stat => stat.name.includes(item.seriesName))?.unit || ''
          result += `${item.marker} ${item.seriesName}: ${item.value} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['总用电量', '用水量', '天然气', '供热量', '制冷量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timeData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '总用电量',
        type: 'line',
        areaStyle: { opacity: 0.3 },
        data: generateSeriesData(parseFloat(energyStats.value[0]?.value || '1234')),
        smooth: true,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '用水量',
        type: 'line',
        areaStyle: { opacity: 0.3 },
        data: generateSeriesData(parseFloat(energyStats.value[1]?.value || '456')),
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '天然气',
        type: 'line',
        areaStyle: { opacity: 0.3 },
        data: generateSeriesData(parseFloat(energyStats.value[2]?.value || '89')),
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '供热量',
        type: 'line',
        areaStyle: { opacity: 0.3 },
        data: generateSeriesData(parseFloat(energyStats.value[3]?.value || '567')),
        smooth: true,
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '制冷量',
        type: 'line',
        areaStyle: { opacity: 0.3 },
        data: generateSeriesData(parseFloat(energyStats.value[4]?.value || '234')),
        smooth: true,
        itemStyle: { color: '#909399' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化分布图表
const initDistributionChart = () => {
  const chart = echarts.init(distributionChart.value)
  
  // 从energyStats数据生成饼图数据
  const pieData = energyStats.value.map(stat => ({
    value: parseFloat(stat.value),
    name: stat.name,
    itemStyle: { color: stat.color }
  }))
  
  const legendData = energyStats.value.map(stat => stat.name)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const stat = energyStats.value.find(s => s.name === params.name)
        return `${params.seriesName}<br/>${params.marker} ${params.name}: ${params.value} ${stat?.unit || ''} (${params.percent}%)`
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: legendData
    },
    series: [
      {
        name: '能源分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {c}'
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化对比图表
const initCompareChart = () => {
  const chart = echarts.init(compareChart.value)
  
  let xAxisData: string[] = []
  let currentPeriodData: number[] = []
  let lastPeriodData: number[] = []
  let legendData = ['本期', '对比期']
  
  // 根据对比类型生成不同的数据
  switch (compareType.value) {
    case 'year':
      // 同比：今年 vs 去年
      legendData = ['今年', '去年同期']
      for (let i = 1; i <= 12; i++) {
        xAxisData.push(`${i}月`)
        currentPeriodData.push(Math.floor(1000 + Math.random() * 800))
        lastPeriodData.push(Math.floor(900 + Math.random() * 700))
      }
      break
    case 'month':
      // 环比：本月 vs 上月
      legendData = ['本月', '上月同期']
      const today = new Date()
      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate()
      const weeksInMonth = Math.ceil(daysInMonth / 7)
      for (let i = 1; i <= weeksInMonth; i++) {
        xAxisData.push(`第${i}周`)
        currentPeriodData.push(Math.floor(1200 + Math.random() * 600))
        lastPeriodData.push(Math.floor(1100 + Math.random() * 500))
      }
      break
    case 'day':
      // 日对比：今日 vs 昨日
      legendData = ['今日', '昨日同期']
      for (let i = 0; i < 24; i += 2) {
        xAxisData.push(`${i}:00`)
        currentPeriodData.push(Math.floor(50 + Math.random() * 100))
        lastPeriodData.push(Math.floor(45 + Math.random() * 90))
      }
      break
    default:
      // 默认根据日期范围生成数据
      const [startDate, endDate] = selectedDateRange.value
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      
      if (daysDiff <= 7) {
        legendData = ['选定期间', '对比期间']
        for (let i = 0; i < daysDiff; i++) {
          const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
          xAxisData.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))
          currentPeriodData.push(Math.floor(800 + Math.random() * 400))
          lastPeriodData.push(Math.floor(700 + Math.random() * 400))
        }
      } else if (daysDiff <= 31) {
        legendData = ['选定期间', '对比期间']
        const weeks = Math.ceil(daysDiff / 7)
        for (let i = 0; i < weeks; i++) {
          xAxisData.push(`第${i + 1}周`)
          currentPeriodData.push(Math.floor(1200 + Math.random() * 600))
          lastPeriodData.push(Math.floor(1000 + Math.random() * 600))
        }
      } else {
        legendData = ['选定期间', '对比期间']
        const months = Math.min(12, Math.ceil(daysDiff / 30))
        for (let i = 0; i < months; i++) {
          const date = new Date(startDate.getTime() + i * 30 * 24 * 60 * 60 * 1000)
          xAxisData.push(date.toLocaleDateString('zh-CN', { month: 'short' }))
          currentPeriodData.push(Math.floor(1500 + Math.random() * 800))
          lastPeriodData.push(Math.floor(1300 + Math.random() * 800))
        }
      }
  }
  
  const maxValue = Math.max(...currentPeriodData, ...lastPeriodData)
  const yAxisMax = Math.ceil(maxValue * 1.2 / 500) * 500
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker} ${item.seriesName}: ${item.value} kWh<br/>`
        })
        return result
      }
    },
    toolbox: {
      feature: {
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar'] },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    legend: {
      data: legendData
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '消耗量',
        min: 0,
        max: yAxisMax,
        interval: yAxisMax / 5,
        axisLabel: {
          formatter: '{value} kWh'
        }
      }
    ],
    series: [
      {
        name: legendData[0],
        type: 'bar',
        data: currentPeriodData,
        itemStyle: { color: '#409eff' }
      },
      {
        name: legendData[1],
        type: 'bar',
        data: lastPeriodData,
        itemStyle: { color: '#67c23a' }
      }
    ]
  }
  chart.setOption(option)
}

// 生成基于日期范围的模拟数据
const generateDataByDateRange = (startDate: Date, endDate: Date) => {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const multiplier = Math.max(1, daysDiff / 30) // 基于天数差异调整数据量
  
  // 生成统计数据
  const baseStats = [
    { name: '总用电量', baseValue: 1234.5, unit: 'kWh', color: '#409eff', icon: Lightning },
    { name: '用水量', baseValue: 456.8, unit: 'm³', color: '#67c23a', icon: Promotion },
    { name: '天然气', baseValue: 89.2, unit: 'm³', color: '#e6a23c', icon: Sunny },
    { name: '供热量', baseValue: 567.3, unit: 'GJ', color: '#f56c6c', icon: Cpu },
    { name: '制冷量', baseValue: 234.7, unit: 'GJ', color: '#909399', icon: Refrigerator }
  ]
  
  return baseStats.map(stat => ({
    ...stat,
    value: (stat.baseValue * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    trend: Math.random() > 0.5 ? 'up' : 'down',
    changeRate: (Math.random() * 10).toFixed(1),
    comparePeriod: daysDiff <= 1 ? '较昨日' : daysDiff <= 7 ? '较上周' : '较上月'
  }))
}

// 生成实时监控数据
const generateRealtimeData = (multiplier: number = 1) => {
  const baseData = [
    { name: '电力', baseValue: 125.6, todayBase: 1234.5, monthBase: 35678.9, unit: 'kWh' },
    { name: '水', baseValue: 45.2, todayBase: 456.8, monthBase: 12345.6, unit: 'm³' },
    { name: '天然气', baseValue: 12.3, todayBase: 89.2, monthBase: 2567.8, unit: 'm³' },
    { name: '供热', baseValue: 67.8, todayBase: 567.3, monthBase: 15234.7, unit: 'GJ' },
    { name: '制冷', baseValue: 23.4, todayBase: 234.7, monthBase: 6789.1, unit: 'GJ' }
  ]
  
  return baseData.map(item => ({
    ...item,
    currentValue: (item.baseValue * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    todayTotal: (item.todayBase * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    monthTotal: (item.monthBase * multiplier * (0.8 + Math.random() * 0.4)).toFixed(1),
    status: Math.random() > 0.1 ? '正常' : '异常',
    efficiency: Math.floor(70 + Math.random() * 25)
  }))
}

// 刷新所有数据
const refreshAllData = async () => {
  loading.value = true
  
  try {
    const [startDate, endDate] = selectedDateRange.value
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const multiplier = Math.max(1, daysDiff / 30)
    
    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新统计数据
    energyStats.value = generateDataByDateRange(startDate, endDate)
    
    // 更新实时监控数据
    realtimeData.value = generateRealtimeData(multiplier)
    
    // 重新初始化图表
  nextTick(() => {
    initRankingChart()
    initTrendChart()
    initDistributionChart()
    initCompareChart()
  })
  } finally {
    loading.value = false
  }
}

// 处理日期范围变化
const handleDateRangeChange = async (dates: [Date, Date]) => {
  if (dates && dates.length === 2) {
    selectedDateRange.value = dates
    console.log('日期范围变化:', dates)
    await refreshAllData()
  }
}

// 处理对比类型变化
const handleCompareChange = async (type: string) => {
  compareType.value = type
  console.log('对比类型变化:', type)
  // 只重新初始化对比图表，不影响其他模块
  await nextTick(() => {
    initCompareChart()
  })
}

// 导出数据
const exportData = () => {
  const [startDate, endDate] = selectedDateRange.value
  const dateRange = `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
  
  // 创建CSV内容
  let csvContent = "数据类型,数值,单位,变化趋势,变化率,对比周期\n"
  
  energyStats.value.forEach(stat => {
    csvContent += `${stat.name},${stat.value},${stat.unit},${stat.trend === 'up' ? '上升' : '下降'},${stat.changeRate}%,${stat.comparePeriod}\n`
  })
  
  csvContent += "\n实时监控数据\n"
  csvContent += "能源类型,当前值,今日总计,本月总计,单位,状态,效率\n"
  
  realtimeData.value.forEach(item => {
    csvContent += `${item.name},${item.currentValue},${item.todayTotal},${item.monthTotal},${item.unit},${item.status},${item.efficiency}%\n`
  })
  
  // 创建下载链接，添加BOM头支持中文
  const BOM = '\uFEFF'
  const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `能源总览数据_${dateRange.replace(/\//g, '-')}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  console.log('数据导出完成')
  
  // 显示成功提示
  ElMessage.success('数据导出成功！')
}

onMounted(async () => {
  // 初始化数据
  await refreshAllData()
})
</script>

<style scoped>
.energy-overview {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.overview-card {
  margin-bottom: 20px;
}

.energy-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 30px 20px;
  background: #fafafa;
  border-radius: 8px;
  margin: 10px 0;
}

.energy-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  min-width: 180px;
  padding: 0 15px;
  position: relative;
}

.energy-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  bottom: 20%;
  width: 1px;
  background: #ebeef5;
}

.energy-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 50%;
  border: 2px solid transparent;
  position: relative;
  flex-shrink: 0;
}

.energy-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
}

.energy-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.energy-value-container {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 6px;
}

.energy-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.energy-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.energy-trend {
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 3px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.energy-divider {
  display: none;
}

.ranking-chart,
.trend-chart,
.distribution-chart,
.compare-chart {
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .energy-stats {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }
  
  .energy-item {
    flex: none;
    max-width: none;
    justify-content: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }
  
  .energy-item:not(:last-child)::after {
    display: none;
  }
  
  .energy-item:last-child {
    margin-bottom: 0;
  }
}
</style> 