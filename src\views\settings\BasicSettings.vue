<template>
  <div class="settings-container">
    <BreadCrumb :items="breadcrumbItems" />
    
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <h3>基础设置</h3>
        </div>
      </template>
      
      <el-form :model="basicSettings" label-width="120px">
        <el-form-item label="系统名称">
          <el-input v-model="basicSettings.systemName" />
        </el-form-item>
        
        <el-form-item label="管理员邮箱">
          <el-input v-model="basicSettings.adminEmail" />
        </el-form-item>
        
        <el-form-item label="时区">
          <el-select v-model="basicSettings.timezone" style="width: 100%">
            <el-option label="北京时间 (UTC+8)" value="UTC+8" />
            <el-option label="东京时间 (UTC+9)" value="UTC+9" />
            <el-option label="伦敦时间 (UTC+0)" value="UTC+0" />
            <el-option label="纽约时间 (UTC-5)" value="UTC-5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期格式">
          <el-select v-model="basicSettings.dateFormat" style="width: 100%">
            <el-option label="YYYY-MM-DD" value="YYYY-MM-DD" />
            <el-option label="DD/MM/YYYY" value="DD/MM/YYYY" />
            <el-option label="MM/DD/YYYY" value="MM/DD/YYYY" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="语言">
          <el-select v-model="basicSettings.language" style="width: 100%">
            <el-option label="简体中文" value="zh-CN" />
            <el-option label="English" value="en-US" />
            <el-option label="日本語" value="ja-JP" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
          <el-button @click="resetBasicSettings">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'

// 面包屑数据
const breadcrumbItems = ref([
  { text: '系统设置' },
  { text: '基础设置' }
])

// 基础设置
const basicSettings = reactive({
  systemName: '能源管理系统',
  adminEmail: '<EMAIL>',
  timezone: 'UTC+8',
  dateFormat: 'YYYY-MM-DD',
  language: 'zh-CN'
})

// 保存基础设置
const saveBasicSettings = () => {
  ElMessage.success('基础设置保存成功')
}

// 重置基础设置
const resetBasicSettings = () => {
  basicSettings.systemName = '能源管理系统'
  basicSettings.adminEmail = '<EMAIL>'
  basicSettings.timezone = 'UTC+8'
  basicSettings.dateFormat = 'YYYY-MM-DD'
  basicSettings.language = 'zh-CN'
  ElMessage.info('基础设置已重置')
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
  width: 100%;
}

.settings-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}
</style> 