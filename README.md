# 能源管理系统

基于Vue 3、TypeScript和Element Plus的能源管理系统前端框架。

## 技术栈

- Vue 3 (Composition API)
- TypeScript
- Vite 4
- Element Plus
- Vue Router
- Pinia (状态管理)

## 功能特性

- 响应式布局，适配主流浏览器
- 基于角色的权限控制
- 用户认证与授权
- 能源数据可视化
- 设备管理与监控
- 支持外部网络访问（配置端口3003）

## 项目结构

```
src/
├── assets/       # 静态资源
│   └── images/   # 图片
├── components/   # 公共组件
├── views/        # 页面视图
├── store/        # 状态管理
├── types/        # TypeScript类型定义
├── styles/       # 全局样式
├── router/       # 路由配置
├── App.vue       # 根组件
└── main.ts       # 入口文件
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 服务器部署

### 开发环境部署

#### 1. 前台运行（适用于开发调试）

```bash
# 启动开发服务器，前台运行
npm run dev
```

服务将在 `http://localhost:3003` 启动，支持热重载。

#### 2. 后台运行（适用于服务器部署）

**使用 nohup 命令（Linux/macOS）：**

```bash
# 后台启动开发服务器
nohup npm run dev > app.log 2>&1 &

# 查看运行状态
ps aux | grep node

# 查看日志
tail -f app.log

# 停止服务
pkill -f "npm run dev"
```

**使用 PM2 进程管理器（推荐）：**

```bash
# 全局安装 PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "energy-management" -- run dev

# 查看应用状态
pm2 status

# 查看日志
pm2 logs energy-management

# 重启应用
pm2 restart energy-management

# 停止应用
pm2 stop energy-management

# 删除应用
pm2 delete energy-management

# 保存PM2配置（开机自启）
pm2 save
pm2 startup
```

**使用 screen 会话（Linux）：**

```bash
# 创建新的screen会话
screen -S energy-management

# 在screen会话中启动应用
npm run dev

# 分离会话（Ctrl+A, 然后按D）
# 重新连接会话
screen -r energy-management

# 查看所有会话
screen -ls

# 终止会话
screen -X -S energy-management quit
```

### 生产环境部署

#### 1. 构建并部署静态文件

```bash
# 构建生产版本
npm run build

# 构建完成后，dist目录包含所有静态文件
# 可以部署到任何静态文件服务器（Nginx、Apache等）
```

#### 2. 使用 Nginx 部署

**Nginx 配置示例：**

```nginx
server {
    listen 80;
    server_name your-domain.com;

    root /path/to/your/project/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

#### 3. 使用 Docker 部署

**创建 Dockerfile：**

```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

**构建和运行 Docker 容器：**

```bash
# 构建镜像
docker build -t energy-management .

# 运行容器
docker run -d -p 80:80 --name energy-management-app energy-management

# 后台运行并重启策略
docker run -d -p 80:80 --restart=always --name energy-management-app energy-management
```

### 网络配置

应用默认配置为：
- **端口**: 3003
- **主机**: 0.0.0.0（允许外部访问）
- **CORS**: 已启用

如需修改配置，请编辑 `vite.config.ts` 文件中的 `server` 配置项。

### 防火墙配置

确保服务器防火墙允许访问配置的端口：

```bash
# Ubuntu/Debian
sudo ufw allow 3003

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3003/tcp
sudo firewall-cmd --reload
```

### 监控和维护

#### 健康检查

```bash
# 检查服务是否运行
curl http://localhost:3003

# 检查进程
ps aux | grep node
netstat -tlnp | grep 3003
```

#### 日志管理

```bash
# 使用PM2查看日志
pm2 logs energy-management

# 使用nohup时查看日志
tail -f app.log

# 日志轮转（避免日志文件过大）
logrotate /etc/logrotate.d/energy-management
```

## 开发指南

- 组件开发遵循Vue 3 Composition API风格
- 使用TypeScript进行类型检查
- 使用Element Plus组件库构建UI
- 使用Pinia进行状态管理
- 使用Vue Router进行路由管理
