# 能源管理系统

基于Vue 3、TypeScript和Element Plus的能源管理系统前端框架。

## 技术栈

- Vue 3 (Composition API)
- TypeScript
- Vite 4
- Element Plus
- Vue Router
- Pinia (状态管理)

## 功能特性

- 响应式布局，适配主流浏览器
- 基于角色的权限控制
- 用户认证与授权
- 能源数据可视化
- 设备管理与监控

## 项目结构

```
src/
├── assets/       # 静态资源
│   └── images/   # 图片
├── components/   # 公共组件
├── views/        # 页面视图
├── store/        # 状态管理
├── types/        # TypeScript类型定义
├── styles/       # 全局样式
├── router/       # 路由配置
├── App.vue       # 根组件
└── main.ts       # 入口文件
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 开发指南

- 组件开发遵循Vue 3 Composition API风格
- 使用TypeScript进行类型检查
- 使用Element Plus组件库构建UI
- 使用Pinia进行状态管理
- 使用Vue Router进行路由管理
