<template>
  <div class="system-linkage" v-loading="loading" element-loading-text="正在加载系统数据...">
    <BreadCrumb :items="breadcrumbItems" />

    <!-- 系统状态总览 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="system-overview">
          <template #header>
            <div class="overview-header">
              <div class="header-left">
                <h2>多系统联动控制中心</h2>
                <div class="system-status" :class="overallStatus">
                  <el-icon class="status-icon">
                    <component :is="getStatusIcon(overallStatus)" />
                  </el-icon>
                  <span class="status-text">{{ getStatusText(overallStatus) }}</span>
                </div>
              </div>
              <div class="header-right">
                <div class="last-update">
                  最后更新：{{ lastUpdateTime }}
                </div>
                <el-button type="primary" size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新数据
                </el-button>
              </div>
            </div>
          </template>

          <div class="system-metrics">
            <div class="metric-item">
              <div class="metric-icon online">
                <el-icon><CircleCheckFilled /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ systemStats.online }}</div>
                <div class="metric-label">在线系统</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon linkage">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ systemStats.linkageRules }}</div>
                <div class="metric-label">联动规则</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon scenes">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ systemStats.activeScenes }}</div>
                <div class="metric-label">活跃场景</div>
              </div>
            </div>

            <div class="metric-divider"></div>

            <div class="metric-item">
              <div class="metric-icon executions">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ systemStats.todayExecutions }}</div>
                <div class="metric-label">今日执行</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态监控 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Monitor /></el-icon>
                系统状态监控
              </h3>
              <div class="header-controls">
                <el-button type="info" size="small" @click="toggleAutoRefresh">
                  <el-icon><Timer /></el-icon>
                  {{ autoRefresh ? '停止' : '开启' }}自动刷新
                </el-button>

              </div>
            </div>
          </template>

          <div class="system-grid">
            <div class="system-card" v-for="system in systems" :key="system.id">
              <div class="system-header">
                <div class="system-icon" :class="system.status">
                  <el-icon>
                    <component :is="getSystemIcon(system.type)" />
                  </el-icon>
                </div>
                <div class="system-info">
                  <div class="system-name">{{ system.name }}</div>
                  <div class="system-type">{{ system.type }}</div>
                </div>
                <div class="system-status-badge">
                  <el-tag :type="getSystemStatusType(system.status)" size="small">
                    {{ getSystemStatusText(system.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="system-details">
                <div class="detail-item">
                  <span class="detail-label">设备数量：</span>
                  <span class="detail-value">{{ system.deviceCount }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">在线率：</span>
                  <span class="detail-value success">{{ system.onlineRate }}%</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">最后通信：</span>
                  <span class="detail-value">{{ system.lastCommunication }}</span>
                </div>
              </div>
              <div class="system-actions">
                <el-button type="primary" size="small" @click="viewSystemDetail(system)">
                  详情
                </el-button>
                <el-button type="success" size="small" @click="configSystem(system)">
                  配置
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 场景模式管理 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Operation /></el-icon>
                场景模式
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showCreateSceneDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建场景
                </el-button>
              </div>
            </div>
          </template>

          <div class="scene-grid">
            <div class="scene-item" v-for="scene in scenes" :key="scene.id">
              <div class="scene-header">
                <div class="scene-icon" :class="scene.status">
                  <el-icon>
                    <component :is="getSceneIcon(scene.type)" />
                  </el-icon>
                </div>
                <div class="scene-info">
                  <div class="scene-name">{{ scene.name }}</div>
                  <div class="scene-description">{{ scene.description }}</div>
                </div>
              </div>
              <div class="scene-status">
                <el-tag :type="getSceneStatusType(scene.status)" size="small">
                  {{ getSceneStatusText(scene.status) }}
                </el-tag>
              </div>
              <div class="scene-actions">
                <el-button
                  :type="scene.status === 'active' ? 'warning' : 'success'"
                  size="small"
                  @click="toggleScene(scene)"
                >
                  {{ scene.status === 'active' ? '停用' : '启用' }}
                </el-button>
                <el-button type="primary" size="small" @click="editScene(scene)">
                  编辑
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 联动规则管理 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Connection /></el-icon>
                联动规则管理
              </h3>
              <div class="header-controls">
                <el-button type="primary" size="small" @click="showCreateRuleDialog = true">
                  <el-icon><Plus /></el-icon>
                  新建规则
                </el-button>
              </div>
            </div>
          </template>

          <el-table :data="linkageRules" border stripe style="width: 100%">
            <el-table-column prop="id" label="规则ID" width="80" align="center" />
            <el-table-column prop="name" label="规则名称" min-width="150" />
            <el-table-column label="触发系统" width="120" align="center">
              <template #default="{ row }">
                <el-tag type="info" size="small">{{ row.triggerSystem }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="目标系统" width="120" align="center">
              <template #default="{ row }">
                <el-tag type="success" size="small">{{ row.targetSystem }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getRuleStatusType(row.status)">
                  {{ getRuleStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="executionCount" label="执行次数" width="100" align="center" />
            <el-table-column prop="lastExecuted" label="最后执行" width="160" align="center" />
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="editRule(row)">编辑</el-button>
                <el-button
                  :type="row.status === 'enabled' ? 'warning' : 'success'"
                  size="small"
                  @click="toggleRule(row)"
                >
                  {{ row.status === 'enabled' ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="deleteRule(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统详情对话框 -->
    <el-dialog
      v-model="showSystemDetailDialog"
      :title="`系统详细信息 - ${selectedSystem?.name || ''}`"
      width="900px"
      :before-close="handleCloseSystemDetail"
      center
      destroy-on-close>
      <div v-if="selectedSystem" class="system-detail-content">
        <!-- 系统基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">系统名称：</span>
                <span class="info-value">{{ selectedSystem.name }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">系统类型：</span>
                <span class="info-value">{{ selectedSystem.type }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">运行状态：</span>
                <span class="info-value" :class="getSystemStatusClass(selectedSystem.status)">
                  {{ getSystemStatusText(selectedSystem.status) }}
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">设备数量：</span>
                <span class="info-value">{{ selectedSystem.deviceCount }}台</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">在线设备：</span>
                <span class="info-value">{{ selectedSystem.onlineDevices }}台</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">最后通信：</span>
                <span class="info-value">{{ selectedSystem.lastCommunication }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 系统性能指标 -->
        <div class="detail-section">
          <h4 class="section-title">性能指标</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="performance-card">
                <div class="performance-title">CPU使用率</div>
                <div class="performance-value">{{ systemPerformance.cpuUsage }}%</div>
                <el-progress :percentage="systemPerformance.cpuUsage" :color="getProgressColor(systemPerformance.cpuUsage)" />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="performance-card">
                <div class="performance-title">内存使用率</div>
                <div class="performance-value">{{ systemPerformance.memoryUsage }}%</div>
                <el-progress :percentage="systemPerformance.memoryUsage" :color="getProgressColor(systemPerformance.memoryUsage)" />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="performance-card">
                <div class="performance-title">网络延迟</div>
                <div class="performance-value">{{ systemPerformance.networkLatency }}ms</div>
                <div class="performance-status" :class="getLatencyClass(systemPerformance.networkLatency)">
                  {{ getLatencyText(systemPerformance.networkLatency) }}
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="performance-card">
                <div class="performance-title">响应时间</div>
                <div class="performance-value">{{ systemPerformance.responseTime }}ms</div>
                <div class="performance-status" :class="getResponseClass(systemPerformance.responseTime)">
                  {{ getResponseText(systemPerformance.responseTime) }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 联动规则 -->
        <div class="detail-section">
          <h4 class="section-title">联动规则</h4>
          <el-table :data="systemLinkageRules" border stripe style="width: 100%" max-height="200">
            <el-table-column prop="ruleName" label="规则名称" width="150" />
            <el-table-column prop="triggerCondition" label="触发条件" min-width="200" />
            <el-table-column prop="targetSystem" label="目标系统" width="120" />
            <el-table-column prop="action" label="执行动作" min-width="150" />
            <el-table-column label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'info'" size="small">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 历史操作记录 -->
        <div class="detail-section">
          <h4 class="section-title">历史操作记录</h4>
          <el-table :data="systemOperationHistory" border stripe style="width: 100%" max-height="200">
            <el-table-column prop="timestamp" label="时间" width="160" />
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="description" label="操作描述" min-width="200" />
            <el-table-column prop="operator" label="操作员" width="100" />
            <el-table-column label="结果" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.success ? 'success' : 'danger'" size="small">
                  {{ row.success ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSystemDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="openSystemConfigDialog">系统配置</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 系统配置对话框 -->
    <el-dialog
      v-model="showSystemConfigDialog"
      :title="`系统配置 - ${selectedSystem?.name || ''}`"
      width="700px"
      :before-close="handleCloseSystemConfig"
      center
      destroy-on-close>
      <el-form
        ref="systemConfigFormRef"
        :model="systemConfigForm"
        :rules="systemConfigRules"
        label-width="120px">
        <el-tabs v-model="activeConfigTab" type="border-card">
          <!-- 基本配置 -->
          <el-tab-pane label="基本配置" name="basic">
            <el-form-item label="系统名称：" prop="systemName">
              <el-input v-model="systemConfigForm.systemName" placeholder="请输入系统名称" />
            </el-form-item>

            <el-form-item label="系统描述：">
              <el-input
                v-model="systemConfigForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述" />
            </el-form-item>

            <el-form-item label="优先级：" prop="priority">
              <el-radio-group v-model="systemConfigForm.priority">
                <el-radio label="low">低</el-radio>
                <el-radio label="medium">中</el-radio>
                <el-radio label="high">高</el-radio>
                <el-radio label="critical">关键</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="自动启动：">
              <el-switch
                v-model="systemConfigForm.autoStart"
                active-text="开启"
                inactive-text="关闭" />
            </el-form-item>
          </el-tab-pane>

          <!-- 通信配置 -->
          <el-tab-pane label="通信配置" name="communication">
            <el-form-item label="通信协议：" prop="protocol">
              <el-select v-model="systemConfigForm.protocol" placeholder="请选择通信协议">
                <el-option label="TCP/IP" value="tcp" />
                <el-option label="UDP" value="udp" />
                <el-option label="HTTP/HTTPS" value="http" />
                <el-option label="MQTT" value="mqtt" />
                <el-option label="Modbus" value="modbus" />
              </el-select>
            </el-form-item>

            <el-form-item label="IP地址：" prop="ipAddress">
              <el-input v-model="systemConfigForm.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>

            <el-form-item label="端口号：" prop="port">
              <el-input-number
                v-model="systemConfigForm.port"
                :min="1"
                :max="65535"
                controls-position="right"
                style="width: 200px;" />
            </el-form-item>

            <el-form-item label="超时时间：">
              <el-input-number
                v-model="systemConfigForm.timeout"
                :min="1"
                :max="300"
                controls-position="right"
                style="width: 200px;" />
              <span style="margin-left: 10px;">秒</span>
            </el-form-item>

            <el-form-item label="重试次数：">
              <el-input-number
                v-model="systemConfigForm.retryCount"
                :min="0"
                :max="10"
                controls-position="right"
                style="width: 200px;" />
            </el-form-item>
          </el-tab-pane>

          <!-- 联动配置 -->
          <el-tab-pane label="联动配置" name="linkage">
            <el-form-item label="启用联动：">
              <el-switch
                v-model="systemConfigForm.linkageEnabled"
                active-text="开启"
                inactive-text="关闭" />
            </el-form-item>

            <el-form-item label="联动延迟：" v-if="systemConfigForm.linkageEnabled">
              <el-input-number
                v-model="systemConfigForm.linkageDelay"
                :min="0"
                :max="60"
                controls-position="right"
                style="width: 200px;" />
              <span style="margin-left: 10px;">秒</span>
            </el-form-item>

            <el-form-item label="联动系统：" v-if="systemConfigForm.linkageEnabled">
              <el-select
                v-model="systemConfigForm.linkageSystems"
                multiple
                placeholder="请选择联动系统">
                <el-option label="空调系统" value="hvac" />
                <el-option label="照明系统" value="lighting" />
                <el-option label="安防系统" value="security" />
                <el-option label="消防系统" value="fire" />
                <el-option label="电梯系统" value="elevator" />
              </el-select>
            </el-form-item>

            <el-form-item label="故障处理：">
              <el-radio-group v-model="systemConfigForm.faultHandling">
                <el-radio label="ignore">忽略</el-radio>
                <el-radio label="alert">告警</el-radio>
                <el-radio label="shutdown">停机</el-radio>
                <el-radio label="failover">故障转移</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSystemConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSystemConfig" :loading="savingConfig">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import {
  Plus,
  Refresh,
  CircleCheckFilled,
  Connection,
  Operation,
  Timer,
  Monitor,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  Setting
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '智能控制中心', to: '/intelligent-control' },
  { text: '多系统联动' }
])

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const overallStatus = ref('normal')
const lastUpdateTime = ref(new Date().toLocaleString('zh-CN'))
const showCreateRuleDialog = ref(false)
const showCreateSceneDialog = ref(false)

// 对话框相关数据
const showSystemDetailDialog = ref(false)
const showSystemConfigDialog = ref(false)
const selectedSystem = ref(null)
const savingConfig = ref(false)
const systemConfigFormRef = ref()
const activeConfigTab = ref('basic')

// 系统配置表单数据
const systemConfigForm = reactive({
  systemName: '',
  description: '',
  priority: 'medium',
  autoStart: true,
  protocol: 'tcp',
  ipAddress: '',
  port: 8080,
  timeout: 30,
  retryCount: 3,
  linkageEnabled: true,
  linkageDelay: 2,
  linkageSystems: [],
  faultHandling: 'alert'
})

// 表单验证规则
const systemConfigRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '系统名称长度在2到50个字符', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  protocol: [
    { required: true, message: '请选择通信协议', trigger: 'change' }
  ],
  ipAddress: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入正确的IP地址格式', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ]
}

// 系统性能数据
const systemPerformance = reactive({
  cpuUsage: 45,
  memoryUsage: 62,
  networkLatency: 25,
  responseTime: 150
})

// 系统联动规则
const systemLinkageRules = ref([
  {
    ruleName: '温度联动',
    triggerCondition: '温度 > 26°C',
    targetSystem: '空调系统',
    action: '启动制冷',
    enabled: true
  },
  {
    ruleName: '人员检测',
    triggerCondition: '检测到人员进入',
    targetSystem: '照明系统',
    action: '开启照明',
    enabled: true
  },
  {
    ruleName: '火警联动',
    triggerCondition: '检测到烟雾',
    targetSystem: '消防系统',
    action: '启动喷淋',
    enabled: true
  }
])

// 系统操作历史
const systemOperationHistory = ref([
  {
    timestamp: '2024-01-15 14:25:30',
    operation: '系统启动',
    description: '手动启动空调系统',
    operator: '管理员',
    success: true
  },
  {
    timestamp: '2024-01-15 14:20:15',
    operation: '参数调整',
    description: '调整温度设定值为24°C',
    operator: '张工程师',
    success: true
  },
  {
    timestamp: '2024-01-15 14:15:45',
    operation: '故障处理',
    description: '处理通信超时故障',
    operator: '李技师',
    success: false
  }
])

// 系统统计数据
const systemStats = reactive({
  online: 8,
  linkageRules: 12,
  activeScenes: 3,
  todayExecutions: 156
})

// 系统列表数据
const systems = ref([
  {
    id: 'SYS001',
    name: '智能照明系统',
    type: '照明控制',
    status: 'online',
    deviceCount: 45,
    onlineRate: 98,
    lastCommunication: '2024-01-15 14:25:30'
  },
  {
    id: 'SYS002',
    name: '空调控制系统',
    type: '环境控制',
    status: 'online',
    deviceCount: 12,
    onlineRate: 100,
    lastCommunication: '2024-01-15 14:25:28'
  },
  {
    id: 'SYS003',
    name: '安防监控系统',
    type: '安全监控',
    status: 'online',
    deviceCount: 28,
    onlineRate: 96,
    lastCommunication: '2024-01-15 14:25:25'
  },
  {
    id: 'SYS004',
    name: '消防报警系统',
    type: '安全报警',
    status: 'warning',
    deviceCount: 18,
    onlineRate: 89,
    lastCommunication: '2024-01-15 14:20:15'
  },
  {
    id: 'SYS005',
    name: '门禁控制系统',
    type: '出入控制',
    status: 'online',
    deviceCount: 24,
    onlineRate: 95,
    lastCommunication: '2024-01-15 14:25:22'
  },
  {
    id: 'SYS006',
    name: '电梯控制系统',
    type: '设备控制',
    status: 'offline',
    deviceCount: 6,
    onlineRate: 0,
    lastCommunication: '2024-01-15 13:45:10'
  }
])

// 联动规则数据
const linkageRules = ref([
  {
    id: 'RULE001',
    name: '夜间安防联动',
    triggerSystem: '门禁系统',
    targetSystem: '照明系统',
    status: 'enabled',
    executionCount: 45,
    lastExecuted: '2024-01-14 22:30:00'
  },
  {
    id: 'RULE002',
    name: '火警应急联动',
    triggerSystem: '消防系统',
    targetSystem: '电梯系统',
    status: 'enabled',
    executionCount: 2,
    lastExecuted: '2024-01-10 15:20:00'
  },
  {
    id: 'RULE003',
    name: '节能模式联动',
    triggerSystem: '时间调度',
    targetSystem: '空调系统',
    status: 'enabled',
    executionCount: 89,
    lastExecuted: '2024-01-15 18:00:00'
  },
  {
    id: 'RULE004',
    name: '人员检测联动',
    triggerSystem: '监控系统',
    targetSystem: '照明系统',
    status: 'disabled',
    executionCount: 156,
    lastExecuted: '2024-01-13 16:45:00'
  }
])

// 场景模式数据
const scenes = ref([
  {
    id: 'SCENE001',
    name: '工作模式',
    type: 'work',
    description: '正常工作时间的系统配置',
    status: 'active'
  },
  {
    id: 'SCENE002',
    name: '节能模式',
    type: 'energy',
    description: '夜间和节假日的节能配置',
    status: 'inactive'
  },
  {
    id: 'SCENE003',
    name: '安全模式',
    type: 'security',
    description: '紧急情况下的安全配置',
    status: 'inactive'
  },
  {
    id: 'SCENE004',
    name: '维护模式',
    type: 'maintenance',
    description: '系统维护时的特殊配置',
    status: 'inactive'
  }
])

// 工具函数
const getStatusIcon = (status: string) => {
  const iconMap = {
    'normal': SuccessFilled,
    'warning': WarningFilled,
    'danger': CircleCloseFilled
  }
  return iconMap[status] || SuccessFilled
}

const getStatusText = (status: string) => {
  const textMap = {
    'normal': '系统正常',
    'warning': '需要关注',
    'danger': '存在异常'
  }
  return textMap[status] || '未知状态'
}

const getSystemIcon = (type: string) => {
  const iconMap = {
    '照明控制': Setting,
    '环境控制': Setting,
    '安全监控': Monitor,
    '安全报警': WarningFilled,
    '出入控制': CircleCheckFilled,
    '设备控制': Operation
  }
  return iconMap[type] || Operation
}

const getSystemStatusType = (status: string) => {
  const typeMap = {
    'online': 'success',
    'warning': 'warning',
    'offline': 'danger'
  }
  return typeMap[status] || 'info'
}

const getSystemStatusText = (status: string) => {
  const textMap = {
    'online': '在线',
    'warning': '警告',
    'offline': '离线'
  }
  return textMap[status] || '未知'
}

const getSystemStatusClass = (status: string) => {
  const classMap = {
    'online': 'status-online',
    'warning': 'status-warning',
    'offline': 'status-offline'
  }
  return classMap[status] || 'status-unknown'
}

const getRuleStatusType = (status: string) => {
  const typeMap = {
    'enabled': 'success',
    'disabled': 'info'
  }
  return typeMap[status] || 'info'
}

const getRuleStatusText = (status: string) => {
  const textMap = {
    'enabled': '启用',
    'disabled': '禁用'
  }
  return textMap[status] || '未知'
}

const getSceneIcon = (type: string) => {
  const iconMap = {
    'work': Operation,
    'energy': SuccessFilled,
    'security': WarningFilled,
    'maintenance': Setting
  }
  return iconMap[type] || Operation
}

const getSceneStatusType = (status: string) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'info'
  }
  return typeMap[status] || 'info'
}

const getSceneStatusText = (status: string) => {
  const textMap = {
    'active': '活跃',
    'inactive': '未激活'
  }
  return textMap[status] || '未知'
}

// 事件处理函数
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    lastUpdateTime.value = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  ElMessage.success(`自动刷新已${autoRefresh.value ? '开启' : '关闭'}`)
}

const viewSystemDetail = (system: any) => {
  console.log('查看系统详情:', system) // 调试信息
  selectedSystem.value = system
  // 根据系统类型更新性能数据
  updateSystemPerformance(system)
  showSystemDetailDialog.value = true
  ElMessage.success(`正在查看 ${system.name} 的详细信息`)
}

const configSystem = (system: any) => {
  console.log('配置系统:', system) // 调试信息
  selectedSystem.value = system
  // 重置配置表单
  resetSystemConfigForm()
  // 预填系统信息
  systemConfigForm.systemName = system.name
  systemConfigForm.description = `${system.type}系统配置`
  ElMessage.info(`正在配置 ${system.name} 系统`)

  // 根据系统类型设置默认配置
  switch (system.type) {
    case '照明控制':
      systemConfigForm.protocol = 'modbus'
      systemConfigForm.port = 502
      systemConfigForm.linkageSystems = ['security']
      break
    case '空调控制':
      systemConfigForm.protocol = 'tcp'
      systemConfigForm.port = 8080
      systemConfigForm.linkageSystems = ['lighting']
      break
    case '安防监控':
      systemConfigForm.protocol = 'http'
      systemConfigForm.port = 80
      systemConfigForm.linkageSystems = ['lighting', 'fire']
      break
    case '消防系统':
      systemConfigForm.protocol = 'tcp'
      systemConfigForm.port = 9999
      systemConfigForm.priority = 'critical'
      systemConfigForm.faultHandling = 'shutdown'
      break
    default:
      systemConfigForm.protocol = 'tcp'
      systemConfigForm.port = 8080
  }

  showSystemConfigDialog.value = true
}

// 更新系统性能数据
const updateSystemPerformance = (system: any) => {
  // 根据系统状态模拟性能数据
  if (system.status === 'online') {
    systemPerformance.cpuUsage = Math.floor(Math.random() * 30) + 30 // 30-60%
    systemPerformance.memoryUsage = Math.floor(Math.random() * 40) + 40 // 40-80%
    systemPerformance.networkLatency = Math.floor(Math.random() * 20) + 10 // 10-30ms
    systemPerformance.responseTime = Math.floor(Math.random() * 100) + 50 // 50-150ms
  } else if (system.status === 'warning') {
    systemPerformance.cpuUsage = Math.floor(Math.random() * 20) + 70 // 70-90%
    systemPerformance.memoryUsage = Math.floor(Math.random() * 20) + 75 // 75-95%
    systemPerformance.networkLatency = Math.floor(Math.random() * 30) + 40 // 40-70ms
    systemPerformance.responseTime = Math.floor(Math.random() * 200) + 200 // 200-400ms
  } else {
    systemPerformance.cpuUsage = 0
    systemPerformance.memoryUsage = 0
    systemPerformance.networkLatency = 999
    systemPerformance.responseTime = 999
  }
}

// 重置系统配置表单
const resetSystemConfigForm = () => {
  systemConfigForm.systemName = ''
  systemConfigForm.description = ''
  systemConfigForm.priority = 'medium'
  systemConfigForm.autoStart = true
  systemConfigForm.protocol = 'tcp'
  systemConfigForm.ipAddress = ''
  systemConfigForm.port = 8080
  systemConfigForm.timeout = 30
  systemConfigForm.retryCount = 3
  systemConfigForm.linkageEnabled = true
  systemConfigForm.linkageDelay = 2
  systemConfigForm.linkageSystems = []
  systemConfigForm.faultHandling = 'alert'
}

// 保存系统配置
const saveSystemConfig = async () => {
  if (!systemConfigFormRef.value) return

  try {
    await systemConfigFormRef.value.validate()
    savingConfig.value = true

    // 模拟保存过程
    setTimeout(() => {
      savingConfig.value = false
      showSystemConfigDialog.value = false

      // 更新系统信息
      if (selectedSystem.value) {
        selectedSystem.value.name = systemConfigForm.systemName
        // 如果配置了高优先级，提升系统状态
        if (systemConfigForm.priority === 'critical' || systemConfigForm.priority === 'high') {
          if (selectedSystem.value.status === 'warning') {
            selectedSystem.value.status = 'online'
          }
        }
      }

      ElMessage.success(`系统配置已保存！系统：${systemConfigForm.systemName}`)

      // 重置表单
      resetSystemConfigForm()
    }, 1500)
  } catch (error) {
    ElMessage.error('请完善配置信息')
  }
}

// 打开系统配置对话框
const openSystemConfigDialog = () => {
  showSystemDetailDialog.value = false
  setTimeout(() => {
    configSystem(selectedSystem.value)
  }, 100)
}

// 关闭系统详情对话框
const handleCloseSystemDetail = () => {
  showSystemDetailDialog.value = false
  selectedSystem.value = null
}

// 关闭系统配置对话框
const handleCloseSystemConfig = () => {
  showSystemConfigDialog.value = false
  resetSystemConfigForm()
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 获取延迟状态类
const getLatencyClass = (latency: number) => {
  if (latency < 30) return 'status-good'
  if (latency < 100) return 'status-warning'
  return 'status-danger'
}

// 获取延迟状态文本
const getLatencyText = (latency: number) => {
  if (latency < 30) return '优秀'
  if (latency < 100) return '良好'
  return '较差'
}

// 获取响应时间状态类
const getResponseClass = (responseTime: number) => {
  if (responseTime < 100) return 'status-good'
  if (responseTime < 300) return 'status-warning'
  return 'status-danger'
}

// 获取响应时间状态文本
const getResponseText = (responseTime: number) => {
  if (responseTime < 100) return '快速'
  if (responseTime < 300) return '正常'
  return '缓慢'
}







const editRule = (rule: any) => {
  ElMessage.info(`编辑规则: ${rule.name}`)
}

const toggleRule = (rule: any) => {
  rule.status = rule.status === 'enabled' ? 'disabled' : 'enabled'
  ElMessage.success(`规则 ${rule.name} 已${rule.status === 'enabled' ? '启用' : '禁用'}`)
}

const deleteRule = (rule: any) => {
  ElMessageBox.confirm(
    `确定要删除规则 "${rule.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const editScene = (scene: any) => {
  ElMessage.info(`编辑场景: ${scene.name}`)
}

const toggleScene = (scene: any) => {
  // 如果要激活场景，先停用其他场景
  if (scene.status === 'inactive') {
    scenes.value.forEach(s => {
      if (s.id !== scene.id) {
        s.status = 'inactive'
      }
    })
  }
  scene.status = scene.status === 'active' ? 'inactive' : 'active'
  ElMessage.success(`场景 ${scene.name} 已${scene.status === 'active' ? '激活' : '停用'}`)
}
</script>

<style scoped>
.system-linkage {
  padding: 0;
}

/* 系统总览样式 */
.system-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.system-overview :deep(.el-card__header) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
}

.system-overview :deep(.el-card__body) {
  background: transparent;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.system-status.normal .status-icon {
  color: #67c23a;
}

.system-status.warning .status-icon {
  color: #e6a23c;
}

.system-status.danger .status-icon {
  color: #f56c6c;
}

.status-icon {
  font-size: 20px;
}

.header-right {
  text-align: right;
}

.last-update {
  margin-bottom: 10px;
  opacity: 0.8;
  font-size: 14px;
}

.system-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.metric-icon.online {
  background: rgba(103, 194, 58, 0.2);
  color: #67c23a;
  border: 2px solid #67c23a;
}

.metric-icon.linkage {
  background: rgba(64, 158, 255, 0.2);
  color: #409eff;
  border: 2px solid #409eff;
}

.metric-icon.scenes {
  background: rgba(230, 162, 60, 0.2);
  color: #e6a23c;
  border: 2px solid #e6a23c;
}

.metric-icon.executions {
  background: rgba(144, 147, 153, 0.2);
  color: #909399;
  border: 2px solid #909399;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-divider {
  width: 1px;
  height: 60px;
  background: rgba(255, 255, 255, 0.3);
}

/* 系统监控样式 */
.system-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.system-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.system-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.system-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.system-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.system-icon.online {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.system-icon.warning {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.system-icon.offline {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.system-info {
  flex: 1;
}

.system-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.system-type {
  font-size: 12px;
  color: #909399;
}

.system-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-label {
  color: #606266;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.detail-value.success {
  color: #67c23a;
}

.system-actions {
  display: flex;
  gap: 8px;
}

.system-actions .el-button {
  flex: 1;
}

/* 场景模式样式 */
.scene-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.scene-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.scene-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.scene-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.scene-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.scene-icon.active {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.scene-icon.inactive {
  background: linear-gradient(135deg, #909399, #b1b3b8);
}

.scene-info {
  flex: 1;
}

.scene-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.scene-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.scene-status {
  margin-bottom: 12px;
}

.scene-actions {
  display: flex;
  gap: 8px;
}

.scene-actions .el-button {
  flex: 1;
}

/* 通用样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-metrics {
    flex-direction: column;
    gap: 20px;
  }

  .metric-divider {
    display: none;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .system-grid {
    grid-template-columns: 1fr;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .header-controls .el-button {
    width: 100%;
  }
}

/* 系统详情对话框样式 */
.system-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #409eff;
  margin-right: 8px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.info-value.status-online {
  color: #67c23a;
}

.info-value.status-warning {
  color: #e6a23c;
}

.info-value.status-offline {
  color: #f56c6c;
}

.performance-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.performance-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.performance-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.performance-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.performance-status.status-good {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.performance-status.status-warning {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.performance-status.status-danger {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

/* 系统配置表单样式 */
.el-tabs--border-card {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-radio-group .el-radio {
  margin-right: 20px;
}

.el-select {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-detail-content {
    max-height: 400px;
  }

  .performance-card {
    margin-bottom: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 4px;
    min-width: auto;
  }

  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .el-tabs__content {
    padding: 10px;
  }
}


</style>
