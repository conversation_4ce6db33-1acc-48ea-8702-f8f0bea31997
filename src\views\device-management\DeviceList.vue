<template>
  <div class="device-list" v-loading="loading" element-loading-text="正在加载设备列表...">
    <BreadCrumb :items="breadcrumbItems" />
    
    <!-- 设备管理操作 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>设备管理</h3>
          <div class="header-controls">
            <el-select v-model="selectedBuilding" size="small" placeholder="选择建筑" style="width: 120px;" @change="handleBuildingChange">
              <el-option label="全部建筑" value="" />
              <el-option label="办公大楼A" value="building-a" />
              <el-option label="办公大楼B" value="building-b" />
              <el-option label="生产车间" value="workshop" />
              <el-option label="配套设施" value="facilities" />
            </el-select>
            <el-button type="primary" size="small" @click="addDevice">
              <el-icon><Plus /></el-icon>
              添加设备
            </el-button>
            <el-button type="success" size="small" @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button type="warning" size="small" @click="batchOperation">
              <el-icon><Operation /></el-icon>
              批量操作
            </el-button>
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="filter-item">
            <label>设备类型：</label>
            <el-select v-model="filters.deviceType" placeholder="请选择设备类型" size="small" clearable @change="handleFilterChange">
              <el-option label="全部类型" value="" />
              <el-option label="计量设备" value="meter" />
              <el-option label="控制设备" value="controller" />
              <el-option label="传感器" value="sensor" />
              <el-option label="执行器" value="actuator" />
              <el-option label="光伏设备" value="solar" />
              <el-option label="储能设备" value="storage" />
              <el-option label="充电桩" value="charger" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>设备状态：</label>
            <el-select v-model="filters.status" placeholder="请选择设备状态" size="small" clearable @change="handleFilterChange">
              <el-option label="全部状态" value="" />
              <el-option label="正常" value="normal" />
              <el-option label="异常" value="abnormal" />
              <el-option label="离线" value="offline" />
              <el-option label="维护中" value="maintenance" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>通信协议：</label>
            <el-select v-model="filters.protocol" placeholder="请选择通信协议" size="small" clearable @change="handleFilterChange">
              <el-option label="全部协议" value="" />
              <el-option label="Modbus" value="modbus" />
              <el-option label="BACnet" value="bacnet" />
              <el-option label="MQTT" value="mqtt" />
              <el-option label="OPC UA" value="opcua" />
              <el-option label="HTTP" value="http" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :span="6">
          <div class="filter-item">
            <label>关键字搜索：</label>
            <el-input 
              v-model="filters.keyword" 
              placeholder="设备名称、编号" 
              size="small" 
              clearable
              @input="handleKeywordSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 设备统计概览 -->
    <el-card class="overview-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>设备统计概览</h3>
          <div class="header-controls">
            <el-button type="primary" size="small" @click="exportDevices">
              <el-icon><Download /></el-icon>
              导出设备
            </el-button>
            <el-button type="warning" size="small" @click="importDevices">
              <el-icon><Upload /></el-icon>
              导入设备
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="stats-overview">
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(64, 158, 255, 0.2); border: 2px solid #409eff;">
            <el-icon style="color: #409eff; font-size: 28px;"><Box /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">设备总数</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ deviceStats.total }}</span>
              <span class="stats-unit">台</span>
            </div>
            <div class="stats-trend trend-up">较昨日 +3</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(103, 194, 58, 0.2); border: 2px solid #67c23a;">
            <el-icon style="color: #67c23a; font-size: 28px;"><SuccessFilled /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">正常设备</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ deviceStats.normal }}</span>
              <span class="stats-unit">台</span>
            </div>
            <div class="stats-trend trend-down">较昨日 -1</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(245, 108, 108, 0.2); border: 2px solid #f56c6c;">
            <el-icon style="color: #f56c6c; font-size: 28px;"><WarningFilled /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">异常设备</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ deviceStats.abnormal }}</span>
              <span class="stats-unit">台</span>
            </div>
            <div class="stats-trend trend-up">较昨日 +2</div>
          </div>
        </div>
        
        <div class="stats-divider"></div>
        
        <div class="stats-item">
          <div class="stats-icon" style="background-color: rgba(144, 147, 153, 0.2); border: 2px solid #909399;">
            <el-icon style="color: #909399; font-size: 28px;"><SwitchButton /></el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-name">离线设备</div>
            <div class="stats-value-row">
              <span class="stats-value">{{ deviceStats.offline }}</span>
              <span class="stats-unit">台</span>
            </div>
            <div class="stats-trend trend-up">较昨日 +2</div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 数据可视化 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>设备类型分布</h4>
          </template>
          <div ref="deviceTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>设备状态趋势</h4>
          </template>
          <div ref="deviceStatusChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备列表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>设备列表</h3>
          <div class="header-controls">
            <el-button type="danger" size="small" @click="batchDelete" :disabled="selectedDevices.length === 0">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedDevices.length }})
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="deviceList" 
        border 
        stripe 
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="deviceId" label="设备编号" width="120" align="center" />
        <el-table-column prop="deviceName" label="设备名称" min-width="150" align="center" />
        <el-table-column label="设备类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getDeviceTypeTagType(row.deviceType)">
              {{ getDeviceTypeName(row.deviceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="安装位置" min-width="120" align="center" />
        <el-table-column label="设备状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="protocol" label="通信协议" width="100" align="center" />
        <el-table-column prop="ipAddress" label="IP地址" width="130" align="center" />
        <el-table-column prop="lastOnlineTime" label="最后在线时间" width="160" align="center" />
        <el-table-column prop="maintainer" label="维护人员" width="100" align="center" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div style="white-space: nowrap;">
              <el-button type="primary" size="small" @click="editDevice(row)" style="margin-right: 5px;">
                编辑
              </el-button>
              <el-button type="info" size="small" @click="viewDevice(row)" style="margin-right: 5px;">
                查看
              </el-button>
              <el-button type="danger" size="small" @click="deleteDevice(row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalDevices"
        layout="total, sizes, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加/编辑设备对话框 -->
    <el-dialog
      v-model="showDeviceDialog"
      :title="isEdit ? '编辑设备' : '添加设备'"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="deviceForm" :rules="deviceRules" ref="deviceFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备编号" prop="deviceId">
              <el-input v-model="deviceForm.deviceId" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="deviceForm.deviceName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="deviceForm.deviceType" placeholder="请选择设备类型" style="width: 100%">
                <el-option label="计量设备" value="meter" />
                <el-option label="控制设备" value="controller" />
                <el-option label="传感器" value="sensor" />
                <el-option label="执行器" value="actuator" />
                <el-option label="光伏设备" value="solar" />
                <el-option label="储能设备" value="storage" />
                <el-option label="充电桩" value="charger" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安装位置" prop="location">
              <el-input v-model="deviceForm.location" placeholder="请输入安装位置" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="通信协议" prop="protocol">
              <el-select v-model="deviceForm.protocol" placeholder="请选择通信协议" style="width: 100%">
                <el-option label="Modbus" value="modbus" />
                <el-option label="BACnet" value="bacnet" />
                <el-option label="MQTT" value="mqtt" />
                <el-option label="OPC UA" value="opcua" />
                <el-option label="HTTP" value="http" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input v-model="deviceForm.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维护人员" prop="maintainer">
              <el-input v-model="deviceForm.maintainer" placeholder="请输入维护人员" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备状态" prop="status">
              <el-select v-model="deviceForm.status" placeholder="请选择设备状态" style="width: 100%">
                <el-option label="正常" value="normal" />
                <el-option label="异常" value="abnormal" />
                <el-option label="离线" value="offline" />
                <el-option label="维护中" value="maintenance" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="设备描述">
          <el-input 
            v-model="deviceForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入设备描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showDeviceDialog = false">取消</el-button>
        <el-button type="primary" @click="saveDevice">保存</el-button>
      </template>
    </el-dialog>

    <!-- 设备详情对话框 -->
    <el-dialog
      v-model="showDeviceDetailDialog"
      title="设备详情"
      width="50%"
    >
      <div v-if="selectedDevice">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备编号">{{ selectedDevice.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ selectedDevice.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ getDeviceTypeName(selectedDevice.deviceType) }}</el-descriptions-item>
          <el-descriptions-item label="安装位置">{{ selectedDevice.location }}</el-descriptions-item>
          <el-descriptions-item label="通信协议">{{ selectedDevice.protocol }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedDevice.ipAddress }}</el-descriptions-item>
          <el-descriptions-item label="设备状态">
            <el-tag :type="getStatusTagType(selectedDevice.status)">
              {{ getStatusName(selectedDevice.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="维护人员">{{ selectedDevice.maintainer }}</el-descriptions-item>
          <el-descriptions-item label="最后在线时间" :span="2">{{ selectedDevice.lastOnlineTime }}</el-descriptions-item>
          <el-descriptions-item label="设备描述" :span="2">{{ selectedDevice.description || '暂无描述' }}</el-descriptions-item>
        </el-descriptions>
        
        <div style="margin-top: 20px;">
          <h4>实时数据</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="功率" :value="selectedDevice.power || 0" suffix="kW" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="电压" :value="selectedDevice.voltage || 0" suffix="V" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="电流" :value="selectedDevice.current || 0" suffix="A" />
            </el-col>
          </el-row>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDeviceDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="editDevice(selectedDevice)">编辑设备</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BreadCrumb from '@/components/BreadCrumb.vue'
import * as echarts from 'echarts'
import {
  Plus,
  Refresh,
  Operation,
  Search,
  Download,
  Upload,
  Delete,
  Box,
  SuccessFilled,
  WarningFilled,
  SwitchButton
} from '@element-plus/icons-vue'

// 面包屑导航数据
const breadcrumbItems = ref([
  { text: '设备资产管理', to: '/device-management' },
  { text: '设备列表' }
])

// 响应式数据
const loading = ref(false)
const selectedBuilding = ref('')
const deviceTypeChart = ref()
const deviceStatusChart = ref()

// 筛选条件
const filters = reactive({
  deviceType: '',
  status: '',
  protocol: '',
  keyword: ''
})

// 设备统计数据
const deviceStats = reactive({
  total: 142,
  normal: 128,
  abnormal: 8,
  offline: 6
})

// 设备列表数据
const deviceList = ref([
  {
    deviceId: 'DEV001',
    deviceName: '主配电柜电表',
    deviceType: 'meter',
    location: '配电房-01',
    status: 'normal',
    protocol: 'Modbus',
    ipAddress: '*************',
    lastOnlineTime: '2024-01-15 14:30:25',
    maintainer: '张工程师',
    description: '主配电柜智能电表，用于监测总进线电能',
    power: 125.6,
    voltage: 380,
    current: 196.8
  },
  {
    deviceId: 'DEV002',
    deviceName: '空调控制器',
    deviceType: 'controller',
    location: '办公区-A座',
    status: 'normal',
    protocol: 'BACnet',
    ipAddress: '*************',
    lastOnlineTime: '2024-01-15 14:28:15',
    maintainer: '李技术员',
    description: '中央空调控制系统，控制办公区域温度',
    power: 45.2,
    voltage: 220,
    current: 205.4
  },
  {
    deviceId: 'DEV003',
    deviceName: '温湿度传感器',
    deviceType: 'sensor',
    location: '会议室-201',
    status: 'abnormal',
    protocol: 'MQTT',
    ipAddress: '*************',
    lastOnlineTime: '2024-01-15 12:45:30',
    maintainer: '王主管',
    description: '环境监测传感器，实时监测温湿度',
    power: 0.5,
    voltage: 24,
    current: 0.02
  },
  {
    deviceId: 'DEV004',
    deviceName: '光伏逆变器',
    deviceType: 'solar',
    location: '屋顶-东区',
    status: 'normal',
    protocol: 'Modbus',
    ipAddress: '*************',
    lastOnlineTime: '2024-01-15 14:29:45',
    maintainer: '刘管理员',
    description: '光伏发电系统逆变器，将直流电转换为交流电',
    power: 89.3,
    voltage: 380,
    current: 135.6
  },
  {
    deviceId: 'DEV005',
    deviceName: '储能电池管理系统',
    deviceType: 'storage',
    location: '储能室-B1',
    status: 'normal',
    protocol: 'OPC UA',
    ipAddress: '*************',
    lastOnlineTime: '2024-01-15 14:31:20',
    maintainer: '陈工程师',
    description: '储能电池管理与监控系统',
    power: 156.8,
    voltage: 800,
    current: 196.0
  }
])

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const totalDevices = ref(142)

// 选中的设备
const selectedDevices = ref([])

// 对话框控制
const showDeviceDialog = ref(false)
const showDeviceDetailDialog = ref(false)
const isEdit = ref(false)
const selectedDevice = ref(null)

// 设备表单
const deviceForm = reactive({
  deviceId: '',
  deviceName: '',
  deviceType: '',
  location: '',
  protocol: '',
  ipAddress: '',
  maintainer: '',
  status: 'normal',
  description: ''
})

// 表单验证规则
const deviceRules = {
  deviceId: [
    { required: true, message: '请输入设备编号', trigger: 'blur' }
  ],
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  location: [
    { required: true, message: '请输入安装位置', trigger: 'blur' }
  ],
  protocol: [
    { required: true, message: '请选择通信协议', trigger: 'change' }
  ],
  ipAddress: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  maintainer: [
    { required: true, message: '请输入维护人员', trigger: 'blur' }
  ]
}

const deviceFormRef = ref()

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 设备类型分布图
    if (deviceTypeChart.value) {
      const typeChart = echarts.init(deviceTypeChart.value)
      const typeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '0%'
        },
        series: [
          {
            name: '设备类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 35, name: '计量设备' },
              { value: 28, name: '控制设备' },
              { value: 25, name: '传感器' },
              { value: 20, name: '光伏设备' },
              { value: 18, name: '储能设备' },
              { value: 16, name: '充电桩' }
            ]
          }
        ]
      }
      typeChart.setOption(typeOption)
    }

    // 设备状态趋势图
    if (deviceStatusChart.value) {
      const statusChart = echarts.init(deviceStatusChart.value)
      const statusOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['正常', '异常', '离线']
        },
        xAxis: {
          type: 'category',
          data: ['01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '正常',
            type: 'line',
            stack: 'Total',
            areaStyle: { opacity: 0.3 },
            data: [120, 122, 125, 124, 126, 127, 128]
          },
          {
            name: '异常',
            type: 'line',
            stack: 'Total',
            areaStyle: { opacity: 0.3 },
            data: [5, 6, 4, 7, 6, 7, 8]
          },
          {
            name: '离线',
            type: 'line',
            stack: 'Total',
            areaStyle: { opacity: 0.3 },
            data: [3, 4, 3, 3, 4, 4, 6]
          }
        ]
      }
      statusChart.setOption(statusOption)
    }
  })
}

// 获取设备类型标签类型
const getDeviceTypeTagType = (type: string) => {
  const typeMap = {
    meter: 'primary',
    controller: 'success',
    sensor: 'info',
    actuator: 'warning',
    solar: 'danger',
    storage: '',
    charger: 'warning'
  }
  return typeMap[type] || ''
}

// 获取设备类型名称
const getDeviceTypeName = (type: string) => {
  const nameMap = {
    meter: '计量设备',
    controller: '控制设备',
    sensor: '传感器',
    actuator: '执行器',
    solar: '光伏设备',
    storage: '储能设备',
    charger: '充电桩'
  }
  return nameMap[type] || type
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap = {
    normal: 'success',
    abnormal: 'danger',
    offline: 'info',
    maintenance: 'warning'
  }
  return statusMap[status] || ''
}

// 获取状态名称
const getStatusName = (status: string) => {
  const nameMap = {
    normal: '正常',
    abnormal: '异常',
    offline: '离线',
    maintenance: '维护中'
  }
  return nameMap[status] || status
}

// 事件处理函数
const handleBuildingChange = () => {
  console.log('建筑筛选变更:', selectedBuilding.value)
  // 实现建筑筛选逻辑
}

const handleFilterChange = () => {
  console.log('筛选条件变更:', filters)
  // 实现筛选逻辑
}

const handleKeywordSearch = () => {
  console.log('关键字搜索:', filters.keyword)
  // 实现搜索逻辑
}

const handleSelectionChange = (selection) => {
  selectedDevices.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  // 重新加载数据
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  // 重新加载数据
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

const addDevice = () => {
  isEdit.value = false
  Object.assign(deviceForm, {
    deviceId: '',
    deviceName: '',
    deviceType: '',
    location: '',
    protocol: '',
    ipAddress: '',
    maintainer: '',
    status: 'normal',
    description: ''
  })
  showDeviceDialog.value = true
}

const editDevice = (device) => {
  isEdit.value = true
  Object.assign(deviceForm, device)
  showDeviceDetailDialog.value = false
  showDeviceDialog.value = true
}

const viewDevice = (device) => {
  selectedDevice.value = device
  showDeviceDetailDialog.value = true
}

const saveDevice = () => {
  deviceFormRef.value?.validate((valid) => {
    if (valid) {
      // 保存设备逻辑
      ElMessage.success(isEdit.value ? '设备更新成功' : '设备添加成功')
      showDeviceDialog.value = false
    }
  })
}

const deleteDevice = (device) => {
  ElMessageBox.confirm(
    `确定要删除设备 "${device.deviceName}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('设备删除成功')
  }).catch(() => {
    // 取消删除
  })
}

const batchOperation = () => {
  ElMessage.info('批量操作功能开发中')
}

const batchDelete = () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择要删除的设备')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedDevices.value.length} 个设备吗？`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success(`成功删除 ${selectedDevices.value.length} 个设备`)
    selectedDevices.value = []
  }).catch(() => {
    // 取消删除
  })
}

const exportDevices = () => {
  ElMessage.success('设备数据导出成功')
}

const importDevices = () => {
  ElMessage.info('设备数据导入功能开发中')
}

// 组件挂载
onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.device-list {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.filter-item label {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  min-width: 80px;
}

.stats-overview {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 30px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-item {
  display: flex;
  align-items: center;
  min-width: 180px;
  padding: 0 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.stats-info {
  flex: 1;
}

.stats-name {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.stats-value-row {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
}

.stats-unit {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.stats-trend {
  font-size: 11px;
  font-weight: 500;
}

.trend-up {
  color: #f56c6c;
}

.trend-down {
  color: #67c23a;
}

.stats-divider {
  width: 1px;
  height: 60%;
  background-color: #ebeef5;
  margin: 0 15px;
}

.overview-card :deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .stats-overview {
    flex-direction: column;
    gap: 20px;
  }
  
  .stats-divider {
    display: none;
  }
  
  .stats-item {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
  }
}
</style> 